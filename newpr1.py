#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Skript pro krátkodobou predikci ceny kryptoměny ALCHUSDT (5 minutový horizont).
Stahuje data z Binance, ukládá do SQL Serveru, trénuje XGBoost model 
(s využitím historie predikčních chyb) a generuje predikci.

*** DŮLEŽITÉ UPOZORNĚNÍ ***
Predikce cen kryptoměn je vysoce spekulativní a riziková.
Tento skript je pouze pro vzdělávací účely.
Výsledky nemusí být přesné a model má svá omezení.
NEPOUŽÍVEJTE TENTO SKRIPT PRO REÁLNÉ OBCHODOVÁNÍ BEZ DŮKLADNÉ ANALÝZY RIZIK!
Toto není finanční poradenství.
"""

import os
import sys
import time
import pyodbc
import pandas as pd
import numpy as np # oprava importu numpy
from dotenv import load_dotenv
from datetime import datetime, timedelta, timezone
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
import xgboost as xgb
import ccxt
import traceback
import pandas_ta as ta

# ANSI color codes for output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

# --- 1. Nastavení a Importy ---
load_dotenv()

# Globální konstanty
SYMBOL = 'ALCHUSDT'
SYMBOL_DB = 'ALCHUSDT'
TIMEFRAME = '1m'
PREDICTION_HORIZON = 10 # Predikce na 10 minut dopředu
DB_TABLE_NAME = f'{SYMBOL_DB}_OHLCV_{TIMEFRAME}'
BINANCE_API_LIMIT = 1000
RATE_LIMIT_DELAY = 0.5
N_SPLITS_CV = 5
LAG_FEATURES_LIST = [1, 2, 3, 4, 5]
ERROR_LAG_FEATURES_LIST = [1, 2, 3] # Jak daleko do historie chyb se dívat
ERROR_ROLLING_WINDOW = 5 # Velikost okna pro klouzavý průměr chyby

XGB_PARAMS = {
    'objective': 'reg:squarederror',
    'n_estimators': 300,           # Zvýšeno pro lepší učení
    'learning_rate': 0.15,         # Zvýšeno pro dynamičtější reakce
    'max_depth': 6,                # Zvýšeno pro komplexnější vztahy
    'subsample': 0.8,              # Sníženo pro lepší generalizaci
    'colsample_bytree': 0.8,       # Sníženo pro lepší generalizaci
    'min_child_weight': 1,         # Ponecháno pro citlivost
    'gamma': 0.01,                 # Ponecháno nízké pro citlivost
    'reg_alpha': 0.05,             # Mírně zvýšeno
    'reg_lambda': 0.5,             # Sníženo pro větší variabilitu
    'random_state': 42,
    'n_jobs': -1
}

# --- 2. Databázové Funkce ---
conn_str = f"DRIVER={{SQL Server}};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"
conn_str2 = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

def connect_db(conn_str):
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        print(f"Chyba připojení k DB: {e}")
        return None

def create_ohlcv_table_if_not_exists(table_name: str, conn: pyodbc.Connection):
    """Vytvoří tabulku pro OHLCV data, pokud neexistuje."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute(f"""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                       WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = ?)
        BEGIN
            CREATE TABLE {table_name} (
                timestamp_utc DATETIME2 PRIMARY KEY,
                open_price FLOAT,
                high_price FLOAT,
                low_price FLOAT,
                close_price FLOAT,
                volume_amt FLOAT
            );
            PRINT 'Tabulka {table_name} byla úspěšně vytvořena.';
        END
        """, (table_name,))
        conn.commit()
        print(f"Ověřeno/vytvořeno: Tabulka '{table_name}'.")
    except pyodbc.Error as e:
        print(f"Chyba při vytváření/ověřování tabulky '{table_name}': {e}")
        if conn: conn.rollback() # Rollback v případě chyby
    finally:
        if cursor:
            cursor.close()

def create_predictions_table(conn: pyodbc.Connection):
    """Vytvoří tabulku pro ukládání predikcí."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                       WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'PREDICTIONS')
        BEGIN
            CREATE TABLE PREDICTIONS (
                prediction_id INT IDENTITY(1,1) PRIMARY KEY,
                symbol VARCHAR(20),
                prediction_time DATETIME2, -- Čas, kdy byla predikce VYTVOŘENA
                target_time DATETIME2,     -- Čas, PRO KTERÝ predikce platí
                predicted_price FLOAT,
                actual_price FLOAT NULL,
                prediction_error FLOAT NULL, -- Absolutní chyba
                prediction_error_pct FLOAT NULL, -- Procentuální chyba
                horizon_minutes INT,
                -- Index pro rychlejší vyhledávání
                INDEX idx_pred_target_time (target_time),
                INDEX idx_pred_prediction_time (prediction_time)
            );
            PRINT 'Tabulka PREDICTIONS byla úspěšně vytvořena.';
        END
        """)
        conn.commit()
        print("Ověřeno/vytvořeno: Tabulka 'PREDICTIONS'.")
    except pyodbc.Error as e:
        print(f"Chyba při vytváření tabulky PREDICTIONS: {e}")
        if conn: conn.rollback()
    finally:
        if cursor:
            cursor.close()

def save_prediction(conn: pyodbc.Connection, symbol: str, prediction_time: datetime,
                   target_time: datetime, predicted_price: float, horizon_minutes: int):
    """Uloží predikci do databáze."""
    cursor = None
    try:
        cursor = conn.cursor()
        # Ukládáme čas s UTC a zaokrouhlením na celé minuty
        prediction_time_utc = prediction_time.astimezone(timezone.utc).replace(second=0, microsecond=0)
        target_time_utc = target_time.astimezone(timezone.utc).replace(second=0, microsecond=0)

        cursor.execute("""
        INSERT INTO PREDICTIONS (symbol, prediction_time, target_time, predicted_price, horizon_minutes)
        VALUES (?, ?, ?, ?, ?)
        """, (symbol, prediction_time_utc, target_time_utc, predicted_price, horizon_minutes))
        conn.commit()
        print(f"Predikce ({prediction_time_utc.strftime('%H:%M:00')} -> {target_time_utc.strftime('%H:%M:00')}, cena: {predicted_price:.5f}) uložena.")
    except pyodbc.Error as e:
        print(f"Chyba při ukládání predikce: {e}")
        if conn: conn.rollback()
    finally:
        if cursor:
            cursor.close()

def update_prediction_results(conn: pyodbc.Connection, symbol: str, table_name: str):
    cursor = None
    updated_count = 0
    try:
        cursor = conn.cursor()
        
        # Získáme predikce k aktualizaci - upravíme dotaz
        cursor.execute(f"""
        SELECT p.prediction_id, 
               p.target_time,
               p.predicted_price
        FROM PREDICTIONS p
        WHERE p.symbol = ? 
        AND (p.actual_price IS NULL OR p.prediction_error IS NULL)
        AND p.target_time <= DATEADD(MINUTE, -1, GETUTCDATE())  -- Přidáno -1 minuta pro jistotu
        ORDER BY p.target_time ASC  -- Přidáno řazení
        """, (symbol,))
        
        predictions_to_update = cursor.fetchall()
        print(f"Nalezeno {len(predictions_to_update)} predikcí k aktualizaci skutečnou cenou.")

        for pred_id, target_time, predicted_price in predictions_to_update:
            # Hledáme přesnou shodu času
            cursor.execute(f"""
            SELECT close_price, timestamp_utc
            FROM {table_name}
            WHERE timestamp_utc = ?
            """, (target_time,))

            result = cursor.fetchone()
            
            if result:
                actual_price, actual_time = result
                if actual_price is not None and predicted_price is not None:
                    prediction_error = actual_price - predicted_price
                    prediction_error_pct = (abs(prediction_error) / predicted_price * 100) if predicted_price != 0 else 0

                    cursor.execute("""
                    UPDATE PREDICTIONS
                    SET actual_price = ?,
                        prediction_error = ?,
                        prediction_error_pct = ?
                    WHERE prediction_id = ?
                    """, (actual_price, prediction_error, prediction_error_pct, pred_id))
                    updated_count += 1

                    print(f"Aktualizace predikce ID {pred_id}:")
                    print(f"  Target time: {target_time}")
                    print(f"  Actual time: {actual_time}")
                    print(f"  Predicted: {predicted_price:.4f}, Actual: {actual_price:.4f}")

        conn.commit()
        if updated_count > 0:
            print(f"Aktualizováno {updated_count} predikcí skutečnými hodnotami.")
            display_recent_predictions(conn, symbol)

    except pyodbc.Error as e:
        print(f"Chyba při aktualizaci výsledků: {e}")
        if conn: conn.rollback()
    finally:
        if cursor:
            cursor.close()

def display_recent_predictions(conn: pyodbc.Connection, symbol: str):
    """ Zobrazí posledních N predikcí a souhrnné statistiky. """
    cursor = None
    try:
        cursor = conn.cursor()
         # Zobrazíme statistiky za posledních 24h
        cursor.execute("""
        SELECT TOP 20
            CONVERT(datetime2, prediction_time) as prediction_time,
            CONVERT(datetime2, target_time) as target_time,
            predicted_price,
            actual_price,
            prediction_error,
            prediction_error_pct
        FROM PREDICTIONS
        WHERE symbol = ? AND prediction_time >= DATEADD(hour, -24, GETUTCDATE())
        ORDER BY prediction_time DESC
        """, (symbol,))
        historical = cursor.fetchall()

        if historical:
            print("\nVýsledky posledních predikcí (max 20 za 24h):")
            print(f"{'Čas predikce':^19} | {'Cílový čas':^19} | {'Predikce':^10} | {'Skutečná':^10} | {'Chyba abs':^10} | {'Chyba %':^8} | Status")
            print("-" * 95)

            completed_count = 0
            sum_error_abs = 0
            sum_error_pct = 0

            for row in historical:
                # Explicitní konverze na datetime, pokud přijde jako string
                pred_time = row[0]
                target_time = row[1]
                if isinstance(pred_time, str):
                    try:
                        # Přidána podpora pro milisekundy v datetime stringu
                        pred_time = datetime.strptime(pred_time.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    except ValueError as e:
                        print(f"VAROVÁNÍ: Nelze parsovat čas predikce '{pred_time}': {e}")
                        continue
                if isinstance(target_time, str):
                    try:
                        # Přidána podpora pro milisekundy v datetime stringu
                        target_time = datetime.strptime(target_time.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    except ValueError as e:
                        print(f"VAROVÁNÍ: Nelze parsovat cílový čas '{target_time}': {e}")
                        continue

                pred_time_str = pred_time.strftime('%Y-%m-%d %H:%M')
                target_time_str = target_time.strftime('%Y-%m-%d %H:%M')
                pred_price = f"{row[2]:.5f}" if row[2] is not None else "N/A"
                actual = f"{row[3]:.5f}" if row[3] is not None else "čeká"
                error_abs = f"{row[4]:.5f}" if row[4] is not None and row[3] is not None else "N/A"
                error_pct = f"{row[5]:.2f}%" if row[5] is not None and row[3] is not None else "N/A"

                status_color = Colors.BLUE
                status_text = "ČEKÁ"
                if row[3] is not None and row[4] is not None: # Pokud máme skutečnou cenu a chybu
                    completed_count += 1
                    sum_error_abs += abs(row[4])
                    sum_error_pct += row[5] if row[5] is not None else 0

                    abs_err = abs(row[4])
                    # Barevné označení podle absolutní chyby
                    if abs_err < 0.005:   # Příklad hranice - upravte dle potřeby
                        status_color = Colors.GREEN
                        status_text = "PŘESNÁ"
                    elif abs_err < 0.02: # Příklad hranice - upravte dle potřeby
                        status_color = Colors.YELLOW
                        status_text = "DOBRÁ"
                    else:
                        status_color = Colors.RED
                        status_text = "NEPŘESNÁ"

                print(f"{pred_time_str:^19} | {target_time_str:^19} | {pred_price:^10} | {actual:^10} | {error_abs:^10} | {error_pct:^8} | {status_color}{status_text}{Colors.ENDC}")

            # Souhrnné statistiky
            if completed_count > 0:
                avg_error_abs = sum_error_abs / completed_count
                avg_error_pct = sum_error_pct / completed_count
                print(f"\nStatistiky dokončených predikcí (zobrazeno max 20):")
                print(f"Počet dokončených: {completed_count}")
                print(f"Průměrná absolutní chyba: {avg_error_abs:.4f} USDT")
                print(f"Průměrná procentuální chyba: {avg_error_pct:.2f}%")
            elif historical:
                 print("\nČekání na dokončení predikcí pro výpočet statistik...")
        else:
            print("Žádné predikce za posledních 24 hodin k zobrazení.")

    except pyodbc.Error as e:
        print(f"Chyba při zobrazování výsledků predikcí: {e}")
    except Exception as e:
        print(f"Neočekávaná chyba v display_recent_predictions: {e}")
        traceback.print_exc()
    finally:
        if cursor:
            cursor.close()


def get_latest_timestamp_from_db(table_name: str, conn: pyodbc.Connection) -> datetime | None:
    """Získá poslední časovou známku z databáze (UTC)."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT MAX(timestamp_utc) FROM {table_name}")
        result = cursor.fetchone()
        if result and result[0]:
            # Explicitní konverze na datetime
            if isinstance(result[0], str):
                # Ořežeme milisekundy před parsováním, pokud existují
                timestamp_str = result[0].split('.')[0]
                latest_ts = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            else:
                latest_ts = result[0]
            # Přidáme UTC informaci
            if latest_ts.tzinfo is None:
                latest_ts = latest_ts.replace(tzinfo=timezone.utc)
            else:
                latest_ts = latest_ts.astimezone(timezone.utc)
            print(f"Poslední timestamp v DB ({table_name}): {latest_ts}")
            return latest_ts
        else:
            print(f"V tabulce '{table_name}' nejsou žádná data.")
            return None
    except Exception as e:
        print(f"Chyba při získávání posledního timestampu z '{table_name}': {e}")
        traceback.print_exc()
        return None
    finally:
        if cursor:
            cursor.close()

def save_dataframe_to_sql(df: pd.DataFrame, table_name: str, conn: pyodbc.Connection) -> bool:
    """Uloží DataFrame do SQL pomocí executemany (neošetřuje duplicity explicitně, spoléhá na PK)."""
    if df.empty:
        print("DataFrame pro uložení je prázdný, neukládám nic.")
        return True
    cursor = None
    inserted_count = 0
    skipped_count = 0
    try:
        cursor = conn.cursor()
        # Získání existujících timestampů pro rychlejší kontrolu (pokud je dat hodně, zvážit jiný přístup)
        # Pro jednoduchost zde vkládáme a necháme PK constraint selhat pro duplicity
        data_tuples = [tuple(x) for x in df.reset_index().to_numpy()]
        sql = f"INSERT INTO {table_name} (timestamp_utc, open_price, high_price, low_price, close_price, volume_amt) VALUES (?,?,?,?,?,?)"

        use_fast_executemany = hasattr(cursor, 'fast_executemany')
        if use_fast_executemany:
             cursor.fast_executemany = True
             print("Používám fast_executemany pro zápis do DB.")

        # Vkládání po jednom s ošetřením chyby duplicity klíče
        for row_tuple in data_tuples:
             try:
                 cursor.execute(sql, row_tuple)
                 inserted_count += 1
             except pyodbc.IntegrityError as ie:
                 # Očekávaná chyba pro duplicitní primární klíč (timestamp_utc)
                 if "PRIMARY KEY constraint" in str(ie) or "unique constraint" in str(ie):
                     skipped_count += 1
                 else:
                     # Jiná chyba integrity, tu chceme vidět
                     print(f"Chyba integrity při vkládání řádku {row_tuple}: {ie}")
                     conn.rollback() # Vrátíme změny v této transakci
                     return False # Ukončíme funkci jako neúspěšnou
             except pyodbc.Error as e:
                 print(f"Jiná chyba DB při vkládání řádku {row_tuple}: {e}")
                 conn.rollback()
                 return False

        conn.commit()
        print(f"Úspěšně uloženo {inserted_count} nových záznamů, přeskočeno {skipped_count} duplicitních do '{table_name}'.")
        return True

    except Exception as e: # Zachytíme i jiné než pyodbc chyby
        print(f"Neočekávaná chyba při ukládání DataFrame do SQL tabulky '{table_name}': {e}")
        traceback.print_exc()
        if conn: conn.rollback()
        return False
    finally:
        if cursor: cursor.close()


def load_data_from_sql(table_name: str, conn: pyodbc.Connection, start_date: datetime | None = None) -> pd.DataFrame | None:
    """Načte data z SQL do DataFrame (seřazené, index timestamp UTC)."""
    cursor = None
    try:
        cursor = conn.cursor()
        sql = f"SELECT timestamp_utc, open_price, high_price, low_price, close_price, volume_amt FROM {table_name}"
        params = []
        if start_date:
            # Zajistíme, že start_date je UTC
            if start_date.tzinfo is None: start_date = start_date.replace(tzinfo=timezone.utc)
            else: start_date = start_date.astimezone(timezone.utc)
            sql += " WHERE timestamp_utc >= ?"
            params.append(start_date)
        sql += " ORDER BY timestamp_utc ASC"

        print(f"Načítám data z DB: {sql}" + (f" s parametrem start_date={start_date}" if start_date else ""))
        cursor.execute(sql, params)
        rows = cursor.fetchall()
        if not rows:
            print(f"Nenalezena žádná data v tabulce '{table_name}'" + (f" od {start_date}." if start_date else "."))
            # Vrátíme prázdný DataFrame se správnými sloupci a indexem
            return pd.DataFrame(columns=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'],
                                index=pd.to_datetime([]).tz_localize('UTC'))

        df = pd.DataFrame.from_records(rows, columns=[desc[0] for desc in cursor.description])
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc']).dt.tz_localize(timezone.utc) # Přímo nastavíme UTC
        df.set_index('timestamp_utc', inplace=True)

        # Konverze na numerické typy
        for col in ['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # Kontrola a odstranění případných NaN po konverzi
        initial_rows = len(df)
        df.dropna(subset=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'], inplace=True)
        if len(df) < initial_rows:
             print(f"VAROVÁNÍ: Odstraněno {initial_rows - len(df)} řádků s neplatnými numerickými hodnotami po načtení z DB.")

        print(f"Úspěšně načteno {len(df)} platných záznamů z tabulky '{table_name}'" + (f" od {start_date}." if start_date else "."))
        return df

    except pyodbc.Error as e:
        print(f"Chyba při načítání dat z SQL tabulky '{table_name}': {e}")
        return None
    except Exception as e:
        print(f"Neočekávaná chyba při zpracování dat z SQL: {e}")
        traceback.print_exc()
        return None
    finally:
        if cursor: cursor.close()

# --- NOVÁ FUNKCE pro načtení historie predikcí ---
def load_prediction_history_from_sql(symbol: str, conn: pyodbc.Connection) -> pd.DataFrame | None:
    """Načte historii dokončených predikcí (čas vytvoření, chyba %) z DB."""
    cursor = None
    try:
        cursor = conn.cursor()
        sql = """
        SELECT prediction_time, prediction_error_pct
        FROM PREDICTIONS
        WHERE symbol = ? AND actual_price IS NOT NULL AND prediction_error_pct IS NOT NULL
        ORDER BY prediction_time ASC
        """
        print(f"Načítám historii predikcí pro symbol '{symbol}'...")
        cursor.execute(sql, (symbol,))
        rows = cursor.fetchall()

        if not rows:
            print(f"Nenalezena žádná dokončená historie predikcí pro symbol '{symbol}'.")
            return pd.DataFrame(columns=['prediction_error_pct'],
                                index=pd.to_datetime([]).tz_localize('UTC'))

        df = pd.DataFrame.from_records(rows, columns=['prediction_time', 'prediction_error_pct'])
        df['prediction_time'] = pd.to_datetime(df['prediction_time']).dt.tz_localize(timezone.utc) # Přímo nastavíme UTC
        df.set_index('prediction_time', inplace=True)
        df['prediction_error_pct'] = pd.to_numeric(df['prediction_error_pct'], errors='coerce')
        df.dropna(inplace=True) # Odstraníme řádky, kde chyba nebyla číselná

        print(f"Úspěšně načteno {len(df)} záznamů historie predikcí.")
        return df

    except pyodbc.Error as e:
        print(f"Chyba při načítání historie predikcí z SQL: {e}")
        return None
    except Exception as e:
        print(f"Neočekávaná chyba při zpracování historie predikcí z SQL: {e}")
        traceback.print_exc()
        return None
    finally:
        if cursor: cursor.close()

# --- NOVÁ FUNKCE monitor_model_quality ---
def monitor_model_quality(conn: pyodbc.Connection, symbol: str, window: int = 100):
    """Sleduje kvalitu modelu na posledních N predikcích."""
    cursor = conn.cursor()
    cursor.execute("""
    SELECT TOP (?) prediction_error_pct 
    FROM PREDICTIONS 
    WHERE symbol = ? AND prediction_error_pct IS NOT NULL 
    ORDER BY prediction_time DESC
    """, (window, symbol))
    errors = [row[0] for row in cursor.fetchall()]
    
    if errors:
        avg_error = np.mean(errors)
        if avg_error > 1.0:  # např. 1% jako práh
            print(f"{Colors.RED}Varování: Průměrná chyba ({avg_error:.2f}%) je příliš vysoká!{Colors.ENDC}")
            return False
    return True

# --- NOVÁ FUNKCE is_market_stable ---
def is_market_stable(latest_data: pd.DataFrame, volatility_threshold: float = 0.5) -> bool:
    """Kontroluje stabilitu trhu s adaptivním prahem."""
    try:
        # Výpočet adaptivního prahu na základě historické volatility
        historical_volatility = latest_data['volatility_5m'].mean()
        recent_volatility = latest_data['volatility_5m'].iloc[-1]
        volatility_std = latest_data['volatility_std'].iloc[-1]
        
        # Adaptivní práh se přizpůsobuje historické volatilitě
        adaptive_threshold = max(0.5, historical_volatility * 1.5)
        
        # Kontrola aktuální volatility a její stability
        is_stable = (recent_volatility <= adaptive_threshold and 
                    volatility_std <= adaptive_threshold/2)
        
        if not is_stable:
            print(f"Trh není stabilní: Volatilita {recent_volatility:.2f}% (práh {adaptive_threshold:.2f}%)")
            print(f"Standardní odchylka volatility: {volatility_std:.2f}%")
        
        return is_stable

    except Exception as e:
        print(f"Chyba při kontrole stability trhu: {e}")
        return False

# --- NOVÁ FUNKCE log_model_performance ---
def log_model_performance(conn: pyodbc.Connection, symbol: str, cycle_time: datetime):
    """Loguje výkonnost modelu pro dlouhodobou analýzu."""
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Získání metrik výkonu s ošetřením NULL hodnot
        cursor.execute("""
        SELECT 
            ISNULL(AVG(prediction_error_pct), 0) as avg_error,
            COUNT(*) as total_count,
            SUM(CASE WHEN prediction_error_pct <= 1.0 THEN 1 ELSE 0 END) as successful_count
        FROM PREDICTIONS
        WHERE symbol = ? 
        AND prediction_time >= DATEADD(HOUR, -24, GETUTCDATE())
        AND prediction_error_pct IS NOT NULL
        """, (symbol,))
        
        result = cursor.fetchone()
        
        if result and result[0] is not None:
            avg_error = float(result[0])
            total_count = int(result[1])
            successful_count = int(result[2] or 0)  # Pokud je NULL, použijeme 0
            
            # Uložení logu
            cursor.execute("""
            INSERT INTO MODEL_PERFORMANCE_LOG 
            (symbol, log_time, avg_error_pct, predictions_count, successful_predictions_count)
            VALUES (?, ?, ?, ?, ?)
            """, (symbol, cycle_time, avg_error, total_count, successful_count))
            
            conn.commit()
            print(f"Výkon modelu zalogován: Prům. chyba {avg_error:.2f}%, Úspěšnost {successful_count}/{total_count}")
        else:
            print("Nepodařilo se získat metriky výkonu modelu - žádná data k dispozici.")
            
    except Exception as e:
        print(f"Chyba při logování výkonu modelu: {e}")
        if conn: conn.rollback()
    finally:
        if cursor: cursor.close()

# --- 3. Získávání a Formátování Dat (Binance) ---

def download_historical_data(symbol: str, timeframe: str, start_date_str: str, exchange: ccxt.Exchange) -> list:
    """Stáhne historická OHLCV data z Binance pomocí ccxt."""
    all_ohlcv = []
    try:
        # Převod na UTC datetime objekt a pak na milisekundy
        start_dt_utc = datetime.strptime(start_date_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
        start_timestamp_ms = int(start_dt_utc.timestamp() * 1000)

        print(f"Stahování dat pro {symbol} od {start_dt_utc.strftime('%Y-%m-%d %H:%M:%S')} UTC...")
        while True:
            print(f"  Stahuji od timestampu: {start_timestamp_ms} ({exchange.iso8601(start_timestamp_ms)})")
            try:
                # Použití parametrů params pro získání dat do současnosti, pokud je to možné
                # since je začátek, limit je počet, chceme nejnovější
                # Pro jistotu necháme původní logiku stahování po částech
                ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=start_timestamp_ms, limit=BINANCE_API_LIMIT)

            except ccxt.RateLimitExceeded as e:
                print(f"{Colors.YELLOW}Chyba RateLimitExceeded: {e}. Čekám {exchange.rateLimit / 1000 * 2:.1f}s a zkouším znovu...{Colors.ENDC}")
                time.sleep(exchange.rateLimit / 1000 * 2)
                continue
            except ccxt.NetworkError as e:
                print(f"{Colors.YELLOW}Chyba sítě: {e}. Čekám 5s a zkouším znovu...{Colors.ENDC}")
                time.sleep(5)
                continue
            except ccxt.ExchangeError as e:
                print(f"{Colors.RED}Chyba burzy: {e}. Ukončuji stahování pro tento cyklus.{Colors.ENDC}")
                break
            except Exception as e:
                print(f"{Colors.RED}Neočekávaná chyba při stahování: {e}. Ukončuji.{Colors.ENDC}")
                traceback.print_exc()
                break

            if not ohlcv:
                print("  API nevrátilo žádná další data (nebo nastal konec historie).")
                break

            first_candle_ts = ohlcv[0][0]
            last_candle_ts = ohlcv[-1][0]
            
            # Přidáme jen data, která jsou novější než poslední stažený timestamp
            new_candles = [candle for candle in ohlcv if candle[0] > (all_ohlcv[-1][0] if all_ohlcv else start_timestamp_ms - 1)]
            all_ohlcv.extend(new_candles)
            
            print(f"  Staženo {len(ohlcv)} svíček. Přidáno {len(new_candles)} nových. "
                  f"Od {exchange.iso8601(first_candle_ts)} do {exchange.iso8601(last_candle_ts)}")


            # Posun na další časový úsek
            timeframe_duration_ms = exchange.parse_timeframe(timeframe) * 1000
            next_start_timestamp_ms = last_candle_ts + timeframe_duration_ms

            # Kontrola, zda jsme neskončili nebo nezacyklili
            if next_start_timestamp_ms <= start_timestamp_ms:
                 print("  Detekován konec dat nebo chyba v časovém posunu.")
                 break
            start_timestamp_ms = next_start_timestamp_ms


            # Pokud API vrátilo méně než limit, pravděpodobně jsme na konci
            if len(ohlcv) < BINANCE_API_LIMIT:
                print("  API vrátilo méně než limit, předpokládám konec dostupných dat.")
                break

            time.sleep(RATE_LIMIT_DELAY) # Respektujeme rate limit

    except ValueError as e:
        print(f"{Colors.RED}Chyba při parsování data '{start_date_str}': {e}{Colors.ENDC}")
    except Exception as e:
        print(f"{Colors.RED}Obecná chyba v download_historical_data: {e}{Colors.ENDC}")
        traceback.print_exc()

    print(f"Celkem staženo {len(all_ohlcv)} záznamů pro {symbol} v tomto cyklu.")
    return all_ohlcv

def format_ohlcv_data(ohlcv_list: list) -> pd.DataFrame | None:
    """Převede seznam OHLCV dat na Pandas DataFrame (index timestamp UTC)."""
    if not ohlcv_list:
        print("Formátování dat: Vstupní seznam je prázdný.")
        return pd.DataFrame(columns=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'],
                            index=pd.to_datetime([]).tz_localize('UTC'))
    try:
        df = pd.DataFrame(ohlcv_list, columns=['timestamp_utc', 'open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'])
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'], unit='ms', utc=True)
        df.set_index('timestamp_utc', inplace=True)

        # Konverze na numerické typy
        for col in ['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # Odstranění duplicitních indexů (timestampů) - ponecháme první výskyt
        df = df[~df.index.duplicated(keep='first')]

        # Odstranění řádků s NaN hodnotami v klíčových sloupcích
        initial_rows = len(df)
        df.dropna(subset=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'], inplace=True)
        if len(df) < initial_rows:
             print(f"VAROVÁNÍ: Odstraněno {initial_rows - len(df)} řádků s neplatnými numerickými hodnotami po formátování stažených dat.")

        df.sort_index(inplace=True)
        print(f"Formátování dat: Úspěšně vytvořen DataFrame s {len(df)} platnými a unikátními záznamy.")
        return df
    except Exception as e:
        print(f"{Colors.RED}Chyba při formátování OHLCV dat: {e}{Colors.ENDC}")
        traceback.print_exc()
        return None

# --- 4. Příprava Dat a Feature Engineering ---

def add_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Přidá technické indikátory pomocí pandas_ta."""
    try:
        print("Přidávám technické indikátory...")
        # Create an explicit copy
        df = df.copy()
        
        # Remove duplicates first
        df = df[~df.index.duplicated(keep='first')]
        
        # Basic TA indicators using .loc
        df.ta.sma(length=3, append=True, col_names=('SMA_3'))
        df.ta.sma(length=5, append=True, col_names=('SMA_5'))
        df.ta.ema(length=3, append=True, col_names=('EMA_3'))
        df.ta.ema(length=5, append=True, col_names=('EMA_5'))
        df.ta.rsi(length=6, append=True, col_names=('RSI_6'))
        df.ta.rsi(length=8, append=True, col_names=('RSI_8'))
        df.ta.atr(length=5, append=True, col_names=('ATR_5'))

        # MACD
        if all(c in df.columns for c in ['high_price', 'low_price', 'close_price']):
            try:
                df.ta.macd(fast=12, slow=26, signal=9, append=True,
                          col_names=('MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9'))
            except Exception as macd_e:
                print(f"VAROVÁNÍ: Chyba při výpočtu MACD: {macd_e}. Nastavuji na 0.")
                df.loc[:, 'MACD_12_26_9'] = 0
                df.loc[:, 'MACDh_12_26_9'] = 0
                df.loc[:, 'MACDs_12_26_9'] = 0
        else:
            print("VAROVÁNÍ: Chybí sloupce pro výpočet MACD. Nastavuji na 0.")
            df.loc[:, 'MACD_12_26_9'] = 0
            df.loc[:, 'MACDh_12_26_9'] = 0
            df.loc[:, 'MACDs_12_26_9'] = 0

        # Additional features using .loc
        df.loc[:, 'close_pct_change'] = df['close_price'].pct_change() * 100
        df.loc[:, 'volume_pct_change'] = df['volume_amt'].pct_change() * 100
        df.loc[:, 'price_std_3'] = df['close_price'].rolling(window=3).std()
        df.loc[:, 'volume_std_3'] = df['volume_amt'].rolling(window=3).std()

        # Handle NaN and inf values
        df = df.replace([np.inf, -np.inf], 0)
        df = df.ffill()
        df = df.bfill()
        df = df.fillna(0)

        print("Technické indikátory přidány a NaN ošetřeny.")
        return df

    except Exception as e:
        print(f"Chyba při přidávání technických indikátorů: {e}")
        traceback.print_exc()
        required_ta_cols = ['SMA_3', 'SMA_5', 'EMA_3', 'EMA_5', 'RSI_6', 'RSI_8', 'ATR_5',
                          'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'close_pct_change',
                          'volume_pct_change', 'price_std_3', 'volume_std_3']
        for col in required_ta_cols:
            if col not in df.columns:
                df.loc[:, col] = 0
        return df

def add_lagged_features(df: pd.DataFrame, lags: list) -> pd.DataFrame:
    """Přidá zpožděné příznaky pro 'close_price' a 'volume_amt'."""
    try:
        print(f"Přidávám zpožděné příznaky pro lagy: {lags}...")
        for lag in lags:
            if lag > 0:
                df[f'close_lag_{lag}'] = df['close_price'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume_amt'].shift(lag)
        print("Zpožděné příznaky (cena, objem) přidány.")
    except Exception as e:
        print(f"{Colors.RED}Chyba při přidávání zpožděných příznaků (cena, objem): {e}{Colors.ENDC}")
    return df

# --- NOVÁ FUNKCE pro přidání příznaků z historie chyb ---
def add_error_history_features(df: pd.DataFrame, error_lags: list, rolling_window: int) -> pd.DataFrame:
    """Přidá příznaky založené na historii predikčních chyb."""
    if 'prediction_error_pct' not in df.columns:
        print("Varování: Sloupec 'prediction_error_pct' nebyl nalezen pro přidání error features. Přeskakuji.")
        return df
    try:
        print(f"Přidávám příznaky z historie chyb (lagy: {error_lags}, okno: {rolling_window})...")

        # Posuneme chybu o 1, protože chyba pro aktuální čas ještě není známa
        # a my chceme použít chybu z MINULÉ predikce jako příznak pro tu AKTUÁLNÍ.
        error_col_shifted = df['prediction_error_pct'].shift(1)

        for lag in error_lags:
             if lag > 0:
                 df[f'error_pct_lag_{lag}'] = error_col_shifted.shift(lag -1) # lag=1 -> shift(0), lag=2 -> shift(1) atd.

        if rolling_window > 0:
             df[f'error_pct_roll_avg_{rolling_window}'] = error_col_shifted.rolling(window=rolling_window, min_periods=1).mean()
             df[f'error_pct_roll_std_{rolling_window}'] = error_col_shifted.rolling(window=rolling_window, min_periods=1).std()

        # Vyplnění NaN hodnot vzniklých posuny a rollingem
        error_feature_cols = [col for col in df.columns if 'error_pct_' in col]
        df[error_feature_cols] = df[error_feature_cols].ffill().bfill().fillna(0)

        print("Příznaky z historie chyb přidány.")
    except Exception as e:
         print(f"{Colors.RED}Chyba při přidávání příznaků z historie chyb: {e}{Colors.ENDC}")
         traceback.print_exc()
         # Zajistit existenci sloupců i při chybě
         generated_cols = [f'error_pct_lag_{lag}' for lag in error_lags if lag > 0]
         if rolling_window > 0:
             generated_cols.extend([f'error_pct_roll_avg_{rolling_window}', f'error_pct_roll_std_{rolling_window}'])
         for col in generated_cols:
             if col not in df.columns:
                 df[col] = 0
    return df

def add_volatility_features(df: pd.DataFrame) -> pd.DataFrame:
    """Přidá volatilitu jako příznak."""
    df = df.copy()
    df.loc[:, 'volatility_1m'] = (df['high_price'] - df['low_price']) / df['close_price'] * 100
    df.loc[:, 'volatility_5m'] = df['volatility_1m'].rolling(window=5).mean()
    df.loc[:, 'volatility_std'] = df['volatility_1m'].rolling(window=5).std()
    return df

def add_target_variable(df: pd.DataFrame, horizon: int) -> pd.DataFrame:
    """Přidá cílovou proměnnou 'target' (budoucí cena 'close_price')."""
    try:
        print(f"Přidávám cílovou proměnnou (close_price posunuté o -{horizon} period)...")
        df['target'] = df['close_price'].shift(-horizon)
        print("Cílová proměnná přidána.")
    except Exception as e:
        print(f"{Colors.RED}Chyba při přidávání cílové proměnné: {e}{Colors.ENDC}")
    return df

# --- UPRAVENÁ FUNKCE prepare_data ---
def prepare_data(df_ohlcv: pd.DataFrame, df_errors: pd.DataFrame | None, horizon: int, lags: list, error_lags: list, error_window: int) -> tuple:
    """
    Kompletní příprava dat: sloučení s chybami, indikátory, lagy (cena/objem/chyby), cíl, odstranění NaN.
    Vrací: X, y, feature_names
    """
    print("Zahajuji přípravu dat pro model...")
    if df_ohlcv.empty:
        print(f"{Colors.RED}Chyba: Vstupní OHLCV DataFrame pro prepare_data je prázdný.{Colors.ENDC}")
        return None, None, None

    df_processed = df_ohlcv.copy()

    # 1. Sloučení s historií chyb (pokud existuje)
    if df_errors is not None and not df_errors.empty:
        print(f"Slučuji OHLCV data ({len(df_processed)} řádků) s historií chyb ({len(df_errors)} řádků)...")
        # Spojíme tak, aby řádek OHLCV měl informaci o chybě predikce, která byla vytvořena v TENTO čas
        df_processed = pd.merge(df_processed, df_errors, left_index=True, right_index=True, how='left')
        # Výchozí hodnotu pro chybu, kde nebyla nalezena historie, můžeme nastavit např. na 0 nebo nechat NaN pro další zpracování
        # df_processed['prediction_error_pct'].fillna(0, inplace=True) # Prozatím necháme NaN, ošetří error features
        print(f"Data sloučena, počet řádků: {len(df_processed)}")
    else:
        print("Historie chyb není dostupná, příznaky z chyb nebudou přidány.")
        df_processed['prediction_error_pct'] = np.nan # Zajistíme existenci sloupce pro další kroky

    # 2. Přidání technických indikátorů
    df_processed = add_technical_indicators(df_processed)

    # 3. Přidání zpožděných příznaků (cena, objem)
    df_processed = add_lagged_features(df_processed, lags)

    # 4. Přidání příznaků z historie chyb (použije sloupec 'prediction_error_pct' vytvořený v kroku 1)
    df_processed = add_error_history_features(df_processed, error_lags, error_window)

    # 5. Přidání volatility jako příznaku
    df_processed = add_volatility_features(df_processed)

    # 6. Přidání cílové proměnné
    df_processed = add_target_variable(df_processed, horizon)

    # 7. Odstranění řádků s NaN hodnotami
    # Klíčové je odstranit řádky, kde chybí cílová hodnota 'target' (vznikne na konci dat kvůli shift(-horizon))
    # a také řádky na začátku, kde chybí hodnoty kvůli lagům nebo indikátorům.
    initial_rows = len(df_processed)
    # Odstraníme řádky, kde chybí cíl NEBO některý z lagů/indikátorů (kromě error features, ty už jsou ošetřené)
    cols_to_check_for_nan = df_processed.columns.difference(['target', 'prediction_error_pct'] + \
                            [col for col in df_processed.columns if 'error_pct_' in col])
    df_processed.dropna(subset=['target'] + list(cols_to_check_for_nan), inplace=True)
    final_rows = len(df_processed)
    print(f"Odstraněno {initial_rows - final_rows} řádků s NaN hodnotami (kvůli target, lagům, indikátorům).")

    if df_processed.empty:
        print(f"{Colors.RED}Chyba: Po odstranění NaN nezůstala žádná data pro trénování.{Colors.ENDC}")
        return None, None, None

    # 8. Rozdělení na X a y
    try:
        y = df_processed['target']
        # Odstraníme sloupce, které nejsou příznaky (cíl a pomocný sloupec s chybou)
        cols_to_drop = ['target', 'prediction_error_pct']
        X = df_processed.drop(columns=[col for col in cols_to_drop if col in df_processed.columns])
        feature_names = X.columns.tolist()
        print(f"Data připravena. Počet příznaků: {len(feature_names)}, Počet vzorků: {len(X)}")
        # print("Použité příznaky:", feature_names) # Pro kontrolu
        return X, y, feature_names
    except KeyError as e:
        print(f"{Colors.RED}Chyba: Sloupec '{e}' nebyl nalezen při finálním dělení na X a y.{Colors.ENDC}")
        return None, None, None
    except Exception as e:
        print(f"{Colors.RED}Neočekávaná chyba při oddělování X a y: {e}{Colors.ENDC}")
        traceback.print_exc()
        return None, None, None


# --- 5. Trénování a Predikce ---

def train_final_model(X: pd.DataFrame, y: pd.Series, xgb_params: dict) -> tuple:
    try:
        print("Zahajuji trénování finálního modelu...")
        
        # Zkrátíme historii na 6 hodin pro citlivější reakce
        hours_back = 6
        rows_back = hours_back * 60
        
        if len(X) > rows_back:
            X = X.iloc[-rows_back:]
            y = y.iloc[-rows_back:]
            print(f"Trénuji na posledních {hours_back} hodinách dat ({len(X)} vzorků)")
        
        # Výraznější váhy pro novější data
        sample_weights = np.exp(np.linspace(0, 1, len(X))) # Exponenciální váhy
        
        # Normalizace vah
        sample_weights = sample_weights / sample_weights.mean()
        
        # Škálování
        scaler = MinMaxScaler(feature_range=(-1, 1))  # Rozšířený rozsah pro větší variabilitu
        X_scaled = scaler.fit_transform(X)
        
        model = xgb.XGBRegressor(**xgb_params)
        model.fit(X_scaled, y, sample_weight=sample_weights)
        
        return model, scaler
    except Exception as e:
        print(f"Chyba při trénování modelu: {e}")
        traceback.print_exc()
        return None, None

# --- UPRAVENÁ FUNKCE predict_future ---
def predict_future(latest_data_raw: pd.DataFrame,
                  latest_errors: pd.DataFrame | None,
                  model: xgb.XGBRegressor,
                  scaler: MinMaxScaler,
                  horizon: int,
                  lags: list,
                  error_lags: list,
                  error_window: int,
                  feature_names: list) -> float | None:
    """Predikuje budoucí cenu na základě nejnovějších surových dat a historie chyb."""
    print("Provádím predikci budoucí ceny...")
    if latest_data_raw.empty:
        print(f"{Colors.RED}Chyba: Prázdný DataFrame 'latest_data_raw' pro predikci.{Colors.ENDC}")
        return None

    # Potřebujeme dostatek historie pro výpočet všech lagů a indikátorů
    # Např. max lag + nejdelší okno indikátoru (např. MACD ~26) + error window
    required_rows = max(lags + error_lags + [26, error_window]) + 5 # Bezpečnostní rezerva
    if len(latest_data_raw) < required_rows:
        print(f"{Colors.RED}Chyba: Nedostatek dat pro predikci. Potřeba alespoň {required_rows} řádků, máme {len(latest_data_raw)}.{Colors.ENDC}")
        return None

    try:
        df_predict = latest_data_raw.copy()
        print(f"Data pro predikci - Počet řádků: {len(df_predict)}, Poslední čas: {df_predict.index[-1]}")

        # 1. Sloučení s historií chyb (pokud existuje) - pro výpočet error features
        if latest_errors is not None and not latest_errors.empty:
             df_predict = pd.merge(df_predict, latest_errors, left_index=True, right_index=True, how='left')
             print("Historie chyb připojena k datům pro predikci.")
        else:
             df_predict['prediction_error_pct'] = np.nan
             print("Historie chyb nedostupná pro predikci.")

        # 2. Přidání technických indikátorů
        df_predict = add_technical_indicators(df_predict)

        # 3. Přidání zpožděných příznaků (cena, objem)
        df_predict = add_lagged_features(df_predict, lags)

        # 4. Přidání příznaků z historie chyb
        df_predict = add_error_history_features(df_predict, error_lags, error_window)

        # 5. Přidání volatility jako příznaku
        df_predict = add_volatility_features(df_predict)

        # Kontrola stability trhu
        if not is_market_stable(df_predict):
            print(f"{Colors.YELLOW}Varování: Trh je příliš volatilní, přeskakuji predikci.{Colors.ENDC}")
            return None

        # Ensemble predikce - vytvoříme několik predikcí s různými posuny dat
        predictions = []
        for i in range(5):  # 5 různých predikcí
            window_data = df_predict.iloc[i:].copy()
            X_latest = prepare_prediction_data(window_data, feature_names)
            if X_latest is not None:
                X_latest_scaled = scaler.transform(X_latest)
                pred = model.predict(X_latest_scaled)[0]
                predictions.append(pred)

        if predictions:
            # Použijeme váženou ensemble predikci
            volatility = df_predict['volatility_5m'].iloc[-1]
            final_prediction = weighted_ensemble_predict(predictions, volatility)
            print(f"Vážená ensemble predikce: {final_prediction:.5f}")
            return final_prediction

    except Exception as e:
        print(f"{Colors.RED}Chyba během predikce: {e}{Colors.ENDC}")
        traceback.print_exc()
        return None

# --- NOVÁ FUNKCE prepare_prediction_data ---
def prepare_prediction_data(df: pd.DataFrame, feature_names: list) -> pd.DataFrame | None:
    """Připraví poslední řádek dat pro predikci."""
    try:
        # Vezmeme poslední řádek a vybereme pouze sloupce použité při trénování
        df_pred = df[feature_names].iloc[[-1]]
        
        # Zkontrolujeme, zda máme všechny potřebné sloupce
        missing_cols = set(feature_names) - set(df_pred.columns)
        if missing_cols:
            print(f"Chybí sloupce pro predikci: {missing_cols}")
            return None
            
        # Zkontrolujeme NaN hodnoty
        if df_pred.isna().any().any():
            print("Varování: Data pro predikci obsahují NaN hodnoty. Nahrazuji nulami.")
            df_pred.fillna(0, inplace=True)
            
        return df_pred

    except Exception as e:
        print(f"Chyba při přípravě dat pro predikci: {e}")
        traceback.print_exc()
        return None

# --- NOVÁ FUNKCE weighted_ensemble_predict ---
def weighted_ensemble_predict(predictions: list, volatility: float) -> float:
    if not predictions:
        return None
    
    # Zvýšení vlivu volatility na náhodnou složku
    noise_factor = volatility * 0.001  # Zvýšeno 10x
    noise = np.random.normal(0, noise_factor, len(predictions))
    predictions = np.array(predictions) + noise
    
    # Dynamické váhy založené na volatilitě
    if volatility > 1.0:  # Při vysoké volatilitě
        weights = [0.3, 0.4, 0.15, 0.15]  # Upravené váhy
    else:  # Při nízké volatilitě
        weights = [0.4, 0.3, 0.15, 0.15]  # Původní váhy
    
    mean_pred = np.mean(predictions)
    median_pred = np.median(predictions)
    min_pred = np.min(predictions)
    max_pred = np.max(predictions)
    
    final_pred = (mean_pred * weights[0] + 
                 median_pred * weights[1] + 
                 min_pred * weights[2] + 
                 max_pred * weights[3])
    
    return final_pred

# --- 6. Hlavní Skript ---

if __name__ == "__main__":
    print(f"{Colors.HEADER}{'='*60}{Colors.ENDC}")
    print(f"{Colors.BOLD}ZAHÁJENÍ SKRIPTU PRO PREDIKCI CENY {SYMBOL} (Horizont: {PREDICTION_HORIZON} min){Colors.ENDC}")
    print(f"{Colors.BOLD}Používá historii chyb pro trénování.{Colors.ENDC}")
    print(f"{Colors.HEADER}{'='*60}{Colors.ENDC}")

    # Inicializace CCXT burzy mimo smyčku
    try:
        exchange = ccxt.binance({'enableRateLimit': True, 'options': {'defaultType': 'future'}})
        print("Připojeno k Binance (futures).")
    except Exception as ex_e:
        print(f"{Colors.RED}Kritická chyba: Nepodařilo se inicializovat CCXT pro Binance: {ex_e}{Colors.ENDC}")
        sys.exit(1) # Ukončíme skript, pokud se nelze připojit na začátku


    while True:
        start_cycle_time = time.time()
        print(f"\n--- Cyklus zahájen: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")
        conn = None # Resetujeme připojení na začátku cyklu
        try:
            # --- Připojení k DB a příprava tabulek ---
            conn = connect_db(conn_str)
            if conn is None:
                print("Nepodařilo se připojit k databázi. Čekám 60s na další pokus...")
                time.sleep(60)
                continue

            create_ohlcv_table_if_not_exists(DB_TABLE_NAME, conn)
            create_predictions_table(conn)

            # --- Aktualizace starých predikcí ---
            print("\n--- Aktualizace výsledků předchozích predikcí ---")
            update_prediction_results(conn, SYMBOL, DB_TABLE_NAME) # Předáváme i název OHLCV tabulky

            # --- Získání nových OHLCV dat ---
            print("\n--- Získávání nových OHLCV dat ---")
            latest_timestamp_db = get_latest_timestamp_from_db(DB_TABLE_NAME, conn)
            current_time_utc = datetime.now(timezone.utc)

            if latest_timestamp_db:
                # Začneme stahovat od další minuty po posledním záznamu v DB
                start_download_dt = latest_timestamp_db + timedelta(minutes=1)
                # Pojistka proti stahování z budoucnosti, pokud by DB měla novější čas než aktuální
                start_download_dt = min(start_download_dt, current_time_utc - timedelta(minutes=1))
            else:
                # Pokud je DB prázdná, stáhneme data např. za posledních N dní/hodin
                start_download_dt = current_time_utc - timedelta(days=3) # Stáhneme 3 dny historie pro start
                print("Databáze je prázdná, stahuji data za poslední 3 dny.")

            start_download_str = start_download_dt.strftime('%Y-%m-%d %H:%M:%S')

            new_ohlcv_list = download_historical_data(SYMBOL, TIMEFRAME, start_download_str, exchange)

            if new_ohlcv_list:
                new_data_df = format_ohlcv_data(new_ohlcv_list)
                if new_data_df is not None and not new_data_df.empty:
                    save_dataframe_to_sql(new_data_df, DB_TABLE_NAME, conn)
                else:
                    print("Nebylo co ukládat (žádná nová validní data).")
            else:
                 print("Nebyly staženy žádné nové OHLCV záznamy.")


            # --- Načtení dat pro trénování a predikci ---
            print("\n--- Načítání dat pro model ---")
            # Načteme všechna data z DB (nebo od rozumného data, např. posledních X dní)
            # Zde načítáme všechna data pro jednoduchost, pro velké DB by bylo lepší omezit
            load_start_date = None # Načte všechna data
            # load_start_date = datetime.now(timezone.utc) - timedelta(days=10) # Příklad: načíst jen 10 dní
            all_data_from_db = load_data_from_sql(DB_TABLE_NAME, conn, start_date=load_start_date)

            # Načtení historie chyb
            prediction_history_df = load_prediction_history_from_sql(SYMBOL, conn)


            # --- Trénování modelu a Predikce ---
            if all_data_from_db is not None and not all_data_from_db.empty:
                print("\n--- Příprava dat a trénování modelu ---")
                # Předáme OHLCV data i historii chyb do prepare_data
                X, y, feature_names = prepare_data(
                    all_data_from_db,
                    prediction_history_df, # << Předáme historii chyb
                    PREDICTION_HORIZON,
                    LAG_FEATURES_LIST,
                    ERROR_LAG_FEATURES_LIST, # << Nový parametr
                    ERROR_ROLLING_WINDOW     # << Nový parametr
                )

                if X is not None and y is not None and feature_names:
                    model, scaler = train_final_model(X, y, XGB_PARAMS)

                    if model is not None and scaler is not None:
                        print("\n--- Generování nové predikce ---")
                        # Pro predikci potřebujeme poslední část OHLCV dat a historii chyb
                        # Délka historie musí stačit na výpočet všech features
                        num_rows_needed = max(LAG_FEATURES_LIST + ERROR_LAG_FEATURES_LIST + [26, ERROR_ROLLING_WINDOW]) + 10
                        latest_data_raw = all_data_from_db.iloc[-num_rows_needed:]

                        # Pro predikci potřebujeme historii chyb končící PŘED posledním časem v latest_data_raw
                        latest_errors_for_pred = None
                        if prediction_history_df is not None and not prediction_history_df.empty:
                            latest_errors_for_pred = prediction_history_df[prediction_history_df.index < latest_data_raw.index[-1]]


                        final_prediction = predict_future(
                            latest_data_raw,
                            latest_errors_for_pred, # << Předáme historii chyb
                            model,
                            scaler,
                            PREDICTION_HORIZON,
                            LAG_FEATURES_LIST,
                            ERROR_LAG_FEATURES_LIST, # << Nový parametr
                            ERROR_ROLLING_WINDOW,    # << Nový parametr
                            feature_names
                         )

                        if final_prediction is not None:
                            # Uložení nové predikce
                            prediction_time_utc = datetime.now(timezone.utc)
                            # Cílový čas = čas poslední svíčky + horizont (nebo aktuální čas + horizont?)
                            # Použijeme čas poslední svíčky dat použitých pro predikci
                            last_data_time = latest_data_raw.index[-1].to_pydatetime()
                            # Pokud je poslední čas dat starší než aktuální čas - horizont, použijeme aktuální čas
                            # if last_data_time < prediction_time_utc - timedelta(minutes=PREDICTION_HORIZON + 1):
                            #      base_time_for_target = prediction_time_utc
                            # else:
                            #      base_time_for_target = last_data_time
                            # --> Jednodušší: vždy od aktuálního času vytvoření predikce
                            base_time_for_target = prediction_time_utc

                            target_time_utc = base_time_for_target + timedelta(minutes=PREDICTION_HORIZON)

                            save_prediction(conn, SYMBOL, prediction_time_utc, target_time_utc,
                                         final_prediction, PREDICTION_HORIZON)

                            # Znovu zobrazíme poslední predikce (již by měla zahrnovat právě uloženou)
                            print("\n--- Aktualizovaný přehled posledních predikcí ---")
                            display_recent_predictions(conn, SYMBOL)

                            # Monitorování kvality modelu
                            print("\n--- Monitorování kvality modelu ---")
                            model_quality_ok = monitor_model_quality(conn, SYMBOL)
                            if not model_quality_ok:
                                print(f"{Colors.YELLOW}Model vykazuje vysokou chybu - přetrénovávám s novými parametry...{Colors.ENDC}")
                                
                                # Upravíme parametry pro lepší regularizaci
                                retrain_params = XGB_PARAMS.copy()
                                retrain_params.update({
                                    'learning_rate': XGB_PARAMS['learning_rate'] * 0.5,  # Snížíme learning rate
                                    'max_depth': max(2, XGB_PARAMS['max_depth'] - 1),    # Snížíme hloubku
                                    'reg_alpha': XGB_PARAMS['reg_alpha'] * 1.5,          # Zvýšíme regularizaci
                                    'reg_lambda': XGB_PARAMS['reg_lambda'] * 1.5,
                                    'min_child_weight': XGB_PARAMS['min_child_weight'] + 1
                                })
                                
                                # Přetrénujeme model s upravenými parametry
                                model, scaler = train_final_model(X, y, retrain_params)
                                
                                if model is not None and scaler is not None:
                                    print(f"{Colors.GREEN}Model úspěšně přetrénován s upravenými parametry.{Colors.ENDC}")
                                else:
                                    print(f"{Colors.RED}Přetrénování modelu selhalo.{Colors.ENDC}")

                            # Logování výkonu modelu
                            print("\n--- Logování výkonu modelu ---")
                            log_model_performance(conn, SYMBOL, datetime.now(timezone.utc))

                        else:
                            print(f"{Colors.YELLOW}Nepodařilo se vygenerovat finální predikci v tomto cyklu.{Colors.ENDC}")
                    else:
                        print(f"{Colors.YELLOW}Model nebo scaler nebyly úspěšně natrénovány, predikce nebude provedena.{Colors.ENDC}")
                else:
                    print(f"{Colors.YELLOW}Nepodařilo se připravit data (X, y), trénování a predikce neproběhne.{Colors.ENDC}")
            else:
                print(f"{Colors.YELLOW}Nebylo možné načíst dostatek dat z DB pro trénování/predikci.{Colors.ENDC}")


        except pyodbc.Error as db_err:
             print(f"{Colors.RED}Chyba databáze v hlavní smyčce: {db_err}{Colors.ENDC}")
             traceback.print_exc()
             # Zde bychom mohli zkusit znovu připojit nebo počkat déle
             time.sleep(30) # Krátká pauza před dalším cyklem v případě DB chyby
        except ccxt.NetworkError as net_err:
             print(f"{Colors.RED}Chyba sítě CCXT v hlavní smyčce: {net_err}{Colors.ENDC}")
             print("Čekám 60s před dalším pokusem...")
             time.sleep(60)
        except Exception as e:
            print(f"{Colors.RED}Neočekávaná chyba v hlavní smyčce: {e}{Colors.ENDC}")
            traceback.print_exc()
            # Obecná pauza pro zotavení
            time.sleep(60)

        finally:
            if conn:
                try:
                    conn.close()
                    print("Připojení k DB uzavřeno.")
                except pyodbc.Error as close_err:
                     print(f"Chyba při zavírání DB spojení: {close_err}")

        # --- Čekání do dalšího cyklu ---
        end_cycle_time = time.time()
        cycle_duration = end_cycle_time - start_cycle_time
        print(f"--- Cyklus dokončen za {cycle_duration:.2f} sekund ---")

        # Chceme běžet přibližně každou minutu
        sleep_time = max(0, 60 - cycle_duration)
        print(f"Čekám {sleep_time:.2f} sekund do dalšího cyklu...")
        time.sleep(sleep_time)


        # Ukončení pomocí Ctrl+C
        # except KeyboardInterrupt:
        #     print("\nUkončení skriptu na žádost uživatele.")
        #     break # Ukončí while smyčku

    # Tento kód se již nevykoná kvůli `while True` a `KeyboardInterrupt`
    # print("Skript ukončen.")