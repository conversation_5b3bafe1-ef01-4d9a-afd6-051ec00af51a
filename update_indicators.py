"""
Skript pro aktualizaci technických indikátorů v databázi.
V reálném nasazení by tento skript měl načítat data z API burzy.
"""
import os
import logging
import pandas as pd
import numpy as np
import pyodbc
import requests
import time
from datetime import datetime, timedelta
import ta  # Knihovna pro výpočet technických indikátorů

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

# Nastavení API
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"
SYMBOL = "ALCHUSDT"  # Symbol kryptoměny
INTERVAL = "1m"  # Interval (1 minuta)

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def get_binance_data(symbol=SYMBOL, interval=INTERVAL, limit=60):
    """
    Získá data z Binance API.

    Args:
        symbol: Symbol kryptoměny
        interval: Interval (1m, 5m, 15m, 1h, atd.)
        limit: Počet záznamů

    Returns:
        DataFrame s daty
    """
    try:
        # Parametry požadavku
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        # Odeslání požadavku
        response = requests.get(BINANCE_API_URL, params=params)

        # Kontrola odpovědi
        if response.status_code != 200:
            logger.error(f"Chyba při získávání dat z Binance API: {response.text}")
            return None

        # Zpracování odpovědi
        data = response.json()

        # Vytvoření DataFrame
        df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume',
                                         'close_time', 'quote_asset_volume', 'number_of_trades',
                                         'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'])

        # Konverze typů
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)

        # Přejmenování sloupců
        df = df.rename(columns={'close': 'close_price'})

        logger.info(f"Získáno {len(df)} záznamů z Binance API")
        return df
    except Exception as e:
        logger.error(f"Chyba při získávání dat z Binance API: {e}")
        return None

def calculate_indicators(df):
    """
    Vypočítá technické indikátory.

    Args:
        df: DataFrame s cenovými daty

    Returns:
        DataFrame s indikátory
    """
    try:
        # Kopie DataFrame
        df_indicators = df.copy()

        # RSI
        rsi = ta.momentum.RSIIndicator(df_indicators['close_price'], window=14)
        df_indicators['RSI'] = rsi.rsi()

        # EMA
        df_indicators['EMA9'] = ta.trend.ema_indicator(df_indicators['close_price'], window=9)
        df_indicators['EMA20'] = ta.trend.ema_indicator(df_indicators['close_price'], window=20)

        # Bollinger Bands
        bollinger = ta.volatility.BollingerBands(df_indicators['close_price'], window=20, window_dev=2)
        df_indicators['boll_high'] = bollinger.bollinger_hband()
        df_indicators['boll_low'] = bollinger.bollinger_lband()

        # ATR
        atr = ta.volatility.AverageTrueRange(df_indicators['high'], df_indicators['low'], df_indicators['close_price'], window=14)
        df_indicators['ATR'] = atr.average_true_range()

        # Odstranění řádků s chybějícími hodnotami
        df_indicators = df_indicators.dropna()

        logger.info(f"Vypočítáno {len(df_indicators)} indikátorů")
        return df_indicators
    except Exception as e:
        logger.error(f"Chyba při výpočtu indikátorů: {e}")
        return None

def save_indicators(conn, df_indicators):
    """
    Uloží indikátory do databáze.

    Args:
        conn: Připojení k databázi
        df_indicators: DataFrame s indikátory

    Returns:
        True pokud bylo uložení úspěšné, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Počet úspěšně uložených záznamů
        success_count = 0

        # Uložení indikátorů
        for _, row in df_indicators.iterrows():
            # Kontrola, zda záznam již existuje
            cursor.execute("""
            SELECT COUNT(*) FROM dbo.alch_indikatory
            WHERE timestamp = ?
            """, (row['timestamp'],))

            if cursor.fetchone()[0] > 0:
                # Záznam již existuje, přeskočíme ho
                continue

            # Vložení indikátoru
            cursor.execute("""
            INSERT INTO dbo.alch_indikatory
            (timestamp, close_price, RSI, EMA9, EMA20, boll_high, boll_low, ATR)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                row['timestamp'], row['close_price'], row['RSI'], row['EMA9'],
                row['EMA20'], row['boll_high'], row['boll_low'], row['ATR']
            ))

            success_count += 1

        conn.commit()
        logger.info(f"Uloženo {success_count} nových indikátorů do databáze")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při ukládání indikátorů: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def simulate_price_update():
    """
    Simuluje aktualizaci ceny a indikátorů.
    V reálném nasazení by tato funkce měla být nahrazena skutečným načítáním dat z API.
    """
    try:
        # Připojení k databázi
        conn = connect_to_db()
        if not conn:
            logger.error("Nelze se připojit k databázi")
            return

        # Načtení posledního záznamu
        cursor = conn.cursor()
        cursor.execute("""
        SELECT TOP 1 timestamp, close_price FROM dbo.alch_indikatory
        ORDER BY timestamp DESC
        """)

        last_record = cursor.fetchone()

        if last_record:
            last_timestamp, last_price = last_record

            # Konverze timestamp na datetime, pokud je to řetězec
            if isinstance(last_timestamp, str):
                try:
                    last_timestamp = datetime.fromisoformat(last_timestamp.replace(' ', 'T'))
                except ValueError:
                    try:
                        last_timestamp = datetime.strptime(last_timestamp, '%Y-%m-%d %H:%M:%S.%f')
                    except ValueError:
                        last_timestamp = datetime.now() - timedelta(minutes=1)

            # Aktuální čas
            now = datetime.now()

            # Pokud je poslední záznam starší než 1 minuta, vytvoříme nový
            if (now - last_timestamp).total_seconds() >= 60:
                # Simulace nové ceny (náhodná změna +/- 0.5%)
                new_price = last_price * (1 + np.random.uniform(-0.005, 0.005))

                # Vytvoření DataFrame s novým záznamem
                df = pd.DataFrame({
                    'timestamp': [now],
                    'open': [last_price],
                    'high': [max(last_price, new_price)],
                    'low': [min(last_price, new_price)],
                    'close_price': [new_price],
                    'volume': [np.random.uniform(10000, 50000)]
                })

                # Výpočet indikátorů
                # Poznámka: Pro správný výpočet indikátorů potřebujeme historická data
                # V této simulaci použijeme zjednodušené výpočty
                df['RSI'] = np.random.uniform(30, 70)
                df['EMA9'] = new_price * (1 + np.random.uniform(-0.002, 0.002))
                df['EMA20'] = new_price * (1 + np.random.uniform(-0.005, 0.005))
                df['boll_high'] = new_price * (1 + np.random.uniform(0.01, 0.02))
                df['boll_low'] = new_price * (1 - np.random.uniform(0.01, 0.02))
                df['ATR'] = new_price * np.random.uniform(0.005, 0.015)

                # Uložení indikátorů
                save_indicators(conn, df)
            else:
                logger.info(f"Poslední záznam je novější než 1 minuta, přeskakuji aktualizaci")
        else:
            logger.warning("Nenalezen žádný záznam v tabulce dbo.alch_indikatory")

            # Vytvoření prvního záznamu
            now = datetime.now()
            price = 0.15  # Výchozí cena

            # Vytvoření DataFrame s novým záznamem
            df = pd.DataFrame({
                'timestamp': [now],
                'open': [price],
                'high': [price * 1.01],
                'low': [price * 0.99],
                'close_price': [price],
                'volume': [np.random.uniform(10000, 50000)]
            })

            # Výpočet indikátorů (zjednodušené)
            df['RSI'] = 50
            df['EMA9'] = price
            df['EMA20'] = price
            df['boll_high'] = price * 1.02
            df['boll_low'] = price * 0.98
            df['ATR'] = price * 0.01

            # Uložení indikátorů
            save_indicators(conn, df)

        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Chyba při simulaci aktualizace ceny: {e}")

def update_indicators_from_api():
    """
    Aktualizuje indikátory z API burzy.
    V reálném nasazení by tato funkce měla být použita místo simulace.
    """
    try:
        # Připojení k databázi
        conn = connect_to_db()
        if not conn:
            logger.error("Nelze se připojit k databázi")
            return

        # Získání dat z API
        df = get_binance_data()
        if df is None:
            logger.error("Nelze získat data z API")
            conn.close()
            return

        # Výpočet indikátorů
        df_indicators = calculate_indicators(df)
        if df_indicators is None:
            logger.error("Nelze vypočítat indikátory")
            conn.close()
            return

        # Uložení indikátorů
        save_indicators(conn, df_indicators)

        conn.close()
    except Exception as e:
        logger.error(f"Chyba při aktualizaci indikátorů z API: {e}")

def main():
    """Hlavní funkce."""
    print("=== Aktualizace technických indikátorů ===")

    # Zpracování argumentů příkazové řádky
    import argparse
    parser = argparse.ArgumentParser(description='Aktualizace technických indikátorů')
    parser.add_argument('--mode', choices=['simulate', 'api'], default='simulate',
                        help='Režim aktualizace (simulate = simulace, api = použití API)')
    parser.add_argument('--interval', type=int, default=60,
                        help='Interval aktualizace v sekundách')
    parser.add_argument('--count', type=int, default=0,
                        help='Počet aktualizací (0 = nekonečno)')

    args = parser.parse_args()

    # Počítadlo aktualizací
    update_count = 0

    try:
        while True:
            # Aktualizace indikátorů
            if args.mode == 'simulate':
                simulate_price_update()
            else:
                update_indicators_from_api()

            # Inkrementace počítadla
            update_count += 1

            # Kontrola počtu aktualizací
            if args.count > 0 and update_count >= args.count:
                logger.info(f"Dosažen požadovaný počet aktualizací ({args.count})")
                break

            # Čekání na další aktualizaci
            logger.info(f"Čekám {args.interval} sekund na další aktualizaci...")
            time.sleep(args.interval)
    except KeyboardInterrupt:
        logger.info("Aktualizace přerušena uživatelem")
    except Exception as e:
        logger.error(f"Neočekávaná chyba: {e}")

if __name__ == "__main__":
    main()
