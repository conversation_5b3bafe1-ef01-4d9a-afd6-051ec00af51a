"""
Modul pro analýzu sentimentu trhu a externích faktorů.
Implementuje metody pro výpočet interních indikátorů sentimentu
a integraci externích dat pro zlepšení predikce.
"""
import os
import logging
import pandas as pd
import numpy as np
import pyodbc
import requests
from datetime import datetime, timedelta
from typing import Dict, Tuple, List, Optional, Union, Any

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

# Konstanty pro analýzu sentimentu
SENTIMENT_WINDOW_SHORT = 12  # Krátké okno pro výpočet sentimentu (hodiny)
SENTIMENT_WINDOW_LONG = 24   # Dlouhé okno pro výpočet sentimentu (hodiny)
VOLUME_WEIGHT = 0.6          # Váha objemu při výpočtu sentimentu
PRICE_WEIGHT = 0.4           # Váha ceny při výpočtu sentimentu

def load_price_data(hours: int = SENTIMENT_WINDOW_LONG) -> pd.DataFrame:
    """
    Načte cenová data z databáze.
    
    Args:
        hours: Počet hodin historie k načtení
        
    Returns:
        DataFrame s cenovými daty
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            query = f"""
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM alch_price_history 
            WHERE timestamp >= '{cutoff_time.strftime('%Y-%m-%d %H:%M:%S')}'
            ORDER BY timestamp ASC
            """
            df = pd.read_sql(query, conn)
            
            logger.info(f"Načteno {len(df)} záznamů pro analýzu sentimentu")
            return df
    except Exception as e:
        logger.error(f"Chyba při načítání cenových dat: {e}")
        return pd.DataFrame()

def calculate_price_momentum(df: pd.DataFrame) -> pd.Series:
    """
    Vypočítá cenové momentum jako indikátor sentimentu.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        Series s hodnotami cenového momenta
    """
    try:
        if df.empty:
            return pd.Series()
        
        # Výpočet procentuální změny ceny
        price_change = df['close_price'].pct_change()
        
        # Výpočet kumulativního momenta (exponenciální vážený průměr)
        momentum = price_change.ewm(span=12).mean()
        
        # Normalizace na rozsah -1 až 1
        max_abs = max(abs(momentum.min()), abs(momentum.max()))
        if max_abs > 0:
            normalized_momentum = momentum / max_abs
        else:
            normalized_momentum = momentum
        
        return normalized_momentum
    except Exception as e:
        logger.error(f"Chyba při výpočtu cenového momenta: {e}")
        return pd.Series()

def calculate_volume_momentum(df: pd.DataFrame) -> pd.Series:
    """
    Vypočítá momentum objemu jako indikátor sentimentu.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        Series s hodnotami momenta objemu
    """
    try:
        if df.empty:
            return pd.Series()
        
        # Výpočet procentuální změny objemu
        volume_change = df['volume'].pct_change()
        
        # Výpočet kumulativního momenta (exponenciální vážený průměr)
        momentum = volume_change.ewm(span=12).mean()
        
        # Normalizace na rozsah -1 až 1
        max_abs = max(abs(momentum.min()), abs(momentum.max()))
        if max_abs > 0:
            normalized_momentum = momentum / max_abs
        else:
            normalized_momentum = momentum
        
        return normalized_momentum
    except Exception as e:
        logger.error(f"Chyba při výpočtu momenta objemu: {e}")
        return pd.Series()

def calculate_buy_sell_pressure(df: pd.DataFrame) -> pd.Series:
    """
    Vypočítá tlak na nákup/prodej jako indikátor sentimentu.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        Series s hodnotami tlaku na nákup/prodej
    """
    try:
        if df.empty:
            return pd.Series()
        
        # Výpočet rozsahu ceny
        price_range = df['high_price'] - df['low_price']
        
        # Výpočet pozice uzavírací ceny v rámci rozsahu
        # 0 = uzavření na minimu, 1 = uzavření na maximu
        close_position = (df['close_price'] - df['low_price']) / price_range
        
        # Nahrazení NaN hodnot (když je rozsah 0)
        close_position = close_position.fillna(0.5)
        
        # Převod na rozsah -1 až 1
        # -1 = silný prodejní tlak, 1 = silný nákupní tlak
        buy_sell_pressure = (close_position * 2) - 1
        
        # Vážení podle objemu
        normalized_volume = df['volume'] / df['volume'].rolling(window=20).mean()
        normalized_volume = normalized_volume.fillna(1.0)
        
        # Omezení extrémních hodnot
        normalized_volume = normalized_volume.clip(0.5, 2.0)
        
        # Aplikace váhy objemu
        weighted_pressure = buy_sell_pressure * normalized_volume
        
        # Vyhlazení pomocí klouzavého průměru
        smoothed_pressure = weighted_pressure.rolling(window=5).mean().fillna(0)
        
        return smoothed_pressure
    except Exception as e:
        logger.error(f"Chyba při výpočtu tlaku na nákup/prodej: {e}")
        return pd.Series()

def calculate_market_sentiment(df: pd.DataFrame = None) -> Tuple[float, Dict[str, float]]:
    """
    Vypočítá celkový sentiment trhu.
    
    Args:
        df: DataFrame s cenovými daty (volitelné)
        
    Returns:
        Tuple (celkový sentiment, detaily sentimentu)
        Sentiment je v rozsahu -1 až 1, kde:
        -1 = extrémně negativní sentiment
        0 = neutrální sentiment
        1 = extrémně pozitivní sentiment
    """
    try:
        # Načtení dat, pokud nejsou poskytnuta
        if df is None or df.empty:
            df = load_price_data()
        
        if df.empty:
            logger.warning("Nedostatek dat pro výpočet sentimentu")
            return 0.0, {}
        
        # Výpočet jednotlivých složek sentimentu
        price_momentum = calculate_price_momentum(df)
        volume_momentum = calculate_volume_momentum(df)
        buy_sell_pressure = calculate_buy_sell_pressure(df)
        
        # Poslední hodnoty
        last_price_momentum = price_momentum.iloc[-1] if not price_momentum.empty else 0.0
        last_volume_momentum = volume_momentum.iloc[-1] if not volume_momentum.empty else 0.0
        last_buy_sell_pressure = buy_sell_pressure.iloc[-1] if not buy_sell_pressure.empty else 0.0
        
        # Výpočet celkového sentimentu jako váženého průměru
        sentiment = (
            last_price_momentum * 0.3 +
            last_volume_momentum * 0.3 +
            last_buy_sell_pressure * 0.4
        )
        
        # Omezení na rozsah -1 až 1
        sentiment = max(-1.0, min(1.0, sentiment))
        
        # Detaily sentimentu
        details = {
            'price_momentum': float(last_price_momentum),
            'volume_momentum': float(last_volume_momentum),
            'buy_sell_pressure': float(last_buy_sell_pressure),
            'overall_sentiment': float(sentiment)
        }
        
        logger.info(f"Vypočítaný sentiment trhu: {sentiment:.2f}")
        logger.info(f"Detaily sentimentu: {details}")
        
        return sentiment, details
    except Exception as e:
        logger.error(f"Chyba při výpočtu sentimentu trhu: {e}")
        return 0.0, {}

def fetch_external_sentiment() -> Dict[str, Any]:
    """
    Získá externí data o sentimentu trhu.
    
    Returns:
        Slovník s externími daty o sentimentu
    """
    # Tato funkce je připravena pro budoucí integraci s externími API
    # V současné implementaci vrací pouze placeholder data
    
    try:
        # Placeholder pro externí data
        external_data = {
            'crypto_fear_greed_index': 50,  # Neutrální hodnota
            'btc_dominance': 45.0,
            'market_trend': 'neutral',
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info("Získána externí data o sentimentu (placeholder)")
        return external_data
    except Exception as e:
        logger.error(f"Chyba při získávání externích dat o sentimentu: {e}")
        return {}

def add_sentiment_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Přidá příznaky sentimentu do DataFrame.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        DataFrame s přidanými příznaky sentimentu
    """
    try:
        # Vytvoření kopie DataFrame
        df = df.copy()
        
        # Výpočet příznaků sentimentu
        df['price_momentum'] = calculate_price_momentum(df)
        df['volume_momentum'] = calculate_volume_momentum(df)
        df['buy_sell_pressure'] = calculate_buy_sell_pressure(df)
        
        # Výpočet celkového sentimentu
        df['market_sentiment'] = (
            df['price_momentum'] * 0.3 +
            df['volume_momentum'] * 0.3 +
            df['buy_sell_pressure'] * 0.4
        )
        
        # Omezení na rozsah -1 až 1
        df['market_sentiment'] = df['market_sentiment'].clip(-1.0, 1.0)
        
        # Kategorizace sentimentu
        df['sentiment_category'] = pd.cut(
            df['market_sentiment'],
            bins=[-1.01, -0.6, -0.2, 0.2, 0.6, 1.01],
            labels=[-2, -1, 0, 1, 2]
        ).astype(float)
        
        # Přidání zpožděných hodnot sentimentu
        for lag in [1, 3, 6, 12]:
            df[f'sentiment_lag_{lag}'] = df['market_sentiment'].shift(lag)
        
        # Výpočet změny sentimentu
        df['sentiment_change'] = df['market_sentiment'].diff()
        df['sentiment_change_pct'] = df['sentiment_change'] / df['market_sentiment'].shift(1).abs()
        
        # Nahrazení NaN hodnot
        sentiment_cols = [col for col in df.columns if 'sentiment' in col]
        df[sentiment_cols] = df[sentiment_cols].fillna(0)
        
        logger.info(f"Přidáno {len(sentiment_cols)} příznaků sentimentu")
        return df
    except Exception as e:
        logger.error(f"Chyba při přidávání příznaků sentimentu: {e}")
        return df

def create_sentiment_table():
    """
    Vytvoří tabulku pro ukládání dat o sentimentu v databázi.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cursor = conn.cursor()
            
            # Kontrola, zda tabulka existuje
            cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'alch_sentiment')
            BEGIN
                CREATE TABLE alch_sentiment (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    timestamp DATETIME NOT NULL,
                    price_momentum FLOAT,
                    volume_momentum FLOAT,
                    buy_sell_pressure FLOAT,
                    market_sentiment FLOAT,
                    external_sentiment FLOAT,
                    combined_sentiment FLOAT
                )
                
                CREATE INDEX idx_sentiment_timestamp ON alch_sentiment(timestamp)
            END
            """)
            
            conn.commit()
            logger.info("Tabulka alch_sentiment vytvořena nebo již existuje")
    except Exception as e:
        logger.error(f"Chyba při vytváření tabulky sentimentu: {e}")

def save_sentiment_data(sentiment_data: Dict[str, float]):
    """
    Uloží data o sentimentu do databáze.
    
    Args:
        sentiment_data: Slovník s daty o sentimentu
    """
    try:
        # Získání externích dat
        external_data = fetch_external_sentiment()
        
        # Výpočet kombinovaného sentimentu
        combined_sentiment = sentiment_data.get('overall_sentiment', 0.0)
        if 'crypto_fear_greed_index' in external_data:
            # Převod indexu strachu a chamtivosti (0-100) na rozsah -1 až 1
            external_sentiment = (external_data['crypto_fear_greed_index'] - 50) / 50
            # Kombinace interního a externího sentimentu
            combined_sentiment = (combined_sentiment * 0.7) + (external_sentiment * 0.3)
        
        with pyodbc.connect(CONN_STR) as conn:
            cursor = conn.cursor()
            
            # Vložení dat do tabulky
            cursor.execute("""
            INSERT INTO alch_sentiment (
                timestamp, price_momentum, volume_momentum, buy_sell_pressure,
                market_sentiment, external_sentiment, combined_sentiment
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                datetime.now(),
                sentiment_data.get('price_momentum', 0.0),
                sentiment_data.get('volume_momentum', 0.0),
                sentiment_data.get('buy_sell_pressure', 0.0),
                sentiment_data.get('overall_sentiment', 0.0),
                external_data.get('crypto_fear_greed_index', 50) / 100,
                combined_sentiment
            )
            
            conn.commit()
            logger.info("Data o sentimentu uložena do databáze")
    except Exception as e:
        logger.error(f"Chyba při ukládání dat o sentimentu: {e}")

def get_latest_sentiment() -> Dict[str, float]:
    """
    Získá nejnovější data o sentimentu z databáze.
    
    Returns:
        Slovník s nejnovějšími daty o sentimentu
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            query = """
            SELECT TOP 1 * FROM alch_sentiment
            ORDER BY timestamp DESC
            """
            df = pd.read_sql(query, conn)
            
            if df.empty:
                logger.warning("Žádná data o sentimentu v databázi")
                return {}
            
            # Převod na slovník
            sentiment_data = {
                'timestamp': df['timestamp'].iloc[0],
                'price_momentum': float(df['price_momentum'].iloc[0]),
                'volume_momentum': float(df['volume_momentum'].iloc[0]),
                'buy_sell_pressure': float(df['buy_sell_pressure'].iloc[0]),
                'market_sentiment': float(df['market_sentiment'].iloc[0]),
                'external_sentiment': float(df['external_sentiment'].iloc[0]),
                'combined_sentiment': float(df['combined_sentiment'].iloc[0])
            }
            
            logger.info(f"Získána nejnovější data o sentimentu z {sentiment_data['timestamp']}")
            return sentiment_data
    except Exception as e:
        logger.error(f"Chyba při získávání nejnovějších dat o sentimentu: {e}")
        return {}

def update_sentiment_data():
    """
    Aktualizuje data o sentimentu v databázi.
    """
    try:
        # Načtení cenových dat
        df = load_price_data()
        
        if df.empty:
            logger.warning("Nedostatek dat pro aktualizaci sentimentu")
            return
        
        # Výpočet sentimentu
        sentiment, details = calculate_market_sentiment(df)
        
        # Vytvoření tabulky, pokud neexistuje
        create_sentiment_table()
        
        # Uložení dat
        save_sentiment_data(details)
        
        logger.info("Data o sentimentu aktualizována")
    except Exception as e:
        logger.error(f"Chyba při aktualizaci dat o sentimentu: {e}")

if __name__ == "__main__":
    # Při spuštění jako samostatný skript aktualizujeme data o sentimentu
    update_sentiment_data()
    
    # Zobrazení aktuálního sentimentu
    sentiment, details = calculate_market_sentiment()
    print(f"Aktuální sentiment trhu: {sentiment:.2f}")
    print(f"Detaily sentimentu: {details}")
