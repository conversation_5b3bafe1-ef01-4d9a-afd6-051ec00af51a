import time
import subprocess
import logging
from datetime import datetime
from colorama import init, Fore, Style

# inicializace barev
init(autoreset=True)

# nastavení logování
logging.basicConfig(
    filename='predikce_loop.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

while True:
    try:
        # SPUSTENI INDIKATORU
        cas_start = datetime.now()
        print(
            f"{Fore.CYAN}[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ✅ Spouštím indikatory.py{Style.RESET_ALL}"
        )
        logging.info("Spouštím indikatory.py")
        
        result = subprocess.run(["python", "indikatory.py"], capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}[CHYBA] {result.stderr}{Style.RESET_ALL}")
            logging.error(f"indikatory.py chyba: {result.stderr}")
        
        cas_konec = datetime.now()
        cas_trvani = (cas_konec - cas_start).total_seconds()
        print(f"{Fore.CYAN}[INFO] indikatory.py dokončeno za {cas_trvani} sekund{Style.RESET_ALL}")
        logging.info(f"indikatory.py dokončeno za {cas_trvani} sekund")

        # SPUSTENI LEARN
        cas_start = datetime.now()
        print(
            f"{Fore.YELLOW}[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🔁 Spouštím learn.py (trénování modelu){Style.RESET_ALL}"
        )
        logging.info("Spouštím learn.py")
        
        result = subprocess.run(["python", "learn2.py"], capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}[CHYBA] {result.stderr}{Style.RESET_ALL}")
            logging.error(f"learn2.py chyba: {result.stderr}")
            
        cas_konec = datetime.now()
        cas_trvani = (cas_konec - cas_start).total_seconds()
        print(f"{Fore.YELLOW}[INFO] learn.py dokončeno za {cas_trvani} sekund{Style.RESET_ALL}")
        logging.info(f"learn.py dokončeno za {cas_trvani} sekund")

        # SPUSTENI PREDIK
        cas_start = datetime.now()
        print(
            f"{Fore.GREEN}[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🔮 Spouštím predik.py (predikce ceny){Style.RESET_ALL}"
        )
        logging.info("Spouštím predik2.py")
        
        result = subprocess.run(["python", "predik2.py"], capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(f"{Fore.RED}[CHYBA] {result.stderr}{Style.RESET_ALL}")
            logging.error(f"predik.py chyba: {result.stderr}")
            
        cas_konec = datetime.now()
        cas_trvani = (cas_konec - cas_start).total_seconds()
        print(f"{Fore.GREEN}[INFO] predik.py dokončeno za {cas_trvani} sekund{Style.RESET_ALL}")
        logging.info(f"predik.py dokončeno za {cas_trvani} sekund")

    except Exception as e:
        print(f"{Fore.RED}[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🛑 Došlo k chybě: {e}{Style.RESET_ALL}")
        logging.error(f"Došlo k chybě: {e}")

    # Čekání 5 minut před dalším cyklem
    print(
        f"{Fore.MAGENTA}[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🕒 Čekám 5 minut před dalším cyklem{Style.RESET_ALL}"
    )
    logging.info("Čekám 5 minut před dalším cyklem")
    time.sleep(200)  # 5 minut = 300 sekund
