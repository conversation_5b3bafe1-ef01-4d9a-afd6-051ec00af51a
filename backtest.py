"""
Modul pro backtesting predikčních modelů na historických datech.
Umožňuje testovat různé strategie a vyhodnocovat jejich výkon.
"""
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pyodbc
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Union, Optional

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def load_historical_data(conn: pyodbc.Connection,
                        symbol: str,
                        start_date: datetime,
                        end_date: datetime) -> pd.DataFrame:
    """
    Načte historická data z databáze.

    Args:
        conn: Připojení k databázi
        symbol: Symbol kryptoměny
        start_date: Počáteční datum
        end_date: Koncové datum

    Returns:
        DataFrame s historickými daty
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení OHLCV dat
        cursor.execute("""
        SELECT * FROM alch_price_history
        WHERE timestamp BETWEEN ? AND ?
        ORDER BY timestamp ASC
        """, (start_date, end_date))

        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"Nenalezena žádná historická data pro {symbol} v období {start_date} až {end_date}")
            return pd.DataFrame()

        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)

        # Konverze datumů
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Nastavení indexu
        df.set_index('timestamp', inplace=True)

        logger.info(f"Načteno {len(df)} záznamů historických dat pro {symbol}")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání historických dat: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def load_historical_indicators(conn: pyodbc.Connection,
                             start_date: datetime,
                             end_date: datetime) -> pd.DataFrame:
    """
    Načte historické technické indikátory z databáze.

    Args:
        conn: Připojení k databázi
        start_date: Počáteční datum
        end_date: Koncové datum

    Returns:
        DataFrame s historickými indikátory
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení indikátorů
        cursor.execute("""
        SELECT * FROM alch_indikatory
        WHERE timestamp BETWEEN ? AND ?
        ORDER BY timestamp ASC
        """, (start_date, end_date))

        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"Nenalezeny žádné historické indikátory v období {start_date} až {end_date}")
            return pd.DataFrame()

        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)

        # Konverze datumů
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Nastavení indexu
        df.set_index('timestamp', inplace=True)

        logger.info(f"Načteno {len(df)} záznamů historických indikátorů")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání historických indikátorů: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def load_historical_sentiment(conn: pyodbc.Connection,
                            symbol: str,
                            start_date: datetime,
                            end_date: datetime) -> pd.DataFrame:
    """
    Načte historická data o sentimentu z databáze.

    Args:
        conn: Připojení k databázi
        symbol: Symbol kryptoměny
        start_date: Počáteční datum
        end_date: Koncové datum

    Returns:
        DataFrame s historickými daty o sentimentu
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení dat o sentimentu
        cursor.execute("""
        SELECT * FROM EXTERNAL_SENTIMENT
        WHERE symbol = ? AND timestamp BETWEEN ? AND ?
        ORDER BY timestamp ASC
        """, (symbol, start_date, end_date))

        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"Nenalezena žádná historická data o sentimentu pro {symbol} v období {start_date} až {end_date}")
            return pd.DataFrame()

        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)

        # Konverze datumů
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Nastavení indexu
        df.set_index('timestamp', inplace=True)

        logger.info(f"Načteno {len(df)} záznamů historických dat o sentimentu pro {symbol}")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání historických dat o sentimentu: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def prepare_backtest_data(price_df: pd.DataFrame,
                         indicators_df: pd.DataFrame,
                         sentiment_df: pd.DataFrame = None) -> pd.DataFrame:
    """
    Připraví data pro backtesting.

    Args:
        price_df: DataFrame s cenovými daty
        indicators_df: DataFrame s technickými indikátory
        sentiment_df: DataFrame s daty o sentimentu (volitelné)

    Returns:
        DataFrame s připravenými daty pro backtesting
    """
    try:
        # Spojení cenových dat a indikátorů
        df = price_df.join(indicators_df, how='inner')

        # Přidání dat o sentimentu, pokud jsou k dispozici
        if sentiment_df is not None and not sentiment_df.empty:
            # Resampling dat o sentimentu na stejnou frekvenci jako cenová data
            sentiment_resampled = sentiment_df.resample('1min').ffill()

            # Spojení s hlavním DataFrame
            df = df.join(sentiment_resampled[['combined_sentiment']], how='left')

            # Doplnění chybějících hodnot
            df['combined_sentiment'] = df['combined_sentiment'].fillna(method='ffill')

        # Odstranění řádků s chybějícími hodnotami
        df = df.dropna()

        logger.info(f"Připraveno {len(df)} řádků dat pro backtesting")
        return df
    except Exception as e:
        logger.error(f"Chyba při přípravě dat pro backtesting: {e}")
        return pd.DataFrame()

class BacktestStrategy:
    """Základní třída pro backtesting strategií."""

    def __init__(self, name: str, initial_balance: float = 1000.0):
        """
        Inicializace strategie.

        Args:
            name: Název strategie
            initial_balance: Počáteční zůstatek
        """
        self.name = name
        self.positions = []
        self.trades = []
        self.current_position = None
        self.balance = initial_balance
        self.equity = [self.balance]
        self.timestamps = []

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generuje obchodní signály.

        Args:
            data: DataFrame s daty

        Returns:
            DataFrame s přidanými signály
        """
        # Tato metoda by měla být přepsána v odvozených třídách
        return data

    def execute_backtest(self, data: pd.DataFrame, commission: float = 0.001) -> Dict[str, Any]:
        """
        Provede backtesting strategie.

        Args:
            data: DataFrame s daty a signály
            commission: Poplatek za obchod (jako desetinné číslo)

        Returns:
            Slovník s výsledky backtestu
        """
        # Generování signálů
        data = self.generate_signals(data)

        # Reset stavu
        self.positions = []
        self.trades = []
        self.current_position = None
        self.balance = 1000.0
        self.equity = [self.balance]
        self.timestamps = [data.index[0]]

        # Procházení dat
        for i in range(1, len(data)):
            timestamp = data.index[i]
            row = data.iloc[i]
            prev_row = data.iloc[i-1]

            # Kontrola signálů
            if 'signal' in row:
                signal = row['signal']

                # Otevření dlouhé pozice
                if signal == 1 and self.current_position is None:
                    self.open_position(timestamp, row['close_price'], 'long', commission)

                # Otevření krátké pozice
                elif signal == -1 and self.current_position is None:
                    self.open_position(timestamp, row['close_price'], 'short', commission)

                # Uzavření pozice
                elif signal == 0 and self.current_position is not None:
                    self.close_position(timestamp, row['close_price'], commission)

            # Aktualizace equity
            if self.current_position is not None:
                # Výpočet nerealizovaného zisku/ztráty
                if self.current_position['type'] == 'long':
                    unrealized_pnl = (row['close_price'] - self.current_position['entry_price']) / self.current_position['entry_price'] * self.current_position['size']
                else:  # short
                    unrealized_pnl = (self.current_position['entry_price'] - row['close_price']) / self.current_position['entry_price'] * self.current_position['size']

                current_equity = self.balance + unrealized_pnl
            else:
                current_equity = self.balance

            self.equity.append(current_equity)
            self.timestamps.append(timestamp)

        # Uzavření otevřené pozice na konci backtestu
        if self.current_position is not None:
            self.close_position(data.index[-1], data.iloc[-1]['close_price'], commission)

        # Výpočet metrik
        metrics = self.calculate_metrics()

        return {
            'strategy_name': self.name,
            'final_balance': self.balance,
            'trades': self.trades,
            'metrics': metrics,
            'equity_curve': pd.Series(self.equity, index=self.timestamps)
        }

    def open_position(self, timestamp: datetime, price: float, position_type: str, commission: float):
        """
        Otevře obchodní pozici.

        Args:
            timestamp: Časová značka
            price: Cena
            position_type: Typ pozice ('long' nebo 'short')
            commission: Poplatek za obchod
        """
        # Velikost pozice (použijeme 90% zůstatku)
        position_size = self.balance * 0.9

        # Odečtení poplatku
        commission_amount = position_size * commission
        position_size -= commission_amount

        self.current_position = {
            'entry_time': timestamp,
            'entry_price': price,
            'type': position_type,
            'size': position_size
        }

        self.positions.append(self.current_position)

        logger.info(f"Otevřena {position_type} pozice na {price} v čase {timestamp}")

    def close_position(self, timestamp: datetime, price: float, commission: float):
        """
        Uzavře obchodní pozici.

        Args:
            timestamp: Časová značka
            price: Cena
            commission: Poplatek za obchod
        """
        if self.current_position is None:
            return

        # Výpočet zisku/ztráty
        if self.current_position['type'] == 'long':
            pnl = (price - self.current_position['entry_price']) / self.current_position['entry_price'] * self.current_position['size']
        else:  # short
            pnl = (self.current_position['entry_price'] - price) / self.current_position['entry_price'] * self.current_position['size']

        # Odečtení poplatku
        commission_amount = (self.current_position['size'] + pnl) * commission
        pnl -= commission_amount

        # Aktualizace zůstatku
        self.balance += pnl

        # Záznam obchodu
        trade = {
            'entry_time': self.current_position['entry_time'],
            'exit_time': timestamp,
            'entry_price': self.current_position['entry_price'],
            'exit_price': price,
            'type': self.current_position['type'],
            'size': self.current_position['size'],
            'pnl': pnl,
            'pnl_pct': pnl / self.current_position['size'] * 100,
            'commission': commission_amount
        }

        self.trades.append(trade)

        logger.info(f"Uzavřena {self.current_position['type']} pozice na {price} v čase {timestamp}, PnL: {pnl:.2f} ({trade['pnl_pct']:.2f}%)")

        self.current_position = None

    def calculate_metrics(self) -> Dict[str, float]:
        """
        Vypočítá metriky výkonu strategie.

        Returns:
            Slovník s metrikami
        """
        if not self.trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'avg_profit': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'total_return': 0.0
            }

        # Základní metriky
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t['pnl'] > 0]
        losing_trades = [t for t in self.trades if t['pnl'] <= 0]

        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0.0

        avg_profit = sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0.0
        avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0.0

        total_profit = sum(t['pnl'] for t in winning_trades)
        total_loss = abs(sum(t['pnl'] for t in losing_trades))

        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')

        # Výpočet drawdownu
        equity_series = pd.Series(self.equity)
        cummax = equity_series.cummax()
        drawdown = (cummax - equity_series) / cummax
        max_drawdown = drawdown.max()

        # Výpočet Sharpe ratio
        equity_returns = pd.Series(self.equity).pct_change().dropna()
        sharpe_ratio = equity_returns.mean() / equity_returns.std() * np.sqrt(252) if len(equity_returns) > 1 else 0.0

        # Celkový výnos
        total_return = (self.balance - 1000.0) / 1000.0 * 100

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_return': total_return
        }
