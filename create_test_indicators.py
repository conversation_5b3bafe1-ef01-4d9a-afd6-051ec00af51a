"""
Skript pro vytvoření testovacích indikátorů v databázi.
"""
import os
import logging
import pyodbc
import random
from datetime import datetime, timedelta

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def check_indicators_table(conn):
    """Zkontroluje, zda existuje tabulka alch_indikatory."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Kontrola, zda tabulka již existuje
        cursor.execute("""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'alch_indikatory'
        """)

        if cursor.fetchone()[0] > 0:
            logger.info("Tabulka dbo.alch_indikatory existuje.")
            return True
        else:
            logger.warning("Tabulka dbo.alch_indikatory neexistuje.")
            return False
    except pyodbc.Error as e:
        logger.error(f"Chyba při kontrole tabulky dbo.alch_indikatory: {e}")
        return False
    finally:
        if cursor:
            cursor.close()

def create_indicators_table(conn):
    """Vytvoří tabulku alch_indikatory v databázi."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Vytvoření tabulky
        cursor.execute("""
        CREATE TABLE dbo.alch_indikatory (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            close_price FLOAT NOT NULL,
            RSI FLOAT NOT NULL,
            EMA9 FLOAT NOT NULL,
            EMA20 FLOAT NOT NULL,
            boll_high FLOAT NOT NULL,
            boll_low FLOAT NOT NULL,
            ATR FLOAT NOT NULL
        )
        """)

        # Vytvoření indexů pro rychlejší vyhledávání
        cursor.execute("""
        CREATE INDEX idx_indikatory_timestamp ON dbo.alch_indikatory (timestamp)
        """)

        conn.commit()
        logger.info("Tabulka dbo.alch_indikatory byla úspěšně vytvořena.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při vytváření tabulky dbo.alch_indikatory: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def insert_test_indicators(conn, count=10):
    """Vloží testovací indikátory do tabulky."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Aktuální čas
        now = datetime.now()

        # Základní cena
        base_price = 0.15

        # Vložení indikátorů
        for i in range(count):
            # Čas (každou minutu)
            timestamp = now - timedelta(minutes=i)

            # Cena
            close_price = base_price * (1 + random.uniform(-0.01, 0.01))

            # Indikátory
            rsi = random.uniform(30, 70)
            ema9 = close_price * (1 + random.uniform(-0.005, 0.005))
            ema20 = close_price * (1 + random.uniform(-0.01, 0.01))
            boll_high = close_price * (1 + random.uniform(0.01, 0.02))
            boll_low = close_price * (1 - random.uniform(0.01, 0.02))
            atr = close_price * random.uniform(0.005, 0.015)

            # Vložení indikátoru
            cursor.execute("""
            INSERT INTO dbo.alch_indikatory
            (timestamp, close_price, RSI, EMA9, EMA20, boll_high, boll_low, ATR)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                timestamp, close_price, rsi, ema9, ema20, boll_high, boll_low, atr
            ))

        conn.commit()
        logger.info(f"Vloženo {count} testovacích indikátorů.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při vkládání testovacích indikátorů: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def main():
    """Hlavní funkce."""
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi.")
        return

    try:
        # Kontrola, zda tabulka existuje
        if not check_indicators_table(conn):
            # Vytvoření tabulky
            if not create_indicators_table(conn):
                logger.error("Nelze vytvořit tabulku dbo.alch_indikatory.")
                return

        # Vložení testovacích indikátorů
        insert_test_indicators(conn, count=10)

        # Výpis informací o tabulce
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM dbo.alch_indikatory")
        count = cursor.fetchone()[0]
        logger.info(f"Počet indikátorů v tabulce: {count}")

        # Výpis posledních 5 indikátorů
        cursor.execute("""
        SELECT TOP 5 id, timestamp, close_price, RSI, EMA9, EMA20
        FROM dbo.alch_indikatory
        ORDER BY timestamp DESC
        """)

        rows = cursor.fetchall()
        if rows:
            logger.info("Poslední indikátory:")
            for row in rows:
                logger.info(f"  ID: {row[0]}, Čas: {row[1]}, Cena: {row[2]}, RSI: {row[3]}, EMA9: {row[4]}, EMA20: {row[5]}")

        cursor.close()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
