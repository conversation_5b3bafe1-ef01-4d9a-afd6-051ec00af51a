"""
Jednoduchý dashboard pro vizualizaci výsledků predikčního modelu.
"""
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pyodbc
from datetime import datetime, timedelta
import matplotlib.dates as mdates
from typing import Dict, List, Tuple, Any, Union, Optional

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def load_predictions(conn: pyodbc.Connection,
                   days: int = 7) -> pd.DataFrame:
    """
    Načte historii predikcí z databáze.

    Args:
        conn: Připojení k databázi
        days: Počet dní historie

    Returns:
        DataFrame s historií predikcí
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Výpočet data
        start_date = datetime.now() - timedelta(days=days)

        # Načtení predikcí
        cursor.execute("""
        SELECT * FROM PREDICTIONS
        WHERE prediction_time >= ?
        ORDER BY prediction_time ASC
        """, (start_date,))

        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"Nenalezeny žádné predikce za posledních {days} dní")
            return pd.DataFrame()

        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)

        # Konverze datumů
        if 'prediction_time' in df.columns:
            df['timestamp'] = pd.to_datetime(df['prediction_time'])
        elif 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])

        logger.info(f"Načteno {len(df)} predikcí za posledních {days} dní")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání predikcí: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def load_model_performance(conn: pyodbc.Connection,
                         days: int = 30) -> pd.DataFrame:
    """
    Načte historii výkonu modelu z databáze.

    Args:
        conn: Připojení k databázi
        days: Počet dní historie

    Returns:
        DataFrame s historií výkonu modelu
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Výpočet data
        start_date = datetime.now() - timedelta(days=days)

        # Načtení výkonu modelu
        cursor.execute("""
        SELECT * FROM MODEL_PERFORMANCE_LOG
        WHERE log_time >= ?
        ORDER BY log_time ASC
        """, (start_date,))

        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"Nenalezena žádná historie výkonu modelu za posledních {days} dní")
            return pd.DataFrame()

        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)

        # Konverze datumů
        df['log_time'] = pd.to_datetime(df['log_time'])

        logger.info(f"Načteno {len(df)} záznamů výkonu modelu za posledních {days} dní")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání výkonu modelu: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def plot_prediction_vs_actual(predictions_df: pd.DataFrame) -> None:
    """
    Vykreslí graf predikce vs. skutečnost.

    Args:
        predictions_df: DataFrame s predikcemi
    """
    if predictions_df.empty:
        logger.warning("Nelze vykreslit graf - DataFrame je prázdný")
        return

    # Kontrola, zda máme potřebné sloupce
    required_columns = ['timestamp', 'predicted_price']
    # Mapování názvů sloupců
    column_mapping = {
        'current_price': 'current_price',
        'actual_price': 'actual_price'
    }

    missing_columns = [col for col in required_columns if col not in predictions_df.columns]

    if missing_columns:
        logger.warning(f"Chybí sloupce pro vykreslení grafu: {missing_columns}")
        return

    try:
        plt.figure(figsize=(12, 6))

        # Vykreslení aktuální ceny
        plt.plot(predictions_df['timestamp'], predictions_df['current_price'],
                 label='Aktuální cena', color='blue')

        # Vykreslení predikované ceny
        plt.plot(predictions_df['timestamp'], predictions_df['predicted_price'],
                 label='Predikovaná cena', color='red', linestyle='--')

        # Vykreslení skutečné ceny (pokud je k dispozici)
        if 'actual_price' in predictions_df.columns and not predictions_df['actual_price'].isna().all():
            plt.plot(predictions_df['timestamp'], predictions_df['actual_price'],
                     label='Skutečná cena', color='green')

        plt.title('Predikce vs. Skutečnost')
        plt.xlabel('Datum a čas')
        plt.ylabel('Cena')
        plt.grid(True)
        plt.legend()

        # Formátování osy x
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=4))
        plt.xticks(rotation=45)

        plt.tight_layout()

        # Uložení grafu
        graph_path = os.path.join(os.getcwd(), 'prediction_vs_actual.png')
        plt.savefig(graph_path)
        logger.info(f"Graf predikce vs. skutečnost uložen jako: {graph_path}")
        print(f"Graf uložen do: {graph_path}")

        # Uložení grafu místo zobrazení
        # plt.show()  # Zakomentováno, aby se program nezastavil
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu predikce vs. skutečnost: {e}")

def plot_prediction_accuracy(predictions_df: pd.DataFrame) -> None:
    """
    Vykreslí graf přesnosti predikcí.

    Args:
        predictions_df: DataFrame s predikcemi
    """
    if predictions_df.empty:
        logger.warning("Nelze vykreslit graf - DataFrame je prázdný")
        return

    # Kontrola, zda máme potřebné sloupce
    if 'actual_price' not in predictions_df.columns or predictions_df['actual_price'].isna().all():
        logger.warning("Chybí sloupec 'actual_price' nebo všechny hodnoty jsou NaN")
        return

    try:
        # Výpočet chyby predikce
        predictions_df = predictions_df.dropna(subset=['actual_price'])
        predictions_df['error'] = predictions_df['predicted_price'] - predictions_df['actual_price']
        predictions_df['error_pct'] = (predictions_df['error'] / predictions_df['actual_price']) * 100

        # Výpočet směru predikce
        predictions_df['predicted_direction'] = np.sign(predictions_df['predicted_price'] - predictions_df['current_price'])
        predictions_df['actual_direction'] = np.sign(predictions_df['actual_price'] - predictions_df['current_price'])
        predictions_df['direction_correct'] = predictions_df['predicted_direction'] == predictions_df['actual_direction']

        # Výpočet klouzavého průměru přesnosti směru
        window_size = min(10, len(predictions_df))
        predictions_df['direction_accuracy_ma'] = predictions_df['direction_correct'].rolling(window=window_size).mean() * 100

        # Vykreslení grafu chyby
        plt.figure(figsize=(12, 10))

        # První subplot - absolutní chyba
        plt.subplot(3, 1, 1)
        plt.plot(predictions_df['timestamp'], predictions_df['error'], marker='o')
        plt.axhline(y=0, color='r', linestyle='-')
        plt.title('Absolutní chyba predikce')
        plt.xlabel('Datum a čas')
        plt.ylabel('Chyba (absolutní)')
        plt.grid(True)

        # Formátování osy x
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=4))
        plt.xticks(rotation=45)

        # Druhý subplot - procentuální chyba
        plt.subplot(3, 1, 2)
        plt.plot(predictions_df['timestamp'], predictions_df['error_pct'], marker='o')
        plt.axhline(y=0, color='r', linestyle='-')
        plt.title('Procentuální chyba predikce')
        plt.xlabel('Datum a čas')
        plt.ylabel('Chyba (%)')
        plt.grid(True)

        # Formátování osy x
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=4))
        plt.xticks(rotation=45)

        # Třetí subplot - přesnost směru
        plt.subplot(3, 1, 3)
        plt.plot(predictions_df['timestamp'], predictions_df['direction_accuracy_ma'], marker='o')
        plt.axhline(y=50, color='r', linestyle='-')
        plt.title('Přesnost predikce směru (klouzavý průměr)')
        plt.xlabel('Datum a čas')
        plt.ylabel('Přesnost (%)')
        plt.grid(True)
        plt.ylim(0, 100)

        # Formátování osy x
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=4))
        plt.xticks(rotation=45)

        plt.tight_layout()

        # Uložení grafu
        graph_path = os.path.join(os.getcwd(), 'prediction_accuracy.png')
        plt.savefig(graph_path)
        logger.info(f"Graf přesnosti predikcí uložen jako: {graph_path}")
        print(f"Graf uložen do: {graph_path}")

        # Uložení grafu místo zobrazení
        # plt.show()  # Zakomentováno, aby se program nezastavil

        # Výpis statistik
        mean_error = predictions_df['error'].mean()
        mean_abs_error = predictions_df['error'].abs().mean()
        mean_error_pct = predictions_df['error_pct'].mean()
        mean_abs_error_pct = predictions_df['error_pct'].abs().mean()
        direction_accuracy = predictions_df['direction_correct'].mean() * 100

        print("\nStatistiky přesnosti predikcí:")
        print(f"Průměrná chyba: {mean_error:.6f}")
        print(f"Průměrná absolutní chyba: {mean_abs_error:.6f}")
        print(f"Průměrná procentuální chyba: {mean_error_pct:.2f}%")
        print(f"Průměrná absolutní procentuální chyba: {mean_abs_error_pct:.2f}%")
        print(f"Přesnost predikce směru: {direction_accuracy:.2f}%")
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu přesnosti predikcí: {e}")

def plot_model_performance_metrics(performance_df: pd.DataFrame) -> None:
    """
    Vykreslí graf metrik výkonu modelu.

    Args:
        performance_df: DataFrame s historií výkonu modelu
    """
    if performance_df.empty:
        logger.warning("Nelze vykreslit graf - DataFrame je prázdný")
        return

    try:
        plt.figure(figsize=(12, 10))

        # První subplot - RMSE
        plt.subplot(3, 1, 1)
        plt.plot(performance_df['log_time'], performance_df['rmse'], marker='o')
        plt.title('RMSE v čase')
        plt.xlabel('Datum a čas')
        plt.ylabel('RMSE')
        plt.grid(True)

        # Formátování osy x
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=5))
        plt.xticks(rotation=45)

        # Druhý subplot - MAE
        plt.subplot(3, 1, 2)
        plt.plot(performance_df['log_time'], performance_df['mae'], marker='o')
        plt.title('MAE v čase')
        plt.xlabel('Datum a čas')
        plt.ylabel('MAE')
        plt.grid(True)

        # Formátování osy x
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=5))
        plt.xticks(rotation=45)

        # Třetí subplot - Průměrná procentuální chyba
        if 'avg_error_pct' in performance_df.columns:
            plt.subplot(3, 1, 3)
            plt.plot(performance_df['log_time'], performance_df['avg_error_pct'], marker='o')
            plt.title('Průměrná procentuální chyba v čase')
            plt.xlabel('Datum a čas')
            plt.ylabel('Chyba (%)')
            plt.grid(True)

            # Formátování osy x
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=5))
            plt.xticks(rotation=45)

        plt.tight_layout()

        # Uložení grafu
        graph_path = os.path.join(os.getcwd(), 'model_performance_metrics.png')
        plt.savefig(graph_path)
        logger.info(f"Graf metrik výkonu modelu uložen jako: {graph_path}")
        print(f"Graf uložen do: {graph_path}")

        # Uložení grafu místo zobrazení
        # plt.show()  # Zakomentováno, aby se program nezastavil
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu metrik výkonu modelu: {e}")

def plot_sentiment_vs_price(conn: pyodbc.Connection, days: int = 30) -> None:
    """
    Vykreslí graf sentimentu vs. cena.

    Args:
        conn: Připojení k databázi
        days: Počet dní historie
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Výpočet data
        start_date = datetime.now() - timedelta(days=days)

        # Načtení dat o sentimentu
        cursor.execute("""
        SELECT timestamp, combined_sentiment FROM EXTERNAL_SENTIMENT
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))

        sentiment_rows = cursor.fetchall()

        if not sentiment_rows:
            logger.warning(f"Nenalezena žádná data o sentimentu za posledních {days} dní")
            return

        # Vytvoření DataFrame pro sentiment
        sentiment_df = pd.DataFrame(sentiment_rows, columns=['timestamp', 'combined_sentiment'])
        sentiment_df['timestamp'] = pd.to_datetime(sentiment_df['timestamp'])

        # Načtení cenových dat
        cursor.execute("""
        SELECT timestamp, close_price FROM alch_price_history
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))

        price_rows = cursor.fetchall()

        if not price_rows:
            logger.warning(f"Nenalezena žádná cenová data za posledních {days} dní")
            return

        # Vytvoření DataFrame pro ceny
        price_df = pd.DataFrame(price_rows, columns=['timestamp', 'close_price'])
        price_df['timestamp'] = pd.to_datetime(price_df['timestamp'])

        # Kontrola, zda máme dostatek dat
        if len(sentiment_df) < 2 or len(price_df) < 2:
            logger.warning("Nedostatek dat pro vykreslení grafu sentimentu vs. cena")

            # Vytvoření jednoduchého grafu pouze s dostupnými daty
            plt.figure(figsize=(12, 6))

            # Vytvoření kopií DataFrame s resetovaným indexem pro bezpečné vykreslení
            if len(sentiment_df) > 0:
                sentiment_plot_df = sentiment_df.copy()
                if 'timestamp' not in sentiment_plot_df.columns:
                    sentiment_plot_df = sentiment_plot_df.reset_index()

                plt.subplot(2, 1, 1)
                plt.plot(sentiment_plot_df['timestamp'], sentiment_plot_df['combined_sentiment'], color='red')
                plt.title('Sentiment v čase')
                plt.ylabel('Sentiment')
                plt.grid(True)

            if len(price_df) > 0:
                price_plot_df = price_df.copy()
                if 'timestamp' not in price_plot_df.columns:
                    price_plot_df = price_plot_df.reset_index()

                plt.subplot(2, 1, 2)
                plt.plot(price_plot_df['timestamp'], price_plot_df['close_price'], color='blue')
                plt.title('Cena v čase')
                plt.ylabel('Cena')
                plt.grid(True)

            plt.tight_layout()

            # Uložení grafu
            graph_path = os.path.join(os.getcwd(), 'sentiment_vs_price.png')
            plt.savefig(graph_path)
            logger.info(f"Graf sentimentu vs. cena uložen jako: {graph_path}")
            print(f"Graf uložen do: {graph_path}")
            return

        # Resampling dat na stejnou frekvenci
        sentiment_df.set_index('timestamp', inplace=True)
        sentiment_resampled = sentiment_df.resample('1H').mean().ffill()

        price_df.set_index('timestamp', inplace=True)
        price_resampled = price_df.resample('1H').last().ffill()

        # Spojení dat
        combined_df = pd.merge(sentiment_resampled, price_resampled, left_index=True, right_index=True, how='inner')

        if combined_df.empty:
            logger.warning("Nelze vykreslit graf - žádná společná data")
            return

        # Vykreslení grafu
        fig, ax1 = plt.subplots(figsize=(12, 6))

        # Osa pro cenu
        ax1.set_xlabel('Datum')
        ax1.set_ylabel('Cena', color='tab:blue')
        ax1.plot(combined_df.index, combined_df['close_price'], color='tab:blue')
        ax1.tick_params(axis='y', labelcolor='tab:blue')

        # Formátování osy x
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.DayLocator(interval=5))
        plt.xticks(rotation=45)

        # Druhá osa pro sentiment
        ax2 = ax1.twinx()
        ax2.set_ylabel('Sentiment', color='tab:red')
        ax2.plot(combined_df.index, combined_df['combined_sentiment'], color='tab:red')
        ax2.tick_params(axis='y', labelcolor='tab:red')
        ax2.set_ylim(-1, 1)

        # Přidání mřížky
        ax1.grid(True)

        plt.title('Sentiment vs. Cena')
        plt.tight_layout()

        # Uložení grafu
        graph_path = os.path.join(os.getcwd(), 'sentiment_vs_price.png')
        plt.savefig(graph_path)
        logger.info(f"Graf sentimentu vs. cena uložen jako: {graph_path}")
        print(f"Graf uložen do: {graph_path}")

        # Uložení grafu místo zobrazení
        # plt.show()  # Zakomentováno, aby se program nezastavil
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu sentimentu vs. cena: {e}")
    finally:
        if cursor:
            cursor.close()

def update_actual_prices(conn: pyodbc.Connection) -> bool:
    """
    Aktualizuje skutečné ceny v tabulce predikcí.

    Args:
        conn: Připojení k databázi

    Returns:
        True pokud byla aktualizace úspěšná, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení predikcí bez skutečných cen
        cursor.execute("""
        SELECT id, timestamp, predicted_price
        FROM alch_predictions
        WHERE actual_price IS NULL AND timestamp < DATEADD(minute, -5, GETDATE())
        """)

        predictions = cursor.fetchall()

        if not predictions:
            logger.info("Žádné predikce k aktualizaci")
            return True

        logger.info(f"Nalezeno {len(predictions)} predikcí k aktualizaci")

        # Aktualizace skutečných cen
        for pred_id, pred_time, pred_price in predictions:
            # Výpočet času pro skutečnou cenu (5 minut po predikci)
            actual_time = pred_time + timedelta(minutes=5)

            # Načtení skutečné ceny
            cursor.execute("""
            SELECT TOP 1 close_price
            FROM alch_price_history
            WHERE timestamp >= ?
            ORDER BY timestamp ASC
            """, (actual_time,))

            actual_price_row = cursor.fetchone()

            if actual_price_row:
                actual_price = actual_price_row[0]

                # Výpočet chyby
                error_pct = abs(pred_price - actual_price) / actual_price * 100

                # Aktualizace záznamu
                cursor.execute("""
                UPDATE alch_predictions
                SET actual_price = ?, error_pct = ?
                WHERE id = ?
                """, (actual_price, error_pct, pred_id))

                logger.debug(f"Aktualizována predikce {pred_id}: skutečná cena = {actual_price}, chyba = {error_pct:.2f}%")

        conn.commit()
        logger.info("Skutečné ceny úspěšně aktualizovány")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při aktualizaci skutečných cen: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def run_dashboard(days: int = 7) -> None:
    """
    Spustí dashboard s vizualizací výsledků.

    Args:
        days: Počet dní historie k zobrazení
    """
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return

    try:
        # Aktualizace skutečných cen
        update_actual_prices(conn)

        # Načtení dat
        predictions_df = load_predictions(conn, days)
        performance_df = load_model_performance(conn, days * 4)  # Delší historie pro výkon modelu

        if predictions_df.empty:
            logger.warning("Žádné predikce k zobrazení")
        else:
            # Vykreslení grafů
            plot_prediction_vs_actual(predictions_df)
            plot_prediction_accuracy(predictions_df)

        if not performance_df.empty:
            plot_model_performance_metrics(performance_df)

        # Vykreslení grafu sentimentu vs. cena
        plot_sentiment_vs_price(conn, days)

        logger.info("Dashboard úspěšně spuštěn")
    except Exception as e:
        logger.error(f"Chyba při spouštění dashboardu: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    # Spuštění dashboardu
    run_dashboard(days=7)
