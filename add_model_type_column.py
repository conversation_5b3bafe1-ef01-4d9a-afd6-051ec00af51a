"""
Skript pro př<PERSON>ání sloupce model_type do tabulky PREDICTIONS
pro rozlišení mezi různými predikčními modely.
"""

import pyodbc

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

def add_model_type_column():
    """Přidá sloupec model_type do tabulky PREDICTIONS."""
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        print("🔧 Kontroluji existenci sloupce model_type...")
        
        # Zkontrolujeme, zda sloupec již existuje
        cursor.execute("""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'PREDICTIONS' 
        AND COLUMN_NAME = 'model_type'
        """)
        
        column_exists = cursor.fetchone()[0] > 0
        
        if column_exists:
            print("✅ Sloupec model_type již existuje.")
        else:
            print("➕ Přidávám sloupec model_type...")
            
            # Přidáme sloupec
            cursor.execute("""
            ALTER TABLE PREDICTIONS
            ADD model_type VARCHAR(50) NULL
            """)
            
            print("✅ Sloupec model_type byl úspěšně přidán.")
            
            # Aktualizujeme existující záznamy
            print("🔄 Aktualizuji existující záznamy...")
            cursor.execute("""
            UPDATE PREDICTIONS
            SET model_type = 'LEGACY'
            WHERE model_type IS NULL
            """)
            
            rows_updated = cursor.rowcount
            print(f"✅ Aktualizováno {rows_updated} existujících záznamů.")
        
        conn.commit()
        
        # Zobrazíme statistiky
        print("\n📊 Statistiky tabulky PREDICTIONS:")
        cursor.execute("""
        SELECT 
            ISNULL(model_type, 'NULL') as model_type,
            COUNT(*) as count
        FROM PREDICTIONS
        GROUP BY model_type
        ORDER BY count DESC
        """)
        
        stats = cursor.fetchall()
        for row in stats:
            print(f"   {row[0]}: {row[1]} záznamů")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Operace dokončena úspěšně!")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    add_model_type_column()
