"""
Skript pro aktualizaci predikcí a porovnání s reálnými výsledky.
"""
import os
import logging
import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Union, Optional

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def update_actual_prices(conn: pyodbc.Connection) -> bool:
    """
    Aktualizuje skutečné ceny v tabulce predikcí.

    Args:
        conn: Připojení k databázi

    Returns:
        True pokud byla aktualizace úspěšná, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení predikcí bez skutečných cen
        cursor.execute("""
        SELECT id, timestamp, predicted_price
        FROM dbo.alch_predictions
        WHERE actual_price IS NULL AND timestamp < DATEADD(minute, -5, GETDATE())
        """)

        predictions = cursor.fetchall()

        if not predictions:
            logger.info("Žádné predikce k aktualizaci")
            return True

        logger.info(f"Nalezeno {len(predictions)} predikcí k aktualizaci")

        # Aktualizace skutečných cen
        for pred_id, pred_time, pred_price in predictions:
            # Výpočet času pro skutečnou cenu (5 minut po predikci)
            actual_time = pred_time + timedelta(minutes=5)

            # Načtení skutečné ceny
            cursor.execute("""
            SELECT TOP 1 close_price
            FROM dbo.alch_price_history
            WHERE timestamp >= ?
            ORDER BY timestamp ASC
            """, (actual_time,))

            actual_price_row = cursor.fetchone()

            if actual_price_row:
                actual_price = actual_price_row[0]

                # Výpočet chyby
                error_pct = ((pred_price - actual_price) / actual_price) * 100

                # Aktualizace záznamu
                cursor.execute("""
                UPDATE dbo.alch_predictions
                SET actual_price = ?, error_pct = ?
                WHERE id = ?
                """, (actual_price, error_pct, pred_id))

                logger.info(f"Aktualizována predikce {pred_id}: skutečná cena = {actual_price}, chyba = {error_pct:.2f}%")

        conn.commit()
        logger.info("Skutečné ceny úspěšně aktualizovány")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při aktualizaci skutečných cen: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def calculate_prediction_metrics(conn: pyodbc.Connection, days: int = 7) -> Dict[str, float]:
    """
    Vypočítá metriky přesnosti predikcí.

    Args:
        conn: Připojení k databázi
        days: Počet dní historie

    Returns:
        Slovník s metrikami
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Výpočet data
        start_date = datetime.now() - timedelta(days=days)

        # Načtení predikcí s aktuálními cenami
        cursor.execute("""
        SELECT predicted_price, actual_price, current_price
        FROM dbo.alch_predictions
        WHERE timestamp >= ? AND actual_price IS NOT NULL
        """, (start_date,))

        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"Nenalezeny žádné predikce s aktuálními cenami za posledních {days} dní")
            return {}

        # Vytvoření DataFrame
        df = pd.DataFrame(rows, columns=['predicted_price', 'actual_price', 'current_price'])

        # Výpočet metrik
        df['error'] = df['predicted_price'] - df['actual_price']
        df['abs_error'] = df['error'].abs()
        df['error_pct'] = (df['error'] / df['actual_price']) * 100
        df['abs_error_pct'] = df['error_pct'].abs()

        # Výpočet směru predikce
        df['predicted_direction'] = np.sign(df['predicted_price'] - df['current_price'])
        df['actual_direction'] = np.sign(df['actual_price'] - df['current_price'])
        df['direction_correct'] = df['predicted_direction'] == df['actual_direction']

        # Výpočet metrik
        metrics = {
            'count': len(df),
            'mean_error': df['error'].mean(),
            'mean_abs_error': df['abs_error'].mean(),
            'mean_error_pct': df['error_pct'].mean(),
            'mean_abs_error_pct': df['abs_error_pct'].mean(),
            'direction_accuracy': df['direction_correct'].mean() * 100,
            'rmse': np.sqrt((df['error'] ** 2).mean()),
            'mae': df['abs_error'].mean()
        }

        logger.info(f"Vypočítány metriky pro {len(df)} predikcí za posledních {days} dní")
        return metrics
    except pyodbc.Error as e:
        logger.error(f"Chyba při výpočtu metrik predikcí: {e}")
        return {}
    finally:
        if cursor:
            cursor.close()

def log_model_performance(conn: pyodbc.Connection, metrics: Dict[str, float],
                        model_type: str = "XGBoost", market_condition: str = "unknown") -> bool:
    """
    Uloží metriky výkonu modelu do databáze.

    Args:
        conn: Připojení k databázi
        metrics: Slovník s metrikami
        model_type: Typ modelu
        market_condition: Tržní podmínky

    Returns:
        True pokud bylo uložení úspěšné, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Kontrola, zda tabulka existuje
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                      WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'MODEL_PERFORMANCE_LOG')
        BEGIN
            CREATE TABLE MODEL_PERFORMANCE_LOG (
                id INT IDENTITY(1,1) PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                log_time DATETIME2 NOT NULL,
                model_type VARCHAR(50) NOT NULL,
                market_condition VARCHAR(50) NULL,
                rmse FLOAT NULL,
                mae FLOAT NULL,
                avg_error_pct FLOAT NULL,
                predictions_count INT NULL,
                successful_predictions_count INT NULL,
                model_parameters NVARCHAR(MAX) NULL,
                features_used NVARCHAR(MAX) NULL
            );
            PRINT 'Tabulka MODEL_PERFORMANCE_LOG byla úspěšně vytvořena.';
        END
        """)

        # Výpočet počtu úspěšných predikcí
        predictions_count = metrics.get('count', 0)
        direction_accuracy = metrics.get('direction_accuracy', 0)
        successful_predictions_count = int(predictions_count * direction_accuracy / 100)

        # Vložení záznamu
        cursor.execute("""
        INSERT INTO MODEL_PERFORMANCE_LOG
        (symbol, log_time, model_type, market_condition, rmse, mae, avg_error_pct,
         predictions_count, successful_predictions_count)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "ALCHUSDT",
            datetime.now(),
            model_type,
            market_condition,
            metrics.get('rmse'),
            metrics.get('mae'),
            metrics.get('mean_abs_error_pct'),
            predictions_count,
            successful_predictions_count
        ))

        conn.commit()
        logger.info(f"Metriky výkonu modelu úspěšně uloženy")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při ukládání metrik výkonu modelu: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def get_current_market_condition(conn: pyodbc.Connection) -> str:
    """
    Získá aktuální tržní podmínky.

    Args:
        conn: Připojení k databázi

    Returns:
        Řetězec s tržními podmínkami
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení posledního záznamu z tabulky predikcí
        cursor.execute("""
        SELECT TOP 1 market_condition
        FROM alch_predictions
        ORDER BY timestamp DESC
        """)

        row = cursor.fetchone()

        if row and row[0]:
            return row[0]
        else:
            return "unknown"
    except pyodbc.Error as e:
        logger.error(f"Chyba při získávání tržních podmínek: {e}")
        return "unknown"
    finally:
        if cursor:
            cursor.close()

def main():
    """Hlavní funkce."""
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return

    try:
        # Aktualizace skutečných cen
        update_actual_prices(conn)

        # Výpočet metrik
        metrics = calculate_prediction_metrics(conn)

        if metrics:
            # Výpis metrik
            logger.info("Metriky přesnosti predikcí:")
            for key, value in metrics.items():
                logger.info(f"  {key}: {value}")

            # Získání aktuálních tržních podmínek
            market_condition = get_current_market_condition(conn)

            # Uložení metrik do databáze
            log_model_performance(conn, metrics, market_condition=market_condition)

        logger.info("Aktualizace predikcí úspěšně dokončena")
    except Exception as e:
        logger.error(f"Chyba při aktualizaci predikcí: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
