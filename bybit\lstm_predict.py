# lstm_predict.py
# LSTM predikce ceny do budoucna

import numpy as np
from sklearn.preprocessing import MinMaxScaler
from keras.models import Sequential
from keras.layers import LSTM, <PERSON><PERSON>

def predict_future_lstm(df, lookback=30, future_steps=20):
    if len(df) < (lookback + future_steps + 1):
        raise ValueError("Nedostatek dat pro LSTM predikci")
    close_data = df['close'].values.reshape(-1,1)
    scaler = MinMaxScaler(feature_range=(0,1))
    close_scaled = scaler.fit_transform(close_data)
    X, y = [], []
    for i in range(len(close_scaled) - lookback):
        X.append(close_scaled[i:i+lookback])
        y.append(close_scaled[i+lookback])
    X = np.array(X)
    y = np.array(y)
    model = Sequential()
    model.add(LSTM(50, input_shape=(lookback, 1)))
    model.add(Dense(1))
    model.compile(loss='mse', optimizer='adam')
    model.fit(X, y, epochs=20, batch_size=16, verbose=0)
    last_sequence = close_scaled[-lookback:]
    future_preds = []
    curr_sequence = last_sequence.copy()
    for _ in range(future_steps):
        pred = model.predict(curr_sequence.reshape(1, lookback, 1), verbose=0)
        future_preds.append(pred[0,0])
        curr_sequence = np.vstack([curr_sequence[1:], pred])
    future_preds = scaler.inverse_transform(np.array(future_preds).reshape(-1,1)).flatten()
    return future_preds
