"""
Modul s implement<PERSON><PERSON> r<PERSON>ch obchodních strategií pro backtesting.
"""
import os
import logging
import pandas as pd
import numpy as np
import joblib
import xgboost as xgb
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Union, Optional

from backtest import BacktestStrategy
from ensemble_models import EnsembleModel

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PredictionModelStrategy(BacktestStrategy):
    """Strategie založená na predikčním modelu."""
    
    def __init__(self, model_path: str, threshold: float = 0.01, 
                 features: List[str] = None, initial_balance: float = 1000.0):
        """
        Inicializace strategie založené na predikčním modelu.
        
        Args:
            model_path: Cesta k souboru modelu
            threshold: Práh pro generování signá<PERSON>ů (jako desetinn<PERSON> č<PERSON>)
            features: Seznam příznaků pro model
            initial_balance: Počáteční zůstatek
        """
        super().__init__(f"PredictionModel_{os.path.basename(model_path)}", initial_balance)
        
        # Načtení modelu
        self.model = joblib.load(model_path)
        
        # Nastavení prahu
        self.threshold = threshold
        
        # Seznam příznaků
        if features is None:
            # Výchozí příznaky
            self.features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
        else:
            self.features = features
        
        logger.info(f"Inicializována strategie založená na modelu {model_path} s prahem {threshold}")
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generuje obchodní signály na základě predikce modelu.
        
        Args:
            data: DataFrame s daty
            
        Returns:
            DataFrame s přidanými signály
        """
        # Kopie dat
        df = data.copy()
        
        # Kontrola, zda máme všechny potřebné příznaky
        missing_features = [f for f in self.features if f not in df.columns]
        if missing_features:
            logger.error(f"Chybí příznaky pro model: {missing_features}")
            return df
        
        # Predikce
        try:
            X = df[self.features]
            
            # Predikce ceny za 5 minut
            df['predicted_price'] = self.model.predict(X)
            
            # Výpočet očekávané změny ceny
            df['predicted_change'] = (df['predicted_price'] - df['close_price']) / df['close_price']
            
            # Generování signálů
            df['signal'] = 0  # Výchozí hodnota (žádný signál)
            
            # Signál pro long pozici (očekávaný růst nad práh)
            df.loc[df['predicted_change'] > self.threshold, 'signal'] = 1
            
            # Signál pro short pozici (očekávaný pokles pod práh)
            df.loc[df['predicted_change'] < -self.threshold, 'signal'] = -1
            
            logger.info(f"Vygenerováno {len(df[df['signal'] != 0])} signálů z {len(df)} řádků dat")
            return df
        except Exception as e:
            logger.error(f"Chyba při generování signálů: {e}")
            return df

class EnsembleModelStrategy(BacktestStrategy):
    """Strategie založená na ensemble modelu."""
    
    def __init__(self, model_path: str, threshold: float = 0.01, 
                 features: List[str] = None, initial_balance: float = 1000.0):
        """
        Inicializace strategie založené na ensemble modelu.
        
        Args:
            model_path: Cesta k souboru ensemble modelu
            threshold: Práh pro generování signálů (jako desetinné číslo)
            features: Seznam příznaků pro model
            initial_balance: Počáteční zůstatek
        """
        super().__init__(f"EnsembleModel_{os.path.basename(model_path)}", initial_balance)
        
        # Načtení modelu
        self.model = EnsembleModel.load(model_path)
        
        # Nastavení prahu
        self.threshold = threshold
        
        # Seznam příznaků
        if features is None:
            # Výchozí příznaky
            self.features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
        else:
            self.features = features
        
        logger.info(f"Inicializována strategie založená na ensemble modelu {model_path} s prahem {threshold}")
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generuje obchodní signály na základě predikce ensemble modelu.
        
        Args:
            data: DataFrame s daty
            
        Returns:
            DataFrame s přidanými signály
        """
        # Kopie dat
        df = data.copy()
        
        # Kontrola, zda máme všechny potřebné příznaky
        missing_features = [f for f in self.features if f not in df.columns]
        if missing_features:
            logger.error(f"Chybí příznaky pro model: {missing_features}")
            return df
        
        # Predikce
        try:
            X = df[self.features]
            
            # Predikce ceny za 5 minut
            df['predicted_price'] = self.model.predict(X)
            
            # Výpočet očekávané změny ceny
            df['predicted_change'] = (df['predicted_price'] - df['close_price']) / df['close_price']
            
            # Generování signálů
            df['signal'] = 0  # Výchozí hodnota (žádný signál)
            
            # Signál pro long pozici (očekávaný růst nad práh)
            df.loc[df['predicted_change'] > self.threshold, 'signal'] = 1
            
            # Signál pro short pozici (očekávaný pokles pod práh)
            df.loc[df['predicted_change'] < -self.threshold, 'signal'] = -1
            
            logger.info(f"Vygenerováno {len(df[df['signal'] != 0])} signálů z {len(df)} řádků dat")
            return df
        except Exception as e:
            logger.error(f"Chyba při generování signálů: {e}")
            return df

class SentimentStrategy(BacktestStrategy):
    """Strategie založená na sentimentu trhu."""
    
    def __init__(self, threshold: float = 0.3, initial_balance: float = 1000.0):
        """
        Inicializace strategie založené na sentimentu.
        
        Args:
            threshold: Práh pro generování signálů (jako desetinné číslo)
            initial_balance: Počáteční zůstatek
        """
        super().__init__(f"Sentiment_Strategy_T{threshold}", initial_balance)
        
        # Nastavení prahu
        self.threshold = threshold
        
        logger.info(f"Inicializována strategie založená na sentimentu s prahem {threshold}")
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generuje obchodní signály na základě sentimentu trhu.
        
        Args:
            data: DataFrame s daty
            
        Returns:
            DataFrame s přidanými signály
        """
        # Kopie dat
        df = data.copy()
        
        # Kontrola, zda máme sloupec se sentimentem
        if 'combined_sentiment' not in df.columns:
            logger.error("Chybí sloupec 'combined_sentiment' v datech")
            return df
        
        # Generování signálů
        df['signal'] = 0  # Výchozí hodnota (žádný signál)
        
        # Signál pro long pozici (pozitivní sentiment nad práh)
        df.loc[df['combined_sentiment'] > self.threshold, 'signal'] = 1
        
        # Signál pro short pozici (negativní sentiment pod práh)
        df.loc[df['combined_sentiment'] < -self.threshold, 'signal'] = -1
        
        logger.info(f"Vygenerováno {len(df[df['signal'] != 0])} signálů z {len(df)} řádků dat")
        return df

class CombinedStrategy(BacktestStrategy):
    """Strategie kombinující predikční model a sentiment."""
    
    def __init__(self, model_path: str, model_threshold: float = 0.01, 
                 sentiment_threshold: float = 0.3, features: List[str] = None, 
                 initial_balance: float = 1000.0):
        """
        Inicializace kombinované strategie.
        
        Args:
            model_path: Cesta k souboru modelu
            model_threshold: Práh pro generování signálů z modelu
            sentiment_threshold: Práh pro generování signálů ze sentimentu
            features: Seznam příznaků pro model
            initial_balance: Počáteční zůstatek
        """
        super().__init__(f"Combined_Strategy_{os.path.basename(model_path)}", initial_balance)
        
        # Načtení modelu
        self.model = joblib.load(model_path)
        
        # Nastavení prahů
        self.model_threshold = model_threshold
        self.sentiment_threshold = sentiment_threshold
        
        # Seznam příznaků
        if features is None:
            # Výchozí příznaky
            self.features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
        else:
            self.features = features
        
        logger.info(f"Inicializována kombinovaná strategie s modelem {model_path}")
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generuje obchodní signály na základě kombinace predikce modelu a sentimentu.
        
        Args:
            data: DataFrame s daty
            
        Returns:
            DataFrame s přidanými signály
        """
        # Kopie dat
        df = data.copy()
        
        # Kontrola, zda máme všechny potřebné příznaky a sentiment
        missing_features = [f for f in self.features if f not in df.columns]
        if missing_features:
            logger.error(f"Chybí příznaky pro model: {missing_features}")
            return df
        
        if 'combined_sentiment' not in df.columns:
            logger.error("Chybí sloupec 'combined_sentiment' v datech")
            return df
        
        # Predikce
        try:
            X = df[self.features]
            
            # Predikce ceny za 5 minut
            df['predicted_price'] = self.model.predict(X)
            
            # Výpočet očekávané změny ceny
            df['predicted_change'] = (df['predicted_price'] - df['close_price']) / df['close_price']
            
            # Generování signálů
            df['signal'] = 0  # Výchozí hodnota (žádný signál)
            
            # Signál pro long pozici (očekávaný růst nad práh A pozitivní sentiment)
            long_condition = (df['predicted_change'] > self.model_threshold) & (df['combined_sentiment'] > self.sentiment_threshold)
            df.loc[long_condition, 'signal'] = 1
            
            # Signál pro short pozici (očekávaný pokles pod práh A negativní sentiment)
            short_condition = (df['predicted_change'] < -self.model_threshold) & (df['combined_sentiment'] < -self.sentiment_threshold)
            df.loc[short_condition, 'signal'] = -1
            
            logger.info(f"Vygenerováno {len(df[df['signal'] != 0])} signálů z {len(df)} řádků dat")
            return df
        except Exception as e:
            logger.error(f"Chyba při generování signálů: {e}")
            return df

def run_backtest(strategy: BacktestStrategy, data: pd.DataFrame, 
                commission: float = 0.001) -> Dict[str, Any]:
    """
    Spustí backtesting strategie na datech.
    
    Args:
        strategy: Instance strategie
        data: DataFrame s daty
        commission: Poplatek za obchod (jako desetinné číslo)
        
    Returns:
        Slovník s výsledky backtestu
    """
    logger.info(f"Spouštím backtesting strategie {strategy.name} na {len(data)} řádcích dat")
    
    # Provedení backtestu
    results = strategy.execute_backtest(data, commission)
    
    # Výpis výsledků
    logger.info(f"Výsledky backtestu strategie {strategy.name}:")
    logger.info(f"  Počáteční zůstatek: 1000.00")
    logger.info(f"  Konečný zůstatek: {results['final_balance']:.2f}")
    logger.info(f"  Celkový výnos: {results['metrics']['total_return']:.2f}%")
    logger.info(f"  Počet obchodů: {results['metrics']['total_trades']}")
    logger.info(f"  Úspěšnost: {results['metrics']['win_rate']*100:.2f}%")
    logger.info(f"  Průměrný zisk: {results['metrics']['avg_profit']:.2f}")
    logger.info(f"  Průměrná ztráta: {results['metrics']['avg_loss']:.2f}")
    logger.info(f"  Profit faktor: {results['metrics']['profit_factor']:.2f}")
    logger.info(f"  Maximální drawdown: {results['metrics']['max_drawdown']*100:.2f}%")
    logger.info(f"  Sharpe ratio: {results['metrics']['sharpe_ratio']:.2f}")
    
    return results

def compare_strategies(strategies: List[BacktestStrategy], data: pd.DataFrame, 
                      commission: float = 0.001) -> pd.DataFrame:
    """
    Porovná výkon různých strategií.
    
    Args:
        strategies: Seznam strategií
        data: DataFrame s daty
        commission: Poplatek za obchod (jako desetinné číslo)
        
    Returns:
        DataFrame s porovnáním výkonu strategií
    """
    results = []
    
    for strategy in strategies:
        # Provedení backtestu
        backtest_results = strategy.execute_backtest(data, commission)
        
        # Přidání výsledků do seznamu
        results.append({
            'strategy_name': strategy.name,
            'final_balance': backtest_results['final_balance'],
            'total_return': backtest_results['metrics']['total_return'],
            'total_trades': backtest_results['metrics']['total_trades'],
            'win_rate': backtest_results['metrics']['win_rate'],
            'profit_factor': backtest_results['metrics']['profit_factor'],
            'max_drawdown': backtest_results['metrics']['max_drawdown'],
            'sharpe_ratio': backtest_results['metrics']['sharpe_ratio']
        })
    
    # Vytvoření DataFrame
    results_df = pd.DataFrame(results)
    
    # Seřazení podle celkového výnosu
    results_df = results_df.sort_values('total_return', ascending=False)
    
    return results_df

def plot_equity_curves(results_dict: Dict[str, Dict[str, Any]]) -> None:
    """
    Vykreslí křivky equity pro různé strategie.
    
    Args:
        results_dict: Slovník s výsledky backtestů pro různé strategie
    """
    import matplotlib.pyplot as plt
    
    plt.figure(figsize=(12, 6))
    
    for strategy_name, results in results_dict.items():
        equity_curve = results['equity_curve']
        plt.plot(equity_curve.index, equity_curve.values, label=strategy_name)
    
    plt.title('Porovnání výkonu strategií')
    plt.xlabel('Datum')
    plt.ylabel('Equity')
    plt.grid(True)
    plt.legend()
    
    # Uložení grafu
    plt.savefig('strategy_comparison.png')
    logger.info("Graf porovnání strategií uložen jako 'strategy_comparison.png'")
    
    # Zobrazení grafu
    plt.show()

if __name__ == "__main__":
    # Příklad použití
    import pyodbc
    from backtest import connect_to_db, load_historical_data, load_historical_indicators, load_historical_sentiment, prepare_backtest_data
    
    # Připojení k databázi
    conn = connect_to_db()
    if conn:
        # Načtení historických dat
        symbol = "ALCHUSDT"
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)
        
        price_df = load_historical_data(conn, symbol, start_date, end_date)
        indicators_df = load_historical_indicators(conn, start_date, end_date)
        sentiment_df = load_historical_sentiment(conn, symbol, start_date, end_date)
        
        # Příprava dat pro backtesting
        data = prepare_backtest_data(price_df, indicators_df, sentiment_df)
        
        if not data.empty:
            # Vytvoření strategií
            model_strategy = PredictionModelStrategy("alch_price_model.pkl", threshold=0.01)
            sentiment_strategy = SentimentStrategy(threshold=0.3)
            combined_strategy = CombinedStrategy("alch_price_model.pkl", model_threshold=0.01, sentiment_threshold=0.3)
            
            # Porovnání strategií
            strategies = [model_strategy, sentiment_strategy, combined_strategy]
            comparison = compare_strategies(strategies, data)
            
            print("\nPorovnání strategií:")
            print(comparison)
            
            # Provedení backtestů a uložení výsledků
            results_dict = {}
            for strategy in strategies:
                results = run_backtest(strategy, data)
                results_dict[strategy.name] = results
            
            # Vykreslení křivek equity
            plot_equity_curves(results_dict)
        
        conn.close()
