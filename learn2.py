import pyodbc
import pandas as pd
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, classification_report
import joblib
import numpy as np
from sklearn.utils import resample

# --- pripojeni k databazi ---
conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"  # Například "*************\\SQLEXPRESS"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)
conn = pyodbc.connect(conn_str)

# --- nacteni dat z tabulky alch_indikatory ---
query = "SELECT * FROM alch_indikatory ORDER BY timestamp ASC"
df = pd.read_sql(query, conn)
conn.close()

# --- dopo<PERSON>tani pokrocilych vlastnosti (feature engineering) ---
df['momentum'] = df['close_price'] - df['close_price'].shift(5)
df['price_change_pct'] = df['close_price'].pct_change()
df['rsi_slope'] = df['RSI'].diff()
df['ema_diff'] = df['EMA9'] - df['EMA20']

# cilova hodnota: bude rust/klesat/stagnovat?
df['future_price'] = df['close_price'].shift(-5)
df['price_diff'] = df['future_price'] - df['close_price']

# Prevod na 0/1/2 (misto -1/0/1), jak to XGBoost ocekava
def klasifikuj(x):
    if x < -0.001:
        return 0  # Pokles
    elif x > 0.001:
        return 2  # Rust
    else:
        return 1  # Stagnace

df['target_class'] = df['price_diff'].apply(klasifikuj)

# odstran NaN
features = ['RSI', 'EMA9', 'EMA20', 'boll_high', 'boll_low', 'ATR', 'momentum', 'price_change_pct', 'rsi_slope', 'ema_diff']
df = df.dropna(subset=features + ['target_class'])

# --- vyvazeni trid s kontrolou minimalniho poctu ---
df_downsampled = pd.DataFrame()
min_samples = df['target_class'].value_counts().min()
min_samples = min(min_samples, 300)

for cls in [0, 1, 2]:
    subset = df[df['target_class'] == cls]
    if len(subset) >= min_samples:
        subset_balanced = resample(subset, replace=False, n_samples=min_samples, random_state=42)
        df_downsampled = pd.concat([df_downsampled, subset_balanced])

X = df_downsampled[features]
y = df_downsampled['target_class']

# --- rozdeleni na trenovaci a testovaci sadu ---
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, shuffle=True, stratify=y, random_state=42
)

# --- trenink XGBoost klasifikatoru ---
model = xgb.XGBClassifier(
    n_estimators=300,
    learning_rate=0.1,
    max_depth=4,
    objective='multi:softprob',
    num_class=3,
    random_state=42
)

model.fit(X_train, y_train)

# --- vyhodnoceni ---
y_pred = model.predict(X_test)
print("\n=== Klasifikacni report ===")
print(classification_report(y_test, y_pred, target_names=['Pokles', 'Stagnace', 'Rust']))

# --- ulozeni modelu ---
joblib.dump(model, "alch_class_model.pkl")
print("\nModel klasifikace ulozen jako alch_class_model.pkl")