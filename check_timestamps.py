"""
Skript pro kontrolu časových značek v databázi.
"""
import os
import logging
import pyodbc
from datetime import datetime, timedelta
import pytz

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

# Nastavení časových zón
UTC = pytz.UTC
LOCAL_TZ = pytz.timezone('Europe/Prague')  # Česká časová zóna (UTC+1/UTC+2)

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def check_timestamps():
    """Zkontroluje časové značky v databázi."""
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return

    cursor = None
    try:
        cursor = conn.cursor()

        # Kontrola časových značek v tabulce alch_indikatory
        cursor.execute("""
        SELECT TOP 10 timestamp, close_price
        FROM dbo.alch_indikatory
        ORDER BY timestamp DESC
        """)

        rows = cursor.fetchall()

        print("Poslední indikátory:")
        for row in rows:
            timestamp, close_price = row
            print(f"  Timestamp: {timestamp}, Close Price: {close_price}")

            # Pokus o konverzi na lokální čas
            try:
                # Předpokládáme, že timestamp je již v lokálním čase (UTC+2)
                local_time = LOCAL_TZ.localize(timestamp)
                utc_time = local_time.astimezone(UTC)
                print(f"  Lokální: {local_time}, UTC: {utc_time}")
            except Exception as e:
                print(f"  Nelze konvertovat čas: {e}")

        # Kontrola aktuálního času serveru
        cursor.execute("SELECT GETDATE() AS server_time")
        server_time = cursor.fetchone()[0]
        print(f"\nAktuální čas serveru: {server_time}")

        # Kontrola časové zóny serveru
        cursor.execute("SELECT SYSDATETIMEOFFSET() AS server_time_with_offset")
        server_time_with_offset = cursor.fetchone()[0]
        print(f"Aktuální čas serveru s offsetem: {server_time_with_offset}")

        # Kontrola časových značek v tabulce alch_predictions
        cursor.execute("""
        SELECT TOP 10 timestamp, current_price, predicted_price
        FROM dbo.alch_predictions
        ORDER BY timestamp DESC
        """)

        rows = cursor.fetchall()

        print("\nPoslední predikce:")
        for row in rows:
            timestamp, current_price, predicted_price = row
            print(f"  Timestamp: {timestamp}, Current Price: {current_price}, Predicted Price: {predicted_price}")

            # Pokus o konverzi na lokální čas
            try:
                # Předpokládáme, že timestamp je již v lokálním čase (UTC+2)
                local_time = LOCAL_TZ.localize(timestamp)
                utc_time = local_time.astimezone(UTC)
                print(f"  Lokální: {local_time}, UTC: {utc_time}")
            except Exception as e:
                print(f"  Nelze konvertovat čas: {e}")

        # Kontrola aktuálního času v Pythonu
        now_utc = datetime.now(UTC)
        now_local = datetime.now(LOCAL_TZ)
        print(f"\nAktuální čas v Pythonu (UTC): {now_utc}")
        print(f"Aktuální čas v Pythonu (Lokální): {now_local}")

    except pyodbc.Error as e:
        logger.error(f"Chyba při kontrole časových značek: {e}")
    finally:
        if cursor:
            cursor.close()
        conn.close()

if __name__ == "__main__":
    check_timestamps()
