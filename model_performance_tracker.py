"""
Modul pro sledování výkonu modelu v čase.
Ukládá metriky výkonu do databáze a poskytuje funkce pro jejich analýzu.
"""
import os
import logging
import pandas as pd
import numpy as np
import pyodbc
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def create_performance_table(conn: pyodbc.Connection) -> bool:
    """Vytvoří tabulku pro ukládání metrik výkonu modelu."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
                      WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'MODEL_PERFORMANCE_LOG')
        BEGIN
            CREATE TABLE MODEL_PERFORMANCE_LOG (
                id INT IDENTITY(1,1) PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                log_time DATETIME2 NOT NULL,
                model_type VARCHAR(50) NOT NULL,
                market_condition VARCHAR(50) NULL,
                rmse FLOAT NULL,
                mae FLOAT NULL,
                avg_error_pct FLOAT NULL,
                predictions_count INT NULL,
                successful_predictions_count INT NULL,
                model_parameters NVARCHAR(MAX) NULL,
                features_used NVARCHAR(MAX) NULL,
                
                -- Indexy pro rychlejší vyhledávání
                INDEX idx_perf_symbol (symbol),
                INDEX idx_perf_log_time (log_time),
                INDEX idx_perf_model_type (model_type)
            );
            PRINT 'Tabulka MODEL_PERFORMANCE_LOG byla úspěšně vytvořena.';
        END
        """)
        conn.commit()
        logger.info("Tabulka MODEL_PERFORMANCE_LOG byla ověřena/vytvořena.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při vytváření tabulky MODEL_PERFORMANCE_LOG: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def log_model_performance(conn: pyodbc.Connection, 
                         symbol: str, 
                         model_type: str,
                         market_condition: str,
                         metrics: Dict[str, float],
                         model_parameters: Dict[str, Any] = None,
                         features_used: List[str] = None) -> bool:
    """
    Uloží metriky výkonu modelu do databáze.
    
    Args:
        conn: Připojení k databázi
        symbol: Symbol, pro který byl model trénován
        model_type: Typ modelu (např. 'XGBoost', 'Ensemble', 'LSTM')
        market_condition: Tržní podmínky (např. 'volatile_bullish', 'stable_bearish')
        metrics: Slovník s metrikami výkonu (rmse, mae, avg_error_pct, atd.)
        model_parameters: Parametry modelu (volitelné)
        features_used: Seznam použitých příznaků (volitelné)
        
    Returns:
        True pokud bylo logování úspěšné, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Převod parametrů a příznaků na string
        model_params_str = str(model_parameters) if model_parameters else None
        features_str = str(features_used) if features_used else None
        
        # Aktuální čas
        log_time = datetime.now()
        
        # Vložení záznamu
        cursor.execute("""
        INSERT INTO MODEL_PERFORMANCE_LOG 
        (symbol, log_time, model_type, market_condition, rmse, mae, avg_error_pct, 
         predictions_count, successful_predictions_count, model_parameters, features_used)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            log_time,
            model_type,
            market_condition,
            metrics.get('rmse'),
            metrics.get('mae'),
            metrics.get('avg_error_pct'),
            metrics.get('predictions_count'),
            metrics.get('successful_predictions_count'),
            model_params_str,
            features_str
        ))
        
        conn.commit()
        logger.info(f"Výkon modelu {model_type} pro {symbol} byl úspěšně zalogován.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při logování výkonu modelu: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def get_model_performance_history(conn: pyodbc.Connection, 
                                 symbol: str = None, 
                                 model_type: str = None,
                                 days: int = 30) -> pd.DataFrame:
    """
    Získá historii výkonu modelu z databáze.
    
    Args:
        conn: Připojení k databázi
        symbol: Symbol pro filtrování (volitelné)
        model_type: Typ modelu pro filtrování (volitelné)
        days: Počet dní historie k načtení
        
    Returns:
        DataFrame s historií výkonu modelu
    """
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Sestavení dotazu
        query = """
        SELECT * FROM MODEL_PERFORMANCE_LOG
        WHERE log_time >= DATEADD(day, ?, GETDATE())
        """
        params = [-days]
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        if model_type:
            query += " AND model_type = ?"
            params.append(model_type)
        
        query += " ORDER BY log_time DESC"
        
        # Provedení dotazu
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        if not rows:
            logger.info(f"Nenalezena žádná historie výkonu modelu pro zadané parametry.")
            return pd.DataFrame()
        
        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)
        
        # Konverze datumů
        df['log_time'] = pd.to_datetime(df['log_time'])
        
        logger.info(f"Načteno {len(df)} záznamů historie výkonu modelu.")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při získávání historie výkonu modelu: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def plot_model_performance(performance_df: pd.DataFrame, metric: str = 'avg_error_pct') -> None:
    """
    Vykreslí graf výkonu modelu v čase.
    
    Args:
        performance_df: DataFrame s historií výkonu modelu
        metric: Metrika k vykreslení ('rmse', 'mae', 'avg_error_pct')
    """
    if performance_df.empty:
        logger.warning("Nelze vykreslit graf - DataFrame je prázdný.")
        return
    
    if metric not in performance_df.columns:
        logger.warning(f"Metrika '{metric}' není v DataFrame.")
        return
    
    try:
        plt.figure(figsize=(12, 6))
        
        # Pokud máme více modelů, vykreslíme je různými barvami
        if 'model_type' in performance_df.columns:
            for model_type, group in performance_df.groupby('model_type'):
                plt.plot(group['log_time'], group[metric], label=model_type, marker='o')
        else:
            plt.plot(performance_df['log_time'], performance_df[metric], marker='o')
        
        plt.title(f'Vývoj metriky {metric} v čase')
        plt.xlabel('Datum a čas')
        plt.ylabel(metric)
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Uložení grafu
        plt.savefig(f'model_performance_{metric}.png')
        logger.info(f"Graf výkonu modelu uložen jako 'model_performance_{metric}.png'")
        
        # Zobrazení grafu
        plt.show()
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu výkonu modelu: {e}")

def analyze_model_performance(performance_df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyzuje výkon modelu a poskytuje statistiky.
    
    Args:
        performance_df: DataFrame s historií výkonu modelu
        
    Returns:
        Slovník se statistikami výkonu
    """
    if performance_df.empty:
        logger.warning("Nelze analyzovat výkon - DataFrame je prázdný.")
        return {}
    
    try:
        # Základní statistiky
        stats = {}
        
        # Průměrné metriky
        for metric in ['rmse', 'mae', 'avg_error_pct']:
            if metric in performance_df.columns:
                stats[f'avg_{metric}'] = performance_df[metric].mean()
                stats[f'min_{metric}'] = performance_df[metric].min()
                stats[f'max_{metric}'] = performance_df[metric].max()
                stats[f'std_{metric}'] = performance_df[metric].std()
        
        # Úspěšnost predikcí
        if 'predictions_count' in performance_df.columns and 'successful_predictions_count' in performance_df.columns:
            total_predictions = performance_df['predictions_count'].sum()
            successful_predictions = performance_df['successful_predictions_count'].sum()
            
            if total_predictions > 0:
                stats['success_rate'] = successful_predictions / total_predictions * 100
            else:
                stats['success_rate'] = 0
        
        # Analýza podle tržních podmínek
        if 'market_condition' in performance_df.columns:
            market_condition_stats = {}
            for condition, group in performance_df.groupby('market_condition'):
                if 'avg_error_pct' in group.columns:
                    market_condition_stats[condition] = {
                        'avg_error_pct': group['avg_error_pct'].mean(),
                        'count': len(group)
                    }
            stats['market_condition_stats'] = market_condition_stats
        
        # Trend výkonu (zlepšuje se nebo zhoršuje?)
        if 'log_time' in performance_df.columns and 'avg_error_pct' in performance_df.columns:
            # Seřadíme podle času
            sorted_df = performance_df.sort_values('log_time')
            
            # Rozdělíme na první a druhou polovinu
            half_idx = len(sorted_df) // 2
            first_half = sorted_df.iloc[:half_idx]
            second_half = sorted_df.iloc[half_idx:]
            
            if not first_half.empty and not second_half.empty:
                first_half_avg = first_half['avg_error_pct'].mean()
                second_half_avg = second_half['avg_error_pct'].mean()
                
                stats['performance_trend'] = 'improving' if second_half_avg < first_half_avg else 'worsening'
                stats['performance_change_pct'] = (first_half_avg - second_half_avg) / first_half_avg * 100
        
        return stats
    except Exception as e:
        logger.error(f"Chyba při analýze výkonu modelu: {e}")
        return {}

def get_best_model_for_condition(conn: pyodbc.Connection, 
                               symbol: str, 
                               market_condition: str) -> Tuple[str, Dict[str, Any]]:
    """
    Najde nejlepší model pro dané tržní podmínky.
    
    Args:
        conn: Připojení k databázi
        symbol: Symbol, pro který hledáme model
        market_condition: Tržní podmínky
        
    Returns:
        Tuple (typ modelu, parametry modelu)
    """
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Najdeme model s nejnižší průměrnou chybou pro dané podmínky
        query = """
        SELECT TOP 1 model_type, model_parameters, AVG(avg_error_pct) as avg_error
        FROM MODEL_PERFORMANCE_LOG
        WHERE symbol = ? AND market_condition = ?
        GROUP BY model_type, model_parameters
        ORDER BY avg_error ASC
        """
        
        cursor.execute(query, (symbol, market_condition))
        row = cursor.fetchone()
        
        if row:
            model_type = row[0]
            model_parameters_str = row[1]
            
            # Převod string parametrů zpět na slovník
            try:
                import ast
                model_parameters = ast.literal_eval(model_parameters_str) if model_parameters_str else {}
            except:
                model_parameters = {}
            
            logger.info(f"Nalezen nejlepší model typu {model_type} pro podmínky {market_condition}")
            return model_type, model_parameters
        else:
            logger.info(f"Nenalezen žádný model pro podmínky {market_condition}, vracím výchozí hodnoty")
            return "XGBoost", {}
    except pyodbc.Error as e:
        logger.error(f"Chyba při hledání nejlepšího modelu: {e}")
        return "XGBoost", {}
    finally:
        if cursor:
            cursor.close()

if __name__ == "__main__":
    # Test funkcionality
    conn = connect_to_db()
    if conn:
        # Vytvoření tabulky
        create_performance_table(conn)
        
        # Příklad logování výkonu
        metrics = {
            'rmse': 0.0123,
            'mae': 0.0098,
            'avg_error_pct': 1.25,
            'predictions_count': 100,
            'successful_predictions_count': 85
        }
        
        log_model_performance(
            conn,
            symbol="ALCHUSDT",
            model_type="XGBoost",
            market_condition="stable_bullish",
            metrics=metrics,
            model_parameters={'n_estimators': 200, 'learning_rate': 0.05},
            features_used=['RSI', 'EMA9', 'EMA20', 'MACD']
        )
        
        # Získání historie výkonu
        history_df = get_model_performance_history(conn, symbol="ALCHUSDT", days=30)
        
        # Analýza výkonu
        stats = analyze_model_performance(history_df)
        print("Statistiky výkonu modelu:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Vykreslení grafu
        if not history_df.empty:
            plot_model_performance(history_df)
        
        conn.close()
