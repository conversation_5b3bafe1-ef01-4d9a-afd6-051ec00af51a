<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOLUSDT Iframe Showcase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .showcase-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .showcase-item {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .showcase-item:hover {
            transform: translateY(-5px);
        }
        
        .showcase-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .showcase-header h2 {
            margin: 0;
            font-size: 1.5em;
        }
        
        .showcase-header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 0.9em;
        }
        
        .iframe-container {
            position: relative;
            height: 400px;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        .full-width .iframe-container {
            height: 600px;
        }
        
        .features {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-top: 30px;
        }
        
        .features h2 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        
        .feature-item h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .code-example pre {
            margin: 0;
            font-family: 'Courier New', monospace;
        }
        
        /* Responsive design */
        @media (max-width: 1024px) {
            .showcase-grid {
                grid-template-columns: 1fr;
            }
            
            .iframe-container {
                height: 350px;
            }
            
            .full-width .iframe-container {
                height: 500px;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .iframe-container {
                height: 300px;
            }
            
            .full-width .iframe-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 SOLUSDT Iframe Showcase</h1>
        
        <div class="showcase-grid">
            <!-- Plná verze -->
            <div class="showcase-item full-width">
                <div class="showcase-header">
                    <h2>🎯 Plná verze (solusdt_iframe.html)</h2>
                    <p>Kompletní graf s headerem, statusem a všemi funkcemi</p>
                </div>
                <div class="iframe-container">
                    <iframe 
                        src="solusdt_iframe.html"
                        title="SOLUSDT Plná verze">
                    </iframe>
                </div>
            </div>
            
            <!-- Minimální verze -->
            <div class="showcase-item">
                <div class="showcase-header">
                    <h2>⚡ Minimální verze</h2>
                    <p>Pouze graf s malým countdown timerem</p>
                </div>
                <div class="iframe-container">
                    <iframe 
                        src="solusdt_minimal.html"
                        title="SOLUSDT Minimální verze">
                    </iframe>
                </div>
            </div>
            
            <!-- Standalone verze -->
            <div class="showcase-item">
                <div class="showcase-header">
                    <h2>🖥️ Standalone verze</h2>
                    <p>Původní verze pro samostatné použití</p>
                </div>
                <div class="iframe-container">
                    <iframe 
                        src="solusdt_future_predictions.html"
                        title="SOLUSDT Standalone verze">
                    </iframe>
                </div>
            </div>
        </div>
        
        <div class="features">
            <h2>🚀 Funkce a vlastnosti</h2>
            
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>📱 Responzivní design</h3>
                    <p>Automaticky se přizpůsobí velikosti iframe a zařízení. Optimalizováno pro desktop, tablet i mobil.</p>
                </div>
                
                <div class="feature-item">
                    <h3>🔄 Auto-refresh</h3>
                    <p>Automatické obnovování dat každých 60 sekund s vizuálním countdown timerem.</p>
                </div>
                
                <div class="feature-item">
                    <h3>💬 Parent komunikace</h3>
                    <p>Posílá statusy do rodičovského okna pro monitoring stavu iframe.</p>
                </div>
                
                <div class="feature-item">
                    <h3>🎨 Customizovatelný</h3>
                    <p>Snadno upravitelný CSS a JavaScript pro přizpůsobení vašemu designu.</p>
                </div>
                
                <div class="feature-item">
                    <h3>⚡ Optimalizovaný</h3>
                    <p>Minimální overhead, rychlé načítání, optimalizované pro iframe použití.</p>
                </div>
                
                <div class="feature-item">
                    <h3>🔒 Bezpečný</h3>
                    <p>Kompatibilní s CSP, X-Frame-Options a dalšími bezpečnostními opatřeními.</p>
                </div>
            </div>
            
            <h2>💻 Příklady použití</h2>
            
            <h3>Základní iframe:</h3>
            <div class="code-example">
                <pre>&lt;iframe 
    src="solusdt_iframe.html"
    width="100%"
    height="600"
    frameborder="0"
    title="SOLUSDT Graf"&gt;
&lt;/iframe&gt;</pre>
            </div>
            
            <h3>Responzivní iframe s CSS:</h3>
            <div class="code-example">
                <pre>&lt;style&gt;
.chart-iframe {
    width: 100%;
    height: 600px;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .chart-iframe { height: 400px; }
}
&lt;/style&gt;

&lt;iframe class="chart-iframe" src="solusdt_iframe.html"&gt;&lt;/iframe&gt;</pre>
            </div>
            
            <h3>JavaScript komunikace:</h3>
            <div class="code-example">
                <pre>window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'solusdt-chart') {
        console.log('Chart status:', event.data.data.status);
        // Možné statusy: 'loaded', 'refreshing', 'error'
    }
});</pre>
            </div>
            
            <h2>📋 Doporučení pro použití</h2>
            
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>🎯 Plná verze</h3>
                    <p><strong>Použijte když:</strong> Chcete kompletní informace, status a header. Ideální pro hlavní dashboard.</p>
                </div>
                
                <div class="feature-item">
                    <h3>⚡ Minimální verze</h3>
                    <p><strong>Použijte když:</strong> Máte omezený prostor nebo chcete pouze graf. Ideální pro sidebar nebo malé widgety.</p>
                </div>
                
                <div class="feature-item">
                    <h3>🖥️ Standalone verze</h3>
                    <p><strong>Použijte když:</strong> Chcete otevřít graf v novém okně nebo jako samostatnou stránku.</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Monitoring všech iframe
        window.addEventListener('message', function(event) {
            if (event.data && (event.data.type === 'solusdt-chart' || event.data.type === 'solusdt-minimal')) {
                console.log(`${event.data.type} status:`, event.data.data.status);
            }
        });
        
        // Responsive iframe height adjustment
        function adjustIframes() {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(iframe => {
                const container = iframe.closest('.iframe-container');
                if (container && window.innerWidth < 768) {
                    if (container.closest('.full-width')) {
                        container.style.height = '400px';
                    } else {
                        container.style.height = '300px';
                    }
                }
            });
        }
        
        window.addEventListener('resize', adjustIframes);
        window.addEventListener('load', adjustIframes);
    </script>
</body>
</html>
