import time
import pyodbc
import pandas as pd
import ta
from datetime import datetime, timedelta

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.0.100,1433;"  # Například "192.168.1.100\\SQLEXPRESS"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)


def aktualizuj_indikatory():
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        # Zjistit poslední záznam v tabulce indikátorů
        poslední_query = """
        SELECT MAX(timestamp) as poslední_čas FROM alch_indikatory
        """
        cursor.execute(poslední_query)
        poslední_záznam = cursor.fetchone()
        poslední_čas = poslední_záznam[0] if poslední_záznam[0] else None
        
        # Určit časové okno pro výpočet - potřebujeme historická data pro správný výpočet indikátorů
        if poslední_čas:
            print(f"Poslední záznam indikátorů v databázi: {poslední_čas}")
            # Načíst data s překryvem pro výpočet správných hodnot (např. 20 dní pro Bollinger)
            history_window = 30  # Dní historie pro správný výpočet
            výpočetní_start = poslední_čas - timedelta(days=history_window)
            
            # Načíst dostatečné množství historických dat pro výpočet
            history_query = f"""
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM alch_price_history 
            WHERE timestamp >= ?
            ORDER BY timestamp ASC
            """
            df_history = pd.read_sql(history_query, conn, params=[výpočetní_start])
            
            # Ale do DB budeme vkládat jen nová data
            insert_start = poslední_čas
            print(f"Načteno {len(df_history)} záznamů pro výpočet indikátorů (včetně historie)")
            print(f"Vkládat budeme záznamy od {insert_start}")
        else:
            print("Tabulka indikátorů je prázdná, načítám všechna data.")
            history_query = """
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM alch_price_history 
            ORDER BY timestamp ASC
            """
            df_history = pd.read_sql(history_query, conn)
            insert_start = None
            print(f"Načteno {len(df_history)} záznamů pro první výpočet indikátorů")
        
        if len(df_history) == 0:
            print("Žádná nová data k dispozici.")
            return
            
        # Výpočet technických indikátorů na celém datasetu s historií
        print("Počítám technické indikátory...")
        df_history["RSI"] = ta.momentum.rsi(df_history["close_price"], window=14)
        df_history["EMA9"] = ta.trend.ema_indicator(df_history["close_price"], window=9)
        df_history["EMA20"] = ta.trend.ema_indicator(df_history["close_price"], window=20)

        bollinger = ta.volatility.BollingerBands(df_history["close_price"], window=20)
        df_history["boll_high"] = bollinger.bollinger_hband()
        df_history["boll_low"] = bollinger.bollinger_lband()

        df_history["ATR"] = ta.volatility.average_true_range(
            df_history["high_price"], df_history["low_price"], df_history["close_price"], window=14
        )

        # Vyfiltrovat pouze nové záznamy k vložení
        if insert_start:
            df_insert = df_history[df_history["timestamp"] > insert_start].copy()
        else:
            df_insert = df_history.copy()
        
        # Odstranění řádků s NaN hodnotami
        df_insert = df_insert.dropna()
        print(f"K vložení připraveno {len(df_insert)} nových záznamů.")
        
        # Vkládání nových záznamů do databáze
        if len(df_insert) > 0:
            počet_vložených = 0
            print("Vkládám nové záznamy do databáze...")
            
            for _, row in df_insert.iterrows():
                try:
                    cursor.execute(
                        """
                        IF NOT EXISTS (SELECT 1 FROM alch_indikatory WHERE timestamp = ?)
                        BEGIN
                            INSERT INTO alch_indikatory (timestamp, close_price, RSI, EMA9, EMA20, boll_high, boll_low, ATR)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        END
                    """,
                        row["timestamp"],
                        row["timestamp"],
                        float(row["close_price"]),
                        float(row["RSI"]),
                        float(row["EMA9"]),
                        float(row["EMA20"]),
                        float(row["boll_high"]),
                        float(row["boll_low"]),
                        float(row["ATR"]),
                    )
                    počet_vložených += 1
                    
                    # Commit po každých 1000 záznamech pro lepší výkon
                    if počet_vložených % 1000 == 0:
                        conn.commit()
                        print(f"Vloženo {počet_vložených} záznamů...")
                        
                except Exception as e:
                    print(f"CHYBA při vkládání záznamu {row['timestamp']}: {e}")
                    
            # Finální commit
            conn.commit()
            print(f"Celkem vloženo {počet_vložených} nových záznamů do tabulky alch_indikatory")
        else:
            print("Žádné nové záznamy k vložení.")
            
    except Exception as e:
        print(f"CHYBA při zpracování: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        print("Aktualizace indikátorů dokončena.")


# Spuštění funkce aktualizace indikátorů
if __name__ == "__main__":
    aktualizuj_indikatory()



