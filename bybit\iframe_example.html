<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Můj web s SOLUSDT grafem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .chart-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .chart-header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .chart-iframe {
            width: 100%;
            height: 600px;
            border: none;
            display: block;
        }
        
        .info-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-loading {
            background: #f39c12;
            color: white;
        }
        
        .status-loaded {
            background: #27ae60;
            color: white;
        }
        
        .status-error {
            background: #e74c3c;
            color: white;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .chart-iframe {
                height: 500px;
            }
            
            body {
                padding: 10px;
            }
        }
        
        @media (max-width: 480px) {
            .chart-iframe {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Můj Trading Dashboard</h1>
        
        <div class="chart-section">
            <div class="chart-header">
                📈 SOLUSDT Live Predikce
                <span id="chart-status" class="status-indicator status-loading">Načítání...</span>
            </div>
            <iframe 
                id="solusdt-chart"
                class="chart-iframe"
                src="solusdt_iframe.html"
                title="SOLUSDT Predikční graf"
                loading="lazy">
                <p>Váš prohlížeč nepodporuje iframe. <a href="solusdt_iframe.html" target="_blank">Otevřít graf v novém okně</a></p>
            </iframe>
        </div>
        
        <div class="info-section">
            <h2>ℹ️ Informace o grafu</h2>
            <ul>
                <li><strong>Symbol:</strong> SOLUSDT</li>
                <li><strong>Timeframe:</strong> 1 minuta</li>
                <li><strong>Predikční modely:</strong> KNN + Chaos Theory</li>
                <li><strong>Auto-refresh:</strong> Každých 60 sekund</li>
                <li><strong>Poslední aktualizace:</strong> <span id="last-update">-</span></li>
            </ul>
            
            <h3>🎯 Jak číst graf:</h3>
            <ul>
                <li><span style="color: royalblue;">■</span> <strong>Modrá linie:</strong> Skutečné ceny SOLUSDT</li>
                <li><span style="color: orange;">■</span> <strong>Oranžová linie:</strong> KNN predikce do budoucna</li>
                <li><span style="color: green;">■</span> <strong>Zelená linie:</strong> Chaos predikce do budoucna</li>
                <li><span style="color: white;">⋯</span> <strong>Bílá čára:</strong> Hranice mezi skutečnými daty a predikcí</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Komunikace s iframe
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'solusdt-chart') {
                const statusElement = document.getElementById('chart-status');
                const updateElement = document.getElementById('last-update');
                
                switch(event.data.data.status) {
                    case 'loaded':
                        statusElement.textContent = 'Online';
                        statusElement.className = 'status-indicator status-loaded';
                        updateElement.textContent = new Date().toLocaleString('cs-CZ');
                        break;
                        
                    case 'refreshing':
                        statusElement.textContent = 'Obnovování...';
                        statusElement.className = 'status-indicator status-loading';
                        break;
                        
                    case 'error':
                        statusElement.textContent = 'Chyba';
                        statusElement.className = 'status-indicator status-error';
                        break;
                }
            }
        });
        
        // Fallback pro detekci načtení iframe
        document.getElementById('solusdt-chart').addEventListener('load', function() {
            setTimeout(function() {
                const statusElement = document.getElementById('chart-status');
                if (statusElement.textContent === 'Načítání...') {
                    statusElement.textContent = 'Načteno';
                    statusElement.className = 'status-indicator status-loaded';
                    document.getElementById('last-update').textContent = new Date().toLocaleString('cs-CZ');
                }
            }, 1000);
        });
        
        // Error handling pro iframe
        document.getElementById('solusdt-chart').addEventListener('error', function() {
            const statusElement = document.getElementById('chart-status');
            statusElement.textContent = 'Chyba načítání';
            statusElement.className = 'status-indicator status-error';
        });
        
        // Responsive iframe height adjustment
        function adjustIframeHeight() {
            const iframe = document.getElementById('solusdt-chart');
            const container = iframe.parentElement;
            const containerWidth = container.offsetWidth;
            
            // Nastavíme výšku podle poměru stran
            if (containerWidth < 768) {
                iframe.style.height = '500px';
            } else if (containerWidth < 480) {
                iframe.style.height = '400px';
            } else {
                iframe.style.height = '600px';
            }
        }
        
        // Spustíme při načtení a změně velikosti okna
        window.addEventListener('load', adjustIframeHeight);
        window.addEventListener('resize', adjustIframeHeight);
    </script>
</body>
</html>
