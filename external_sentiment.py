"""
Modul pro získávání a zpracování externích dat o sentimentu trhu.
Implementuje konektory na různé API a zdroje dat o sentimentu kryptoměn.
"""
import os
import logging
import pandas as pd
import numpy as np
import requests
import json
import pyodbc
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from bs4 import BeautifulSoup

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

# Konstanty pro API
FEAR_GREED_API_URL = "https://api.alternative.me/fng/"
COINMARKETCAP_API_KEY = os.environ.get("COINMARKETCAP_API_KEY", "")
COINMARKETCAP_API_URL = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def create_sentiment_table(conn: pyodbc.Connection) -> bool:
    """Vytvoří tabulku pro ukládání externích dat o sentimentu."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                      WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'EXTERNAL_SENTIMENT')
        BEGIN
            CREATE TABLE EXTERNAL_SENTIMENT (
                id INT IDENTITY(1,1) PRIMARY KEY,
                timestamp DATETIME2 NOT NULL,
                symbol VARCHAR(20) NOT NULL,
                fear_greed_index INT NULL,
                fear_greed_value VARCHAR(20) NULL,
                market_cap_change_24h FLOAT NULL,
                volume_change_24h FLOAT NULL,
                social_sentiment FLOAT NULL,
                news_sentiment FLOAT NULL,
                combined_sentiment FLOAT NULL,
                source VARCHAR(50) NOT NULL,
                raw_data NVARCHAR(MAX) NULL,

                -- Indexy pro rychlejší vyhledávání
                INDEX idx_sentiment_timestamp (timestamp),
                INDEX idx_sentiment_symbol (symbol)
            );
            PRINT 'Tabulka EXTERNAL_SENTIMENT byla úspěšně vytvořena.';
        END
        """)
        conn.commit()
        logger.info("Tabulka EXTERNAL_SENTIMENT byla ověřena/vytvořena.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při vytváření tabulky EXTERNAL_SENTIMENT: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def get_fear_greed_index() -> Dict[str, Any]:
    """
    Získá Fear & Greed Index pro kryptoměny.

    Returns:
        Slovník s daty o Fear & Greed Indexu
    """
    try:
        logger.info("Získávám Fear & Greed Index...")
        response = requests.get(FEAR_GREED_API_URL)
        response.raise_for_status()

        data = response.json()

        if 'data' in data and len(data['data']) > 0:
            latest = data['data'][0]

            result = {
                'timestamp': datetime.fromtimestamp(int(latest['timestamp'])),
                'value': int(latest['value']),
                'value_classification': latest['value_classification'],
                'source': 'alternative.me',
                'raw_data': json.dumps(latest)
            }

            logger.info(f"Fear & Greed Index: {result['value']} ({result['value_classification']})")
            return result
        else:
            logger.warning("Neplatná odpověď z Fear & Greed API")
            return {}
    except requests.RequestException as e:
        logger.error(f"Chyba při získávání Fear & Greed Indexu: {e}")
        return {}
    except Exception as e:
        logger.error(f"Neočekávaná chyba při získávání Fear & Greed Indexu: {e}")
        return {}

def get_coinmarketcap_data(symbol: str) -> Dict[str, Any]:
    """
    Získá data o kryptoměně z CoinMarketCap API.

    Args:
        symbol: Symbol kryptoměny (např. 'BTC')

    Returns:
        Slovník s daty o kryptoměně
    """
    if not COINMARKETCAP_API_KEY:
        logger.warning("CoinMarketCap API klíč není nastaven")
        return {}

    try:
        logger.info(f"Získávám data o {symbol} z CoinMarketCap...")

        headers = {
            'X-CMC_PRO_API_KEY': COINMARKETCAP_API_KEY,
            'Accept': 'application/json'
        }

        params = {
            'symbol': symbol,
            'convert': 'USD'
        }

        response = requests.get(COINMARKETCAP_API_URL, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()

        if 'data' in data and symbol in data['data']:
            coin_data = data['data'][symbol]
            quote = coin_data['quote']['USD']

            result = {
                'timestamp': datetime.now(),
                'symbol': symbol,
                'price': quote['price'],
                'volume_24h': quote['volume_24h'],
                'volume_change_24h': quote['volume_change_24h'],
                'market_cap': quote['market_cap'],
                'market_cap_change_24h': quote['market_cap_change_24h'],
                'percent_change_1h': quote['percent_change_1h'],
                'percent_change_24h': quote['percent_change_24h'],
                'percent_change_7d': quote['percent_change_7d'],
                'source': 'coinmarketcap',
                'raw_data': json.dumps(coin_data)
            }

            logger.info(f"Data z CoinMarketCap pro {symbol}: Cena=${result['price']:.2f}, Změna 24h={result['percent_change_24h']:.2f}%")
            return result
        else:
            logger.warning(f"Neplatná odpověď z CoinMarketCap API pro {symbol}")
            return {}
    except requests.RequestException as e:
        logger.error(f"Chyba při získávání dat z CoinMarketCap: {e}")
        return {}
    except Exception as e:
        logger.error(f"Neočekávaná chyba při získávání dat z CoinMarketCap: {e}")
        return {}

def get_social_sentiment(symbol: str) -> Dict[str, Any]:
    """
    Získá sentiment z sociálních médií (Twitter, Reddit).
    Tato funkce je placeholder - v reálném nasazení by bylo potřeba
    implementovat skutečné připojení k API sociálních médií.

    Args:
        symbol: Symbol kryptoměny

    Returns:
        Slovník s daty o sentimentu
    """
    # Placeholder - v reálném nasazení by zde bylo připojení k API
    logger.info(f"Získávám sociální sentiment pro {symbol} (placeholder)...")

    # Simulace dat
    sentiment_value = np.random.normal(0, 0.3)  # Náhodný sentiment mezi -1 a 1
    sentiment_value = max(-1, min(1, sentiment_value))  # Omezení na rozsah -1 až 1

    result = {
        'timestamp': datetime.now(),
        'symbol': symbol,
        'sentiment': sentiment_value,
        'source': 'social_media_placeholder',
        'raw_data': json.dumps({
            'twitter_sentiment': sentiment_value * 0.8,
            'reddit_sentiment': sentiment_value * 1.2,
            'mentions_count': int(np.random.randint(100, 1000))
        })
    }

    logger.info(f"Sociální sentiment pro {symbol}: {sentiment_value:.2f}")
    return result

def get_news_sentiment(symbol: str) -> Dict[str, Any]:
    """
    Získá sentiment z zpravodajských článků.
    Tato funkce je placeholder - v reálném nasazení by bylo potřeba
    implementovat skutečné připojení k API zpravodajských služeb.

    Args:
        symbol: Symbol kryptoměny

    Returns:
        Slovník s daty o sentimentu
    """
    # Placeholder - v reálném nasazení by zde bylo připojení k API
    logger.info(f"Získávám sentiment zpráv pro {symbol} (placeholder)...")

    # Simulace dat
    sentiment_value = np.random.normal(0, 0.2)  # Náhodný sentiment mezi -1 a 1
    sentiment_value = max(-1, min(1, sentiment_value))  # Omezení na rozsah -1 až 1

    result = {
        'timestamp': datetime.now(),
        'symbol': symbol,
        'sentiment': sentiment_value,
        'source': 'news_placeholder',
        'raw_data': json.dumps({
            'positive_articles': int(np.random.randint(0, 10)),
            'negative_articles': int(np.random.randint(0, 10)),
            'neutral_articles': int(np.random.randint(0, 20))
        })
    }

    logger.info(f"Sentiment zpráv pro {symbol}: {sentiment_value:.2f}")
    return result

def calculate_combined_sentiment(fear_greed_data: Dict[str, Any],
                               market_data: Dict[str, Any],
                               social_data: Dict[str, Any],
                               news_data: Dict[str, Any]) -> float:
    """
    Vypočítá kombinovaný sentiment z různých zdrojů.

    Args:
        fear_greed_data: Data z Fear & Greed Indexu
        market_data: Data z CoinMarketCap
        social_data: Data ze sociálních médií
        news_data: Data ze zpravodajských článků

    Returns:
        Kombinovaný sentiment (-1 až 1)
    """
    # Váhy pro jednotlivé zdroje
    weights = {
        'fear_greed': 0.3,
        'market': 0.3,
        'social': 0.2,
        'news': 0.2
    }

    # Inicializace sentimentů
    sentiments = {
        'fear_greed': 0.0,
        'market': 0.0,
        'social': 0.0,
        'news': 0.0
    }

    # Fear & Greed Index (0-100) -> -1 až 1
    if fear_greed_data and 'value' in fear_greed_data:
        sentiments['fear_greed'] = (fear_greed_data['value'] - 50) / 50

    # Market data
    if market_data:
        # Kombinace změny tržní kapitalizace a objemu
        market_cap_change = market_data.get('market_cap_change_24h', 0)
        volume_change = market_data.get('volume_change_24h', 0)

        # Normalizace na rozsah -1 až 1
        if market_cap_change:
            market_cap_sentiment = max(-1, min(1, market_cap_change / 10))
        else:
            market_cap_sentiment = 0

        if volume_change:
            volume_sentiment = max(-1, min(1, volume_change / 20))
        else:
            volume_sentiment = 0

        sentiments['market'] = 0.7 * market_cap_sentiment + 0.3 * volume_sentiment

    # Social sentiment
    if social_data and 'sentiment' in social_data:
        sentiments['social'] = social_data['sentiment']

    # News sentiment
    if news_data and 'sentiment' in news_data:
        sentiments['news'] = news_data['sentiment']

    # Výpočet váženého průměru
    combined_sentiment = 0.0
    total_weight = 0.0

    for source, sentiment in sentiments.items():
        weight = weights[source]
        combined_sentiment += sentiment * weight
        total_weight += weight

    if total_weight > 0:
        combined_sentiment /= total_weight

    logger.info(f"Vypočítán kombinovaný sentiment: {combined_sentiment:.2f}")
    return combined_sentiment

def save_sentiment_data(conn: pyodbc.Connection,
                      symbol: str,
                      fear_greed_data: Dict[str, Any],
                      market_data: Dict[str, Any],
                      social_data: Dict[str, Any],
                      news_data: Dict[str, Any],
                      combined_sentiment: float) -> bool:
    """
    Uloží data o sentimentu do databáze.

    Args:
        conn: Připojení k databázi
        symbol: Symbol kryptoměny
        fear_greed_data: Data z Fear & Greed Indexu
        market_data: Data z CoinMarketCap
        social_data: Data ze sociálních médií
        news_data: Data ze zpravodajských článků
        combined_sentiment: Kombinovaný sentiment

    Returns:
        True pokud bylo uložení úspěšné, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Aktuální čas
        timestamp = datetime.now()

        # Extrakce hodnot
        fear_greed_index = fear_greed_data.get('value') if fear_greed_data else None
        fear_greed_value = fear_greed_data.get('value_classification') if fear_greed_data else None

        market_cap_change = market_data.get('market_cap_change_24h') if market_data else None
        volume_change = market_data.get('volume_change_24h') if market_data else None

        social_sentiment = social_data.get('sentiment') if social_data else None
        news_sentiment = news_data.get('sentiment') if news_data else None

        # Zdroje dat
        sources = []
        if fear_greed_data:
            sources.append(fear_greed_data.get('source', 'fear_greed'))
        if market_data:
            sources.append(market_data.get('source', 'market'))
        if social_data:
            sources.append(social_data.get('source', 'social'))
        if news_data:
            sources.append(news_data.get('source', 'news'))

        # Omezení délky zdroje na 50 znaků (velikost sloupce v databázi)
        source = '+'.join(sources)
        if len(source) > 50:
            source = source[:47] + '...'

        # Surová data
        raw_data = {
            'fear_greed': fear_greed_data.get('raw_data') if fear_greed_data else None,
            'market': market_data.get('raw_data') if market_data else None,
            'social': social_data.get('raw_data') if social_data else None,
            'news': news_data.get('raw_data') if news_data else None
        }

        raw_data_json = json.dumps(raw_data)

        # Vložení záznamu
        cursor.execute("""
        INSERT INTO EXTERNAL_SENTIMENT
        (timestamp, symbol, fear_greed_index, fear_greed_value, market_cap_change_24h,
         volume_change_24h, social_sentiment, news_sentiment, combined_sentiment, source, raw_data)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            timestamp,
            symbol,
            fear_greed_index,
            fear_greed_value,
            market_cap_change,
            volume_change,
            social_sentiment,
            news_sentiment,
            combined_sentiment,
            source,
            raw_data_json
        ))

        conn.commit()
        logger.info(f"Data o sentimentu pro {symbol} byla úspěšně uložena.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při ukládání dat o sentimentu: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def get_latest_sentiment(conn: pyodbc.Connection, symbol: str) -> Dict[str, Any]:
    """
    Získá nejnovější data o sentimentu z databáze.

    Args:
        conn: Připojení k databázi
        symbol: Symbol kryptoměny

    Returns:
        Slovník s daty o sentimentu
    """
    cursor = None
    try:
        cursor = conn.cursor()

        cursor.execute("""
        SELECT TOP 1 * FROM EXTERNAL_SENTIMENT
        WHERE symbol = ?
        ORDER BY timestamp DESC
        """, (symbol,))

        row = cursor.fetchone()

        if row:
            columns = [column[0] for column in cursor.description]
            result = dict(zip(columns, row))

            logger.info(f"Načtena nejnovější data o sentimentu pro {symbol} z {result['timestamp']}")
            return result
        else:
            logger.info(f"Nenalezena žádná data o sentimentu pro {symbol}")
            return {}
    except pyodbc.Error as e:
        logger.error(f"Chyba při získávání dat o sentimentu: {e}")
        return {}
    finally:
        if cursor:
            cursor.close()

def get_sentiment_history(conn: pyodbc.Connection,
                        symbol: str,
                        days: int = 7) -> pd.DataFrame:
    """
    Získá historii dat o sentimentu z databáze.

    Args:
        conn: Připojení k databázi
        symbol: Symbol kryptoměny
        days: Počet dní historie

    Returns:
        DataFrame s historií dat o sentimentu
    """
    cursor = None
    try:
        cursor = conn.cursor()

        cursor.execute("""
        SELECT * FROM EXTERNAL_SENTIMENT
        WHERE symbol = ? AND timestamp >= DATEADD(day, ?, GETDATE())
        ORDER BY timestamp ASC
        """, (symbol, -days))

        rows = cursor.fetchall()

        if rows:
            columns = [column[0] for column in cursor.description]
            df = pd.DataFrame.from_records(rows, columns=columns)

            # Konverze datumů
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            logger.info(f"Načteno {len(df)} záznamů historie sentimentu pro {symbol}")
            return df
        else:
            logger.info(f"Nenalezena žádná historie sentimentu pro {symbol}")
            return pd.DataFrame()
    except pyodbc.Error as e:
        logger.error(f"Chyba při získávání historie sentimentu: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()

def update_sentiment_data(symbol: str) -> bool:
    """
    Aktualizuje data o sentimentu pro daný symbol.

    Args:
        symbol: Symbol kryptoměny

    Returns:
        True pokud byla aktualizace úspěšná, jinak False
    """
    conn = None
    try:
        logger.info(f"Aktualizuji data o sentimentu pro {symbol}...")

        # Připojení k databázi
        conn = connect_to_db()
        if not conn:
            logger.error("Nelze se připojit k databázi")
            return False

        # Vytvoření tabulky, pokud neexistuje
        create_sentiment_table(conn)

        # Získání dat z různých zdrojů
        fear_greed_data = get_fear_greed_index()
        market_data = get_coinmarketcap_data(symbol)
        social_data = get_social_sentiment(symbol)
        news_data = get_news_sentiment(symbol)

        # Výpočet kombinovaného sentimentu
        combined_sentiment = calculate_combined_sentiment(
            fear_greed_data, market_data, social_data, news_data
        )

        # Uložení dat do databáze
        success = save_sentiment_data(
            conn, symbol, fear_greed_data, market_data, social_data, news_data, combined_sentiment
        )

        return success
    except Exception as e:
        logger.error(f"Neočekávaná chyba při aktualizaci dat o sentimentu: {e}")
        return False
    finally:
        if conn:
            conn.close()

def get_sentiment_features(conn: pyodbc.Connection, symbol: str) -> Dict[str, float]:
    """
    Získá příznaky sentimentu pro použití v predikčním modelu.

    Args:
        conn: Připojení k databázi
        symbol: Symbol kryptoměny

    Returns:
        Slovník s příznaky sentimentu
    """
    try:
        # Získání nejnovějších dat o sentimentu
        latest_sentiment = get_latest_sentiment(conn, symbol)

        if not latest_sentiment:
            logger.warning(f"Nenalezena žádná data o sentimentu pro {symbol}, používám neutrální hodnoty")
            return {
                'fear_greed_normalized': 0.0,
                'market_cap_change_normalized': 0.0,
                'volume_change_normalized': 0.0,
                'social_sentiment': 0.0,
                'news_sentiment': 0.0,
                'combined_sentiment': 0.0
            }

        # Normalizace Fear & Greed Indexu na rozsah -1 až 1
        fear_greed_normalized = 0.0
        if latest_sentiment.get('fear_greed_index'):
            fear_greed_normalized = (latest_sentiment['fear_greed_index'] - 50) / 50

        # Normalizace změny tržní kapitalizace na rozsah -1 až 1
        market_cap_change_normalized = 0.0
        if latest_sentiment.get('market_cap_change_24h'):
            market_cap_change_normalized = max(-1, min(1, latest_sentiment['market_cap_change_24h'] / 10))

        # Normalizace změny objemu na rozsah -1 až 1
        volume_change_normalized = 0.0
        if latest_sentiment.get('volume_change_24h'):
            volume_change_normalized = max(-1, min(1, latest_sentiment['volume_change_24h'] / 20))

        # Vytvoření slovníku příznaků
        features = {
            'fear_greed_normalized': fear_greed_normalized,
            'market_cap_change_normalized': market_cap_change_normalized,
            'volume_change_normalized': volume_change_normalized,
            'social_sentiment': latest_sentiment.get('social_sentiment', 0.0),
            'news_sentiment': latest_sentiment.get('news_sentiment', 0.0),
            'combined_sentiment': latest_sentiment.get('combined_sentiment', 0.0)
        }

        logger.info(f"Získány příznaky sentimentu pro {symbol}: {features}")
        return features
    except Exception as e:
        logger.error(f"Chyba při získávání příznaků sentimentu: {e}")
        return {
            'fear_greed_normalized': 0.0,
            'market_cap_change_normalized': 0.0,
            'volume_change_normalized': 0.0,
            'social_sentiment': 0.0,
            'news_sentiment': 0.0,
            'combined_sentiment': 0.0
        }

if __name__ == "__main__":
    # Test funkcionality
    symbol = "ALCHUSDT"

    # Aktualizace dat o sentimentu
    success = update_sentiment_data(symbol)

    if success:
        # Připojení k databázi
        conn = connect_to_db()
        if conn:
            # Získání nejnovějších dat
            latest = get_latest_sentiment(conn, symbol)
            print("Nejnovější data o sentimentu:")
            for key, value in latest.items():
                if key != 'raw_data':  # Vynecháme raw_data pro přehlednost
                    print(f"  {key}: {value}")

            # Získání historie
            history = get_sentiment_history(conn, symbol, days=7)
            if not history.empty:
                print(f"\nHistorie sentimentu (posledních {len(history)} záznamů):")
                print(history[['timestamp', 'combined_sentiment']].tail())

            # Získání příznaků
            features = get_sentiment_features(conn, symbol)
            print("\nPříznaky sentimentu pro model:")
            for key, value in features.items():
                print(f"  {key}: {value}")

            conn.close()
