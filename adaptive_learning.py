"""
Modul pro adaptivní učení a průběžné dolaďování predikčního modelu.
Implementuje mechanismy pro sledování výkonu modelu, detekci degradace
a automatické přetrénování s optimalizovanými parametry.
"""
import os
import logging
import pandas as pd
import numpy as np
import joblib
import pyodbc
import xgboost as xgb
from datetime import datetime, timedelta
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error
from typing import Dict, Tuple, List, Optional, Union, Any

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Konstanty pro adaptivní učení
ERROR_THRESHOLD = 0.02  # 2% chyba jako výchozí práh
MAX_ERROR_RATIO = 1.5   # Maximální poměr aktuální chyby k historické
MIN_SAMPLES_FOR_RETRAINING = 100  # Minimální počet vzorků pro přetrénování
HISTORY_WINDOW_DAYS = 14  # Okno pro historická data (dny)
MODEL_PERFORMANCE_FILE = "model_performance.csv"  # Soubor pro sledování výkonu modelu

# Připojovací řetězec k databázi
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

class ModelPerformanceTracker:
    """Třída pro sledování výkonu modelu v čase."""

    def __init__(self, performance_file: str = MODEL_PERFORMANCE_FILE):
        """
        Inicializace trackeru výkonu modelu.

        Args:
            performance_file: Cesta k souboru pro ukládání historie výkonu
        """
        self.performance_file = performance_file
        self.performance_history = self._load_performance_history()

    def _load_performance_history(self) -> pd.DataFrame:
        """Načte historii výkonu modelu ze souboru."""
        try:
            if os.path.exists(self.performance_file):
                return pd.read_csv(self.performance_file, parse_dates=['timestamp'])
            else:
                # Vytvoření prázdného DataFrame s požadovanou strukturou
                return pd.DataFrame(columns=[
                    'timestamp', 'rmse', 'mae', 'num_samples', 'model_version',
                    'market_condition', 'avg_error_pct'
                ])
        except Exception as e:
            logger.error(f"Chyba při načítání historie výkonu: {e}")
            return pd.DataFrame(columns=[
                'timestamp', 'rmse', 'mae', 'num_samples', 'model_version',
                'market_condition', 'avg_error_pct'
            ])

    def save_performance(self, metrics: Dict[str, Any]) -> None:
        """
        Uloží aktuální metriky výkonu modelu.

        Args:
            metrics: Slovník s metrikami výkonu (rmse, mae, atd.)
        """
        try:
            # Přidání časového razítka
            metrics['timestamp'] = datetime.now()

            # Přidání nového záznamu do historie
            new_record = pd.DataFrame([metrics])
            self.performance_history = pd.concat([self.performance_history, new_record], ignore_index=True)

            # Uložení aktualizované historie
            self.performance_history.to_csv(self.performance_file, index=False)
            logger.info(f"Výkon modelu uložen do {self.performance_file}")
        except Exception as e:
            logger.error(f"Chyba při ukládání výkonu modelu: {e}")

    def get_recent_performance(self, days: int = 7) -> pd.DataFrame:
        """
        Získá výkon modelu za posledních N dní.

        Args:
            days: Počet dní historie k vrácení

        Returns:
            DataFrame s historií výkonu za zadané období
        """
        if self.performance_history.empty:
            return pd.DataFrame()

        cutoff_date = datetime.now() - timedelta(days=days)
        return self.performance_history[self.performance_history['timestamp'] >= cutoff_date]

    def is_performance_degrading(self, current_metrics: Dict[str, float]) -> bool:
        """
        Zjistí, zda se výkon modelu zhoršuje.

        Args:
            current_metrics: Aktuální metriky výkonu

        Returns:
            True pokud se výkon zhoršuje, jinak False
        """
        if self.performance_history.empty:
            return False

        # Získání průměrného RMSE z posledních záznamů
        recent_history = self.get_recent_performance()
        if recent_history.empty:
            return False

        avg_rmse = recent_history['rmse'].mean()
        avg_mae = recent_history['mae'].mean()

        # Kontrola, zda aktuální chyby jsou výrazně vyšší než průměrné
        rmse_ratio = current_metrics['rmse'] / avg_rmse if avg_rmse > 0 else 1.0
        mae_ratio = current_metrics['mae'] / avg_mae if avg_mae > 0 else 1.0

        logger.info(f"Poměr RMSE: {rmse_ratio:.2f}, Poměr MAE: {mae_ratio:.2f}")

        # Výkon se zhoršuje, pokud je poměr chyb vyšší než práh
        return rmse_ratio > MAX_ERROR_RATIO or mae_ratio > MAX_ERROR_RATIO

def load_training_data(days: int = HISTORY_WINDOW_DAYS) -> pd.DataFrame:
    """
    Načte trénovací data z databáze.

    Args:
        days: Počet dní historie k načtení

    Returns:
        DataFrame s daty pro trénování
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cutoff_date = datetime.now() - timedelta(days=days)

            query = f"""
            SELECT * FROM alch_indikatory
            WHERE timestamp >= '{cutoff_date.strftime('%Y-%m-%d')}'
            ORDER BY timestamp ASC
            """
            df = pd.read_sql(query, conn)

            logger.info(f"Načteno {len(df)} záznamů pro adaptivní učení")
            return df
    except Exception as e:
        logger.error(f"Chyba při načítání dat pro adaptivní učení: {e}")
        return pd.DataFrame()

def load_prediction_errors(days: int = HISTORY_WINDOW_DAYS) -> pd.DataFrame:
    """
    Načte historii chyb predikce z databáze.

    Args:
        days: Počet dní historie k načtení

    Returns:
        DataFrame s historií chyb predikce
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cutoff_date = datetime.now() - timedelta(days=days)

            query = f"""
            SELECT * FROM alch_predictions
            WHERE timestamp >= '{cutoff_date.strftime('%Y-%m-%d')}'
            ORDER BY timestamp ASC
            """
            df = pd.read_sql(query, conn)

            if not df.empty:
                # Výpočet procentuální chyby predikce
                df['error_pct'] = abs(df['predicted_price'] - df['actual_price']) / df['actual_price'] * 100
                logger.info(f"Načteno {len(df)} záznamů s historií predikcí")

            return df
    except Exception as e:
        logger.error(f"Chyba při načítání historie predikcí: {e}")
        return pd.DataFrame()

def calculate_model_performance(model_path: str = "alch_price_model.pkl") -> Dict[str, float]:
    """
    Vypočítá aktuální výkon modelu na nejnovějších datech.

    Args:
        model_path: Cesta k souboru modelu

    Returns:
        Slovník s metrikami výkonu
    """
    try:
        # Načtení modelu
        model = joblib.load(model_path)

        # Načtení dat pro testování
        df = load_training_data(days=7)  # Použijeme data za poslední týden
        if df.empty:
            logger.warning("Nedostatek dat pro výpočet výkonu modelu")
            return {'rmse': 0, 'mae': 0, 'num_samples': 0, 'avg_error_pct': 0}

        # Příprava dat
        features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]

        # Kontrola, zda máme všechny potřebné sloupce
        missing_cols = [col for col in features if col not in df.columns]
        if missing_cols:
            logger.error(f"Chybí sloupce pro výpočet výkonu: {missing_cols}")
            return {'rmse': 0, 'mae': 0, 'num_samples': 0, 'avg_error_pct': 0}

        # Odstranění řádků s NaN hodnotami
        df = df.dropna(subset=features + ['close_price'])

        X = df[features]
        y = df["close_price"].shift(-5)  # Predikce ceny za 5 minut

        # Odstranění NaN hodnot po posunu
        X = X[:-5]
        y = y[:-5].dropna()

        # Zarovnání indexů
        X = X.loc[y.index]

        # Predikce
        y_pred = model.predict(X)

        # Výpočet metrik
        rmse = np.sqrt(mean_squared_error(y, y_pred))
        mae = mean_absolute_error(y, y_pred)

        # Výpočet průměrné procentuální chyby
        error_pct = np.mean(np.abs(y_pred - y) / y * 100)

        return {
            'rmse': rmse,
            'mae': mae,
            'num_samples': len(X),
            'avg_error_pct': error_pct,
            'model_version': os.path.getmtime(model_path) if os.path.exists(model_path) else 0,
            'market_condition': 'unknown'  # Toto by mělo být doplněno z modulu market_conditions
        }
    except Exception as e:
        logger.error(f"Chyba při výpočtu výkonu modelu: {e}")
        return {'rmse': 0, 'mae': 0, 'num_samples': 0, 'avg_error_pct': 0}

def optimize_model_parameters(current_params: Dict[str, Any],
                             performance_history: pd.DataFrame) -> Dict[str, Any]:
    """
    Optimalizuje parametry modelu na základě historie výkonu.

    Args:
        current_params: Aktuální parametry modelu
        performance_history: Historie výkonu modelu

    Returns:
        Optimalizované parametry modelu
    """
    # Výchozí parametry, pokud nemáme aktuální
    if not current_params:
        current_params = {
            'n_estimators': 200,
            'learning_rate': 0.05,
            'max_depth': 5,
            'min_child_weight': 3,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0
        }

    # Pokud nemáme dostatek historie, vrátíme aktuální parametry
    if performance_history.empty or len(performance_history) < 3:
        return current_params

    # Analýza trendu chyb
    error_trend = performance_history['rmse'].diff().mean()

    # Optimalizace parametrů na základě trendu chyb
    new_params = current_params.copy()

    if error_trend > 0:  # Chyby rostou
        # Zvýšení regularizace pro omezení přeučení
        new_params['reg_alpha'] = current_params.get('reg_alpha', 0.1) * 1.2
        new_params['reg_lambda'] = current_params.get('reg_lambda', 1.0) * 1.2

        # Snížení learning rate pro stabilnější učení
        new_params['learning_rate'] = current_params.get('learning_rate', 0.05) * 0.8

        # Snížení hloubky stromu pro omezení přeučení
        new_params['max_depth'] = max(3, current_params.get('max_depth', 5) - 1)

    else:  # Chyby klesají nebo jsou stabilní
        # Mírné zvýšení kapacity modelu
        new_params['n_estimators'] = int(current_params.get('n_estimators', 200) * 1.1)

        # Mírné zvýšení learning rate pro rychlejší učení
        new_params['learning_rate'] = min(0.1, current_params.get('learning_rate', 0.05) * 1.1)

    logger.info(f"Optimalizované parametry modelu: {new_params}")
    return new_params

def retrain_model(model_path: str = "alch_price_model.pkl",
                 days: int = HISTORY_WINDOW_DAYS,
                 custom_params: Dict[str, Any] = None) -> bool:
    """Přetrénuje model s optimalizovanými parametry.

    Pokud je dostupná GPU, využije ji pro trénování.

    Args:
        model_path: Cesta k souboru modelu
        days: Počet dní historie pro trénování
        custom_params: Vlastní parametry pro model (volitelné)

    Returns:
        True pokud bylo přetrénování úspěšné, jinak False
    """
    try:
        # Načtení dat pro trénování
        df = load_training_data(days=days)
        if df.empty or len(df) < MIN_SAMPLES_FOR_RETRAINING:
            logger.warning(f"Nedostatek dat pro přetrénování (minimum: {MIN_SAMPLES_FOR_RETRAINING})")
            return False

        # Příprava dat
        features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]

        # Kontrola, zda máme všechny potřebné sloupce
        missing_cols = [col for col in features if col not in df.columns]
        if missing_cols:
            logger.error(f"Chybí sloupce pro trénování: {missing_cols}")
            return False

        # Odstranění řádků s NaN hodnotami
        df = df.dropna(subset=features + ['close_price'])

        X = df[features]
        y = df["close_price"].shift(-5)  # Predikce ceny za 5 minut

        # Odstranění NaN hodnot po posunu
        X = X[:-5]
        y = y[:-5].dropna()

        # Zarovnání indexů
        X = X.loc[y.index]

        # Rozdělení na trénovací a testovací sadu
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

        # Přímá kontrola dostupnosti GPU pro XGBoost
        gpu_available = False
        try:
            # Explicitní test XGBoost s GPU (nová syntaxe od verze 2.0.0)
            test_params = {
                'objective': 'reg:squarederror',
                'tree_method': 'hist',  # Efektivní algoritmus pro stromy
                'device': 'cuda'        # Použití GPU
            }
            test_model = xgb.XGBRegressor(**test_params)
            # Vytvoření malého testovacího datasetu
            test_X = np.random.rand(10, 5)
            test_y = np.random.rand(10)
            # Pokus o fit - pokud GPU není dostupná, vyhodí výjimku
            test_model.fit(test_X, test_y, verbose=False)
            gpu_available = True
            logger.info("GPU je dostupná a funkční pro XGBoost, bude použita pro trénování")
        except Exception as e:
            logger.warning(f"GPU není dostupná pro XGBoost: {e}")
            logger.info("Použiji CPU pro trénování modelu")

        # Načtení aktuálního modelu pro získání parametrů
        current_params = {}
        try:
            current_model = joblib.load(model_path)
            current_params = current_model.get_params()
        except:
            logger.warning("Nelze načíst aktuální model, použijí se výchozí parametry")

        # Získání historie výkonu modelu
        tracker = ModelPerformanceTracker()

        # Optimalizace parametrů, pokud nejsou zadány vlastní
        if custom_params is None:
            params = optimize_model_parameters(current_params, tracker.performance_history)
        else:
            params = custom_params

        # Přidání parametrů pro GPU, pokud je dostupná
        if gpu_available:
            # Optimalizace pro GPU (nová syntaxe od verze 2.0.0)
            gpu_params = {
                'tree_method': 'hist',  # Efektivní algoritmus pro stromy
                'device': 'cuda',       # Použití GPU
                'max_bin': 256,        # Zvýšení pro lepší využití GPU
                'n_jobs': 1            # Na GPU je lepší použít 1 vlákno
            }
            # Aktualizace parametrů
            params.update(gpu_params)
            logger.info(f"Použity parametry pro GPU akceleraci: {gpu_params}")
        else:
            # Optimalizace pro CPU
            params['tree_method'] = 'hist'  # Efektivní CPU algoritmus
            params['device'] = 'cpu'        # Explicitní nastavení CPU
            params['n_jobs'] = -1           # Využití všech CPU jader

        # Vytvoření a trénování nového modelu
        model = xgb.XGBRegressor(**params)
        model.fit(X_train, y_train)

        # Vyhodnocení modelu
        # Kontrola, zda model používá GPU
        is_gpu_model = False
        try:
            model_params = model.get_params()
            is_gpu_model = model_params.get('device') == 'cuda'
        except:
            pass

        # Predikce s ohledem na zařízení
        if is_gpu_model:
            # Použití DMatrix pro GPU predikci
            dtest = xgb.DMatrix(X_test)
            booster = model.get_booster()
            y_pred = booster.predict(dtest)
        else:
            # Standardní predikce pro CPU
            y_pred = model.predict(X_test)

        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        mae = mean_absolute_error(y_test, y_pred)

        # Výpočet průměrné procentuální chyby
        error_pct = np.mean(np.abs(y_pred - y_test) / y_test * 100)

        logger.info(f"Nový model - RMSE: {rmse:.4f}, MAE: {mae:.4f}, Chyba: {error_pct:.2f}%")

        # Uložení nového modelu
        joblib.dump(model, model_path)
        logger.info(f"Nový model uložen do {model_path}")

        # Uložení informací o výkonu
        metrics = {
            'rmse': rmse,
            'mae': mae,
            'num_samples': len(X_test),
            'avg_error_pct': error_pct,
            'model_version': datetime.now().timestamp(),
            'market_condition': 'unknown'  # Toto by mělo být doplněno z modulu market_conditions
        }
        tracker.save_performance(metrics)

        return True
    except Exception as e:
        logger.error(f"Chyba při přetrénování modelu: {e}")
        return False

def should_retrain_model() -> bool:
    """
    Rozhodne, zda by měl být model přetrénován na základě jeho výkonu.

    Returns:
        True pokud by měl být model přetrénován, jinak False
    """
    try:
        # Výpočet aktuálního výkonu modelu
        current_metrics = calculate_model_performance()

        # Kontrola, zda máme dostatek dat
        if current_metrics['num_samples'] < MIN_SAMPLES_FOR_RETRAINING:
            logger.info("Nedostatek dat pro rozhodnutí o přetrénování")
            return False

        # Kontrola, zda je chyba nad prahem
        if current_metrics['avg_error_pct'] > ERROR_THRESHOLD:
            logger.info(f"Chyba modelu ({current_metrics['avg_error_pct']:.2f}%) je nad prahem ({ERROR_THRESHOLD:.2f}%)")
            return True

        # Kontrola, zda se výkon zhoršuje
        tracker = ModelPerformanceTracker()
        if tracker.is_performance_degrading(current_metrics):
            logger.info("Výkon modelu se zhoršuje, doporučeno přetrénování")
            return True

        logger.info("Model funguje dobře, přetrénování není potřeba")
        return False
    except Exception as e:
        logger.error(f"Chyba při rozhodování o přetrénování: {e}")
        return False

def adaptive_learning_step() -> None:
    """
    Provede jeden krok adaptivního učení - vyhodnotí výkon modelu
    a případně ho přetrénuje.
    """
    try:
        logger.info("Spouštím krok adaptivního učení...")

        # Kontrola, zda by měl být model přetrénován
        if should_retrain_model():
            logger.info("Přetrénování modelu...")
            success = retrain_model()
            if success:
                logger.info("Model úspěšně přetrénován")
            else:
                logger.warning("Přetrénování modelu selhalo")
        else:
            logger.info("Přetrénování není potřeba")

        # Aktualizace metrik výkonu i když nepřetrénováváme
        current_metrics = calculate_model_performance()
        tracker = ModelPerformanceTracker()
        tracker.save_performance(current_metrics)

    except Exception as e:
        logger.error(f"Chyba během kroku adaptivního učení: {e}")

if __name__ == "__main__":
    # Při spuštění jako samostatný skript provedeme jeden krok adaptivního učení
    adaptive_learning_step()
