import plotly.graph_objs as go
from plotly.offline import plot
import pandas as pd
import pytz

def plot_future_predictions(
    df, future_lstm, future_chaos,
    cooldown_seconds=60,
    poslednich_svic=60
):
    # Serad data podle timestampu vzestupne
    df = df.sort_values('timestamp').reset_index(drop=True)

    # Casova zona Praha
    tz = pytz.timezone('Europe/Prague')
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True).dt.tz_convert(tz)

    # Vyber pouze poslednich N svic
    df_view = df

    # Priprava osy X
    real_x = [t.strftime('%H:%M') for t in df_view['datetime']]
    last_timestamp = df_view['timestamp'].iloc[-1]
    future_timestamps = [last_timestamp + (i+1)*60*1000 for i in range(len(future_lstm))]
    future_times = pd.to_datetime(future_timestamps, unit='ms', utc=True).tz_convert(tz)
    future_x = [t.strftime('%H:%M') for t in future_times]

    print("Zobrazuju poslednich {} svic:".format(len(df_view)))
    print(df_view[['datetime', 'open', 'high', 'low', 'close']].tail(10))
    print("Posledni cas na ose X:", real_x[-1])
    print("Predikce zacina od:", future_x[0])
    print("Aktualni systemovy cas:", pd.Timestamp.now(tz='Europe/Prague'))

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=real_x, y=df_view['close'].values, mode='lines', name='Real Close', line=dict(color='royalblue')
    ))
    fig.add_trace(go.Scatter(
        x=future_x, y=future_lstm, mode='lines+markers', name='KNN Predikce (do budoucna)', line=dict(color='orange')
    ))
    fig.add_trace(go.Scatter(
        x=future_x, y=future_chaos, mode='lines+markers', name='Chaos Predikce (do budoucna)', line=dict(color='green')
    ))
    fig.add_shape(type='line',
                  x0=real_x[-1], y0=min(df_view['close'].min(), min(future_lstm), min(future_chaos)),
                  x1=real_x[-1], y1=max(df_view['close'].max(), max(future_lstm), max(future_chaos)),
                  line=dict(color='white', width=2, dash='dot'))
    fig.update_layout(
        title='SOLUSDT: Realna data + Predikce do budoucna',
        xaxis_title='Cas (HH:MM) [Evropa/Prague]',
        yaxis_title='Cena (USD)',
        template='plotly_dark'
    )

    html_str = plot(fig, include_plotlyjs=True, output_type='div', auto_open=False)

    custom_html = f"""
    <html>
    <head>
        <title>SOLUSDT: Realna data + Predikce do budoucna</title>
        <meta charset="utf-8">
    </head>
    <body>
    <div style="position:fixed;top:10px;right:20px;z-index:1000;background:rgba(20,20,20,0.85);padding:10px 18px;border-radius:12px;">
        <span style="font-size:22px;font-weight:bold;color:#ffa500;">Cooldown: <span id="countdown">{cooldown_seconds}</span> s</span><br>
        <span style="font-size:16px;color:#ccc;">Aktualni cas: <span id="lastupdate"></span></span>
    </div>
    {html_str}
    <script>
    var seconds = {cooldown_seconds};
    function updateCountdown() {{
        if(seconds > 0) {{
            seconds -= 1;
            document.getElementById("countdown").innerText = seconds;
        }} else {{
            location.reload();
        }}
    }}
    setInterval(updateCountdown, 1000);

    function showTime() {{
        var now = new Date();
        document.getElementById("lastupdate").innerText = now.toLocaleTimeString();
    }}
    setInterval(showTime, 1000);
    showTime();
    </script>
    </body>
    </html>
    """

    with open('solusdt_future_predictions.html', 'w', encoding='utf-8') as f:
        f.write(custom_html)
