import plotly.graph_objs as go
from plotly.offline import plot
import pandas as pd
import pytz

def create_iframe_version(html_str, cooldown_seconds):
    """Vytvoří iframe-friendly verzi HTML grafu."""
    iframe_html = f"""<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOLUSDT Predikce</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            overflow-x: hidden;
        }}

        .header {{
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(30, 30, 30, 0.95);
            backdrop-filter: blur(10px);
            padding: 8px 15px;
            z-index: 1000;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }}

        .title {{
            font-size: 16px;
            font-weight: 600;
            color: #ffa500;
        }}

        .status {{
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
        }}

        .countdown {{
            background: linear-gradient(135deg, #ff6b35, #ffa500);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: bold;
            color: #000;
        }}

        .time {{
            color: #ccc;
        }}

        .chart-container {{
            margin-top: 50px;
            height: calc(100vh - 50px);
            width: 100%;
        }}

        .plotly-graph-div {{
            height: 100% !important;
            width: 100% !important;
        }}

        /* Responsive design */
        @media (max-width: 768px) {{
            .header {{
                flex-direction: column;
                height: auto;
                padding: 8px;
            }}

            .title {{
                font-size: 14px;
                margin-bottom: 5px;
            }}

            .status {{
                font-size: 11px;
                gap: 10px;
            }}

            .chart-container {{
                margin-top: 70px;
                height: calc(100vh - 70px);
            }}
        }}

        /* Iframe optimalizace */
        .js-plotly-plot .plotly .modebar {{
            right: 10px !important;
            top: 60px !important;
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="title">📈 SOLUSDT Predikce</div>
        <div class="status">
            <div class="countdown">
                ⏱️ <span id="countdown">{cooldown_seconds}</span>s
            </div>
            <div class="time">
                🕒 <span id="lastupdate"></span>
            </div>
        </div>
    </div>

    <div class="chart-container">
        {html_str}
    </div>

    <script>
        // Countdown timer
        let seconds = {cooldown_seconds};

        function updateCountdown() {{
            if (seconds > 0) {{
                seconds -= 1;
                document.getElementById("countdown").innerText = seconds;
            }} else {{
                // Soft reload - pouze obnovíme data
                location.reload();
            }}
        }}

        // Aktualizace času
        function showTime() {{
            const now = new Date();
            const timeString = now.toLocaleTimeString('cs-CZ', {{
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }});
            document.getElementById("lastupdate").innerText = timeString;
        }}

        // Spuštění timerů
        setInterval(updateCountdown, 1000);
        setInterval(showTime, 1000);
        showTime();

        // Iframe komunikace s parent window
        function notifyParent(message) {{
            if (window.parent && window.parent !== window) {{
                window.parent.postMessage({{
                    type: 'solusdt-chart',
                    data: message
                }}, '*');
            }}
        }}

        // Oznámení o načtení
        window.addEventListener('load', function() {{
            notifyParent({{ status: 'loaded', timestamp: new Date().toISOString() }});
        }});

        // Oznámení o refreshi
        window.addEventListener('beforeunload', function() {{
            notifyParent({{ status: 'refreshing', timestamp: new Date().toISOString() }});
        }});

        // Responsive handling pro Plotly
        window.addEventListener('resize', function() {{
            if (window.Plotly) {{
                const graphDiv = document.querySelector('.js-plotly-plot');
                if (graphDiv) {{
                    window.Plotly.Plots.resize(graphDiv);
                }}
            }}
        }});

        // Touch handling pro mobilní zařízení
        document.addEventListener('touchstart', function(e) {{
            // Zabránění zoom na double-tap
            if (e.touches.length > 1) {{
                e.preventDefault();
            }}
        }}, {{ passive: false }});

        // Optimalizace pro iframe
        if (window.self !== window.top) {{
            // Jsme v iframe
            document.body.style.margin = '0';
            document.body.style.padding = '0';

            // Skryjeme některé Plotly controls pro iframe
            const style = document.createElement('style');
            style.textContent = `
                .modebar-btn[data-title="Download plot as a png"] {{ display: none !important; }}
                .modebar-btn[data-title="Edit in Chart Studio"] {{ display: none !important; }}
            `;
            document.head.appendChild(style);
        }}
    </script>
</body>
</html>"""
    return iframe_html

def create_minimal_version(html_str, cooldown_seconds):
    """Vytvoří minimální verzi pro malé iframe (bez header)."""
    minimal_html = f"""<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOLUSDT</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            background: #1e1e1e;
            color: #ffffff;
            overflow: hidden;
        }}

        .chart-container {{
            height: 100vh;
            width: 100%;
        }}

        .plotly-graph-div {{
            height: 100% !important;
            width: 100% !important;
        }}

        /* Skryjeme Plotly modebar pro minimální verzi */
        .modebar {{
            display: none !important;
        }}

        /* Countdown v rohu */
        .mini-countdown {{
            position: fixed;
            top: 5px;
            right: 5px;
            background: rgba(255, 165, 0, 0.9);
            color: #000;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            z-index: 1000;
        }}
    </style>
</head>
<body>
    <div class="mini-countdown">
        <span id="countdown">{cooldown_seconds}</span>s
    </div>

    <div class="chart-container">
        {html_str}
    </div>

    <script>
        // Minimální countdown
        let seconds = {cooldown_seconds};

        function updateCountdown() {{
            if (seconds > 0) {{
                seconds -= 1;
                document.getElementById("countdown").innerText = seconds;
            }} else {{
                location.reload();
            }}
        }}

        setInterval(updateCountdown, 1000);

        // Iframe optimalizace
        if (window.self !== window.top) {{
            document.body.style.margin = '0';
            document.body.style.padding = '0';
        }}

        // Responsive Plotly
        window.addEventListener('resize', function() {{
            if (window.Plotly) {{
                const graphDiv = document.querySelector('.js-plotly-plot');
                if (graphDiv) {{
                    window.Plotly.Plots.resize(graphDiv);
                }}
            }}
        }});

        // Parent komunikace
        if (window.parent && window.parent !== window) {{
            window.addEventListener('load', function() {{
                window.parent.postMessage({{
                    type: 'solusdt-minimal',
                    data: {{ status: 'loaded' }}
                }}, '*');
            }});
        }}
    </script>
</body>
</html>"""
    return minimal_html

def plot_future_predictions(
    df, future_lstm, future_chaos,
    cooldown_seconds=60,
    poslednich_svic=60
):
    # Serad data podle timestampu vzestupne
    df = df.sort_values('timestamp').reset_index(drop=True)

    # Casova zona Praha
    tz = pytz.timezone('Europe/Prague')
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True).dt.tz_convert(tz)

    # Vyber pouze poslednich N svic
    df_view = df

    # Priprava osy X
    real_x = [t.strftime('%H:%M') for t in df_view['datetime']]
    last_timestamp = df_view['timestamp'].iloc[-1]
    future_timestamps = [last_timestamp + (i+1)*60*1000 for i in range(len(future_lstm))]
    future_times = pd.to_datetime(future_timestamps, unit='ms', utc=True).tz_convert(tz)
    future_x = [t.strftime('%H:%M') for t in future_times]

    print("Zobrazuju poslednich {} svic:".format(len(df_view)))
    print(df_view[['datetime', 'open', 'high', 'low', 'close']].tail(10))
    print("Posledni cas na ose X:", real_x[-1])
    print("Predikce zacina od:", future_x[0])
    print("Aktualni systemovy cas:", pd.Timestamp.now(tz='Europe/Prague'))

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=real_x, y=df_view['close'].values, mode='lines', name='Real Close', line=dict(color='royalblue')
    ))
    fig.add_trace(go.Scatter(
        x=future_x, y=future_lstm, mode='lines+markers', name='KNN Predikce (do budoucna)', line=dict(color='orange')
    ))
    fig.add_trace(go.Scatter(
        x=future_x, y=future_chaos, mode='lines+markers', name='Chaos Predikce (do budoucna)', line=dict(color='green')
    ))
    fig.add_shape(type='line',
                  x0=real_x[-1], y0=min(df_view['close'].min(), min(future_lstm), min(future_chaos)),
                  x1=real_x[-1], y1=max(df_view['close'].max(), max(future_lstm), max(future_chaos)),
                  line=dict(color='white', width=2, dash='dot'))
    fig.update_layout(
        title='SOLUSDT: Realna data + Predikce do budoucna',
        xaxis_title='Cas (HH:MM) [Evropa/Prague]',
        yaxis_title='Cena (USD)',
        template='plotly_dark'
    )

    html_str = plot(fig, include_plotlyjs=True, output_type='div', auto_open=False)

    custom_html = f"""
    <html>
    <head>
        <title>SOLUSDT: Realna data + Predikce do budoucna</title>
        <meta charset="utf-8">
    </head>
    <body>
    <div style="position:fixed;top:10px;right:20px;z-index:1000;background:rgba(20,20,20,0.85);padding:10px 18px;border-radius:12px;">
        <span style="font-size:22px;font-weight:bold;color:#ffa500;">Cooldown: <span id="countdown">{cooldown_seconds}</span> s</span><br>
        <span style="font-size:16px;color:#ccc;">Aktualni cas: <span id="lastupdate"></span></span>
    </div>
    {html_str}
    <script>
    var seconds = {cooldown_seconds};
    function updateCountdown() {{
        if(seconds > 0) {{
            seconds -= 1;
            document.getElementById("countdown").innerText = seconds;
        }} else {{
            location.reload();
        }}
    }}
    setInterval(updateCountdown, 1000);

    function showTime() {{
        var now = new Date();
        document.getElementById("lastupdate").innerText = now.toLocaleTimeString();
    }}
    setInterval(showTime, 1000);
    showTime();
    </script>
    </body>
    </html>
    """

    # Původní verze pro standalone použití
    with open('solusdt_future_predictions.html', 'w', encoding='utf-8') as f:
        f.write(custom_html)

    # Iframe-friendly verze
    iframe_html = create_iframe_version(html_str, cooldown_seconds)
    with open('solusdt_iframe.html', 'w', encoding='utf-8') as f:
        f.write(iframe_html)

    # Minimální verze pro malé iframe
    minimal_html = create_minimal_version(html_str, cooldown_seconds)
    with open('solusdt_minimal.html', 'w', encoding='utf-8') as f:
        f.write(minimal_html)
