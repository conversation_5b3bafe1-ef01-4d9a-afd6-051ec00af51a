"""
Skript pro spuštění predikčního systému jako služby na Windows.
Používá knihovnu win32serviceutil pro vytvoření a správu služby.
"""
import os
import sys
import time
import logging
import subprocess
import servicemanager
import win32event
import win32service
import win32serviceutil

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='prediction_service.log',
    filemode='a'
)
logger = logging.getLogger(__name__)

class PredictionService(win32serviceutil.ServiceFramework):
    """Služba pro kontinuální běh predikčního systému."""
    
    _svc_name_ = "PredictionService"
    _svc_display_name_ = "Prediction System Service"
    _svc_description_ = "Služba pro kontinuální běh predikčního systému pro kryptoměny"
    
    def __init__(self, args):
        """Inicializace služby."""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.stop_event = win32event.CreateEvent(None, 0, 0, None)
        self.process = None
    
    def SvcStop(self):
        """Zastavení služby."""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.stop_event)
        
        # Ukončení procesu
        if self.process:
            try:
                # Poslání signálu CTRL+C procesu
                self.process.terminate()
                
                # Čekání na ukončení procesu
                self.process.wait(timeout=10)
                
                logger.info("Proces úspěšně ukončen")
            except Exception as e:
                logger.error(f"Chyba při ukončování procesu: {e}")
                
                # Pokud se nepodařilo ukončit proces, zkusíme ho zabít
                try:
                    self.process.kill()
                    logger.info("Proces byl násilně ukončen")
                except Exception as e:
                    logger.error(f"Chyba při násilném ukončování procesu: {e}")
    
    def SvcDoRun(self):
        """Spuštění služby."""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        self.main()
    
    def main(self):
        """Hlavní funkce služby."""
        logger.info("Služba predikčního systému byla spuštěna")
        
        # Cesta k Python interpreteru
        python_exe = sys.executable
        
        # Cesta ke skriptu continuous_run.py
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "continuous_run.py")
        
        # Argumenty pro continuous_run.py
        args = [
            "--interval", "60",
            "--ensemble",
            "--use_sentiment",
            "--update_sentiment"
        ]
        
        # Sestavení příkazu
        cmd = [python_exe, script_path] + args
        
        try:
            # Spuštění procesu
            logger.info(f"Spouštím příkaz: {' '.join(cmd)}")
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            # Čekání na ukončení služby
            while True:
                # Kontrola, zda služba má být ukončena
                if win32event.WaitForSingleObject(self.stop_event, 1000) == win32event.WAIT_OBJECT_0:
                    break
                
                # Kontrola, zda proces stále běží
                if self.process.poll() is not None:
                    logger.error(f"Proces neočekávaně skončil s návratovým kódem {self.process.returncode}")
                    
                    # Čtení výstupu a chyb
                    stdout, stderr = self.process.communicate()
                    
                    if stdout:
                        logger.info(f"Výstup procesu: {stdout}")
                    
                    if stderr:
                        logger.error(f"Chyby procesu: {stderr}")
                    
                    # Restart procesu
                    logger.info("Restartuji proces...")
                    self.process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                    )
        except Exception as e:
            logger.error(f"Chyba v hlavní smyčce služby: {e}")
        finally:
            # Ukončení procesu, pokud stále běží
            if self.process and self.process.poll() is None:
                try:
                    self.process.terminate()
                    self.process.wait(timeout=10)
                except Exception as e:
                    logger.error(f"Chyba při ukončování procesu: {e}")
                    
                    try:
                        self.process.kill()
                    except Exception as e:
                        logger.error(f"Chyba při násilném ukončování procesu: {e}")
            
            logger.info("Služba predikčního systému byla ukončena")

if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(PredictionService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(PredictionService)
