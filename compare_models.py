"""
Skript pro porovnání výkonu různ<PERSON>ch predikčn<PERSON>ch modelů.
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

# ANSI color codes
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def compare_model_performance():
    """Porovná výkon různých predikčních modelů."""
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print(f"{Colors.BOLD}📊 POROVNÁNÍ VÝKONU PREDIKČNÍCH MODELŮ{Colors.ENDC}")
        print("=" * 60)

        # Získ<PERSON>me statistiky za posledních 24 hodin
        cursor.execute("""
        SELECT
            ISNULL(model_type, 'UNKNOWN') as model_type,
            COUNT(*) as total_predictions,
            COUNT(actual_price) as completed_predictions,
            AVG(CASE WHEN actual_price IS NOT NULL THEN prediction_error END) as avg_error,
            AVG(CASE WHEN actual_price IS NOT NULL THEN prediction_error_pct END) as avg_error_pct,
            MIN(prediction_time) as first_prediction,
            MAX(prediction_time) as last_prediction
        FROM PREDICTIONS
        WHERE prediction_time >= DATEADD(hour, -24, GETUTCDATE())
        GROUP BY model_type
        ORDER BY avg_error ASC
        """)

        results = cursor.fetchall()

        if not results:
            print(f"{Colors.YELLOW}❌ Žádné predikce za posledních 24 hodin.{Colors.ENDC}")
            return

        print(f"{Colors.BLUE}{'Model':^20} | {'Celkem':^8} | {'Dokončeno':^10} | {'Avg Error':^12} | {'Avg Error %':^12} | {'První':^16} | {'Poslední':^16}{Colors.ENDC}")
        print("-" * 110)

        for row in results:
            model_type = row[0]
            total = row[1]
            completed = row[2] if row[2] else 0
            avg_error = f"{row[3]:.4f}" if row[3] else "N/A"
            avg_error_pct = f"{row[4]:.2f}%" if row[4] else "N/A"
            # Převedeme stringy na datetime objekty pokud je potřeba
            first_pred_obj = row[5]
            last_pred_obj = row[6]

            if isinstance(first_pred_obj, str):
                first_pred_obj = pd.to_datetime(first_pred_obj) if first_pred_obj else None
            if isinstance(last_pred_obj, str):
                last_pred_obj = pd.to_datetime(last_pred_obj) if last_pred_obj else None

            first_pred = first_pred_obj.strftime('%m-%d %H:%M') if first_pred_obj else "N/A"
            last_pred = last_pred_obj.strftime('%m-%d %H:%M') if last_pred_obj else "N/A"

            # Barevné označení podle typu modelu
            if "ENSEMBLE" in model_type:
                model_color = Colors.GREEN
            elif "XGBOOST" in model_type:
                model_color = Colors.BLUE
            else:
                model_color = Colors.YELLOW

            completion_rate = (completed / total * 100) if total > 0 else 0

            print(f"{model_color}{model_type:^20}{Colors.ENDC} | {total:^8} | {completed:^10} | {avg_error:^12} | {avg_error_pct:^12} | {first_pred:^16} | {last_pred:^16}")

        # Detailní porovnání pro dokončené predikce
        print(f"\n{Colors.BOLD}🎯 DETAILNÍ ANALÝZA PŘESNOSTI{Colors.ENDC}")
        print("-" * 60)

        cursor.execute("""
        SELECT
            ISNULL(model_type, 'UNKNOWN') as model_type,
            COUNT(CASE WHEN prediction_error < 0.5 THEN 1 END) as excellent,
            COUNT(CASE WHEN prediction_error >= 0.5 AND prediction_error < 2.0 THEN 1 END) as good,
            COUNT(CASE WHEN prediction_error >= 2.0 THEN 1 END) as poor,
            COUNT(*) as total_completed
        FROM PREDICTIONS
        WHERE prediction_time >= DATEADD(hour, -24, GETUTCDATE())
            AND actual_price IS NOT NULL
        GROUP BY model_type
        ORDER BY model_type
        """)

        accuracy_results = cursor.fetchall()

        for row in accuracy_results:
            model_type = row[0]
            excellent = row[1]
            good = row[2]
            poor = row[3]
            total = row[4]

            if total > 0:
                excellent_pct = (excellent / total) * 100
                good_pct = (good / total) * 100
                poor_pct = (poor / total) * 100

                print(f"\n{Colors.HEADER}{model_type}:{Colors.ENDC}")
                print(f"  {Colors.GREEN}Výborná (<0.5 USDT): {excellent} ({excellent_pct:.1f}%){Colors.ENDC}")
                print(f"  {Colors.YELLOW}Dobrá (0.5-2.0 USDT): {good} ({good_pct:.1f}%){Colors.ENDC}")
                print(f"  {Colors.RED}Slabá (>2.0 USDT): {poor} ({poor_pct:.1f}%){Colors.ENDC}")

        # Trend analýza
        print(f"\n{Colors.BOLD}📈 TREND ANALÝZA (posledních 10 predikcí){Colors.ENDC}")
        print("-" * 60)

        for model_result in results:
            model_type = model_result[0]

            cursor.execute("""
            SELECT TOP 10
                predicted_price,
                actual_price,
                prediction_error,
                prediction_time
            FROM PREDICTIONS
            WHERE model_type = ?
                AND actual_price IS NOT NULL
            ORDER BY prediction_time DESC
            """, (model_type,))

            recent_preds = cursor.fetchall()

            if len(recent_preds) >= 3:
                errors = [float(row[2]) for row in recent_preds if row[2] is not None]
                if errors:
                    recent_avg_error = sum(errors) / len(errors)
                    trend = "zlepšuje se" if len(errors) > 1 and errors[0] < errors[-1] else "zhoršuje se" if len(errors) > 1 and errors[0] > errors[-1] else "stabilní"

                    trend_color = Colors.GREEN if "zlepšuje" in trend else Colors.RED if "zhoršuje" in trend else Colors.BLUE

                    print(f"{model_type}: Průměrná chyba {recent_avg_error:.4f} USDT - {trend_color}{trend}{Colors.ENDC}")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    compare_model_performance()
