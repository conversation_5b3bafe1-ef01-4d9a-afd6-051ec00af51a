"""
Skript pro spuštění celého predikčního systému.
"""
import os
import sys
import subprocess
import time
import signal
import logging
from datetime import datetime

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Seznam procesů
processes = []

def signal_handler(sig, frame):
    """Zpracování signálu pro ukončení skriptu."""
    logger.info("Přijat signál pro ukončení. Ukončuji běh...")

    # Ukončení všech procesů
    for process in processes:
        if process.poll() is None:  # Proces stále běží
            logger.info(f"Ukončuji proces: {process.args}")
            process.terminate()

    # Ukončení skriptu
    sys.exit(0)

def run_process(command):
    """Spustí proces a vrátí jeho instanci."""
    try:
        process = subprocess.Popen(command, shell=True)
        logger.info(f"Spuštěn proces: {command}")
        return process
    except Exception as e:
        logger.error(f"Chyba při spouštění procesu: {e}")
        return None

def main():
    """Hlavní funkce."""
    # Registrace handleru pro signály
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Zpracování argumentů příkazové řádky
    import argparse
    parser = argparse.ArgumentParser(description='Spuštění predikčního systému')
    parser.add_argument('--update-interval', type=int, default=60,
                        help='Interval aktualizace indikátorů v sekundách')
    parser.add_argument('--predict-interval', type=int, default=60,
                        help='Interval predikce v sekundách')
    parser.add_argument('--api', action='store_true',
                        help='Použít Bybit API pro aktualizaci indikátorů')

    args = parser.parse_args()

    try:
        # 1. Spuštění aktualizace indikátorů
        if args.api:
            update_cmd = f"python update_indicators_pybit.py --interval {args.update_interval}"
        else:
            update_cmd = f"python update_indicators_bybit.py --interval {args.update_interval}"

        update_process = run_process(update_cmd)
        if update_process:
            processes.append(update_process)

        # Predikce a graf budou spuštěny periodicky v hlavní smyčce

        # Čekání na ukončení procesů
        logger.info("Predikční systém běží. Stiskněte Ctrl+C pro ukončení.")

        # Časovače pro predikce a grafy
        last_predict_time = 0
        last_graph_time = 0

        # Definice příkazů
        predict_cmd = "python fix_prediction_v3.py"
        graph_cmd = "python simple_prediction_graph.py"

        while True:
            current_time = time.time()

            # Kontrola, zda procesy stále běží
            for i, process in enumerate(processes[:]):
                if process.poll() is not None:  # Proces skončil
                    logger.warning(f"Proces {process.args} skončil s návratovým kódem {process.returncode}")
                    processes.remove(process)

                    # Restartování procesu aktualizace indikátorů
                    if "update_indicators" in process.args:
                        logger.info("Restartování procesu aktualizace indikátorů")
                        new_process = run_process(update_cmd)
                        if new_process:
                            processes.append(new_process)

            # Spuštění predikce každou minutu
            if current_time - last_predict_time >= args.predict_interval:
                # Kontrola, zda už neběží jiná predikce
                predict_running = False
                for process in processes:
                    if "fix_prediction_v3" in process.args and process.poll() is None:
                        predict_running = True
                        break

                if not predict_running:
                    logger.info(f"Spuštím predikci (interval: {args.predict_interval}s)")
                    subprocess.run(predict_cmd, shell=True, check=False)
                    last_predict_time = current_time

            # Aktualizace grafu každých 5 minut
            if current_time - last_graph_time >= 300:  # 5 minut
                # Kontrola, zda už neběží jiná aktualizace grafu
                graph_running = False
                for process in processes:
                    if "simple_prediction_graph" in process.args and process.poll() is None:
                        graph_running = True
                        break

                if not graph_running:
                    logger.info("Aktualizuji graf predikcí")
                    subprocess.run(graph_cmd, shell=True, check=False)
                    last_graph_time = current_time

            # Pokud nejsou žádné procesy, ukončíme skript
            if not processes:
                logger.error("Všechny procesy skončily. Ukončuji běh.")
                break

            # Čekání
            time.sleep(5)
    except KeyboardInterrupt:
        logger.info("Přijat signál pro ukončení. Ukončuji běh...")
    except Exception as e:
        logger.error(f"Neočekávaná chyba: {e}")
    finally:
        # Ukončení všech procesů
        for process in processes:
            if process.poll() is None:  # Proces stále běží
                logger.info(f"Ukončuji proces: {process.args}")
                process.terminate()

if __name__ == "__main__":
    main()
