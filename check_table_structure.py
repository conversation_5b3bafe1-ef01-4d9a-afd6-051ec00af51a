"""
Skript pro zjištění struktury tabulky v databázi.
"""
import os
import logging
import pyodbc

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def get_table_structure(conn, table_name):
    """Zjistí strukturu tabulky."""
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Zjištění struktury tabulky
        cursor.execute(f"""
        SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        
        if not columns:
            logger.warning(f"Tabulka dbo.{table_name} neexistuje nebo nemá žádné sloupce.")
            return []
        
        logger.info(f"Struktura tabulky dbo.{table_name}:")
        for column in columns:
            column_name = column[0]
            data_type = column[1]
            max_length = column[2]
            
            if max_length is not None:
                logger.info(f"  {column_name}: {data_type}({max_length})")
            else:
                logger.info(f"  {column_name}: {data_type}")
        
        return columns
    except pyodbc.Error as e:
        logger.error(f"Chyba při zjišťování struktury tabulky: {e}")
        return []
    finally:
        if cursor:
            cursor.close()

def main():
    """Hlavní funkce."""
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi.")
        return
    
    try:
        # Zjištění struktury tabulky alch_indikatory
        get_table_structure(conn, "alch_indikatory")
        
        # Zjištění struktury tabulky alch_predictions
        get_table_structure(conn, "alch_predictions")
        
        # Zjištění struktury tabulky EXTERNAL_SENTIMENT
        get_table_structure(conn, "EXTERNAL_SENTIMENT")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
