"""
Modul pro detekci a klasifikaci tržn<PERSON><PERSON> podmínek.
Implementuje metody pro rozpoznání různých stavů trhu (volatilní/stabilní)
a výběr vhodného modelu pro aktuální podmínky.
"""
import os
import logging
import pandas as pd
import numpy as np
import joblib
import pyodbc
from datetime import datetime, timedelta
from typing import Dict, Tuple, List, Optional, Union, Any, Literal

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Konstanty pro detekci tržních podmínek
VOLATILITY_THRESHOLD_HIGH = 1.5  # Vysoká volatilita (%)
VOLATILITY_THRESHOLD_LOW = 0.5   # Nízká volatilita (%)
TREND_STRENGTH_THRESHOLD = 25    # Pr<PERSON>h sí<PERSON> (ADX)
VOLUME_RATIO_THRESHOLD = 1.5     # Práh poměru objemu k průměru
HISTORY_WINDOW = 24              # Okno pro výpočet historických průměrů (hodiny)

# Připojovací řetězec k databázi
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

# Typy tržních podmínek
MarketConditionType = Literal[
    'volatile_bullish',    # Volatilní s býčím trendem
    'volatile_bearish',    # Volatilní s medvědím trendem
    'volatile_neutral',    # Volatilní bez jasného trendu
    'stable_bullish',      # Stabilní s býčím trendem
    'stable_bearish',      # Stabilní s medvědím trendem
    'stable_neutral',      # Stabilní bez jasného trendu
    'ranging',             # Trh v rozmezí (bez trendu)
    'breakout',            # Průlom z rozmezí
    'reversal',            # Obrat trendu
    'unknown'              # Neznámé podmínky
]

def load_market_data(hours: int = HISTORY_WINDOW) -> pd.DataFrame:
    """
    Načte data o trhu z databáze.
    
    Args:
        hours: Počet hodin historie k načtení
        
    Returns:
        DataFrame s daty o trhu
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Načtení cenových dat
            price_query = f"""
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM alch_price_history 
            WHERE timestamp >= '{cutoff_time.strftime('%Y-%m-%d %H:%M:%S')}'
            ORDER BY timestamp ASC
            """
            df_price = pd.read_sql(price_query, conn)
            
            # Načtení indikátorů
            indicators_query = f"""
            SELECT timestamp, RSI, EMA9, EMA20, boll_high, boll_low, ATR
            FROM alch_indikatory 
            WHERE timestamp >= '{cutoff_time.strftime('%Y-%m-%d %H:%M:%S')}'
            ORDER BY timestamp ASC
            """
            df_indicators = pd.read_sql(indicators_query, conn)
            
            # Spojení dataframů
            df = pd.merge(df_price, df_indicators, on='timestamp', how='inner')
            
            logger.info(f"Načteno {len(df)} záznamů pro analýzu tržních podmínek")
            return df
    except Exception as e:
        logger.error(f"Chyba při načítání dat o trhu: {e}")
        return pd.DataFrame()

def calculate_volatility(df: pd.DataFrame) -> Tuple[float, float]:
    """
    Vypočítá aktuální volatilitu trhu.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        Tuple (aktuální volatilita, průměrná volatilita)
    """
    try:
        if df.empty:
            return 0.0, 0.0
        
        # Výpočet volatility jako procentuální rozdíl mezi high a low
        df['volatility'] = (df['high_price'] - df['low_price']) / df['close_price'] * 100
        
        # Aktuální volatilita (poslední hodnota)
        current_volatility = df['volatility'].iloc[-1]
        
        # Průměrná volatilita za celé období
        avg_volatility = df['volatility'].mean()
        
        return current_volatility, avg_volatility
    except Exception as e:
        logger.error(f"Chyba při výpočtu volatility: {e}")
        return 0.0, 0.0

def detect_trend(df: pd.DataFrame) -> Tuple[int, float]:
    """
    Detekuje trend trhu.
    
    Args:
        df: DataFrame s cenovými daty a indikátory
        
    Returns:
        Tuple (směr trendu, síla trendu)
        Směr trendu: 1 pro býčí, -1 pro medvědí, 0 pro neutrální
    """
    try:
        if df.empty:
            return 0, 0.0
        
        # Výpočet směru trendu na základě EMA
        ema_diff = df['EMA9'] - df['EMA20']
        ema_diff_change = ema_diff.diff()
        
        # Poslední hodnoty
        last_ema_diff = ema_diff.iloc[-1]
        last_ema_diff_change = ema_diff_change.iloc[-1]
        
        # RSI pro potvrzení trendu
        last_rsi = df['RSI'].iloc[-1]
        
        # Určení směru trendu
        if last_ema_diff > 0 and last_ema_diff_change > 0:
            trend_direction = 1  # Býčí trend
        elif last_ema_diff < 0 and last_ema_diff_change < 0:
            trend_direction = -1  # Medvědí trend
        else:
            trend_direction = 0  # Neutrální nebo nejasný trend
        
        # Potvrzení pomocí RSI
        if trend_direction == 1 and last_rsi < 40:
            trend_direction = 0  # Rozpor s RSI
        elif trend_direction == -1 and last_rsi > 60:
            trend_direction = 0  # Rozpor s RSI
        
        # Výpočet síly trendu (0-100)
        # Použijeme absolutní hodnotu rozdílu EMA jako aproximaci síly trendu
        trend_strength = min(100, abs(last_ema_diff) * 10)
        
        return trend_direction, trend_strength
    except Exception as e:
        logger.error(f"Chyba při detekci trendu: {e}")
        return 0, 0.0

def detect_volume_anomaly(df: pd.DataFrame) -> bool:
    """
    Detekuje anomálie v objemu obchodů.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        True pokud je detekována anomálie v objemu, jinak False
    """
    try:
        if df.empty or 'volume' not in df.columns:
            return False
        
        # Výpočet průměrného objemu
        avg_volume = df['volume'].mean()
        
        # Poslední objem
        last_volume = df['volume'].iloc[-1]
        
        # Detekce anomálie
        return last_volume > avg_volume * VOLUME_RATIO_THRESHOLD
    except Exception as e:
        logger.error(f"Chyba při detekci anomálie objemu: {e}")
        return False

def detect_breakout(df: pd.DataFrame) -> bool:
    """
    Detekuje průlom z cenového rozmezí.
    
    Args:
        df: DataFrame s cenovými daty a indikátory
        
    Returns:
        True pokud je detekován průlom, jinak False
    """
    try:
        if df.empty or len(df) < 10:
            return False
        
        # Použití Bollingerových pásem pro detekci průlomu
        last_close = df['close_price'].iloc[-1]
        last_boll_high = df['boll_high'].iloc[-1]
        last_boll_low = df['boll_low'].iloc[-1]
        
        # Kontrola předchozích hodnot (před 3 periodami)
        prev_idx = -4
        if len(df) > abs(prev_idx):
            prev_close = df['close_price'].iloc[prev_idx]
            prev_boll_high = df['boll_high'].iloc[prev_idx]
            prev_boll_low = df['boll_low'].iloc[prev_idx]
            
            # Detekce průlomu nahoru
            breakout_up = (prev_close < prev_boll_high) and (last_close > last_boll_high)
            
            # Detekce průlomu dolů
            breakout_down = (prev_close > prev_boll_low) and (last_close < last_boll_low)
            
            return breakout_up or breakout_down
        
        return False
    except Exception as e:
        logger.error(f"Chyba při detekci průlomu: {e}")
        return False

def detect_reversal(df: pd.DataFrame) -> bool:
    """
    Detekuje obrat trendu.
    
    Args:
        df: DataFrame s cenovými daty a indikátory
        
    Returns:
        True pokud je detekován obrat trendu, jinak False
    """
    try:
        if df.empty or len(df) < 10:
            return False
        
        # Výpočet změny směru RSI
        rsi_change = df['RSI'].diff()
        
        # Poslední hodnoty
        last_rsi = df['RSI'].iloc[-1]
        last_rsi_change = rsi_change.iloc[-1]
        
        # Detekce obratu nahoru (z přeprodaného stavu)
        reversal_up = (last_rsi < 30) and (last_rsi_change > 0)
        
        # Detekce obratu dolů (z překoupeného stavu)
        reversal_down = (last_rsi > 70) and (last_rsi_change < 0)
        
        return reversal_up or reversal_down
    except Exception as e:
        logger.error(f"Chyba při detekci obratu trendu: {e}")
        return False

def detect_market_condition(df: pd.DataFrame = None) -> MarketConditionType:
    """
    Detekuje aktuální tržní podmínky.
    
    Args:
        df: DataFrame s cenovými daty a indikátory (volitelné)
        
    Returns:
        Typ tržních podmínek
    """
    try:
        # Načtení dat, pokud nejsou poskytnuta
        if df is None or df.empty:
            df = load_market_data()
        
        if df.empty:
            logger.warning("Nedostatek dat pro detekci tržních podmínek")
            return 'unknown'
        
        # Výpočet volatility
        current_volatility, avg_volatility = calculate_volatility(df)
        
        # Detekce trendu
        trend_direction, trend_strength = detect_trend(df)
        
        # Detekce anomálií
        volume_anomaly = detect_volume_anomaly(df)
        breakout_detected = detect_breakout(df)
        reversal_detected = detect_reversal(df)
        
        # Logování detekovaných podmínek
        logger.info(f"Volatilita: {current_volatility:.2f}% (průměr: {avg_volatility:.2f}%)")
        logger.info(f"Trend: směr={trend_direction}, síla={trend_strength:.2f}")
        logger.info(f"Anomálie objemu: {volume_anomaly}")
        logger.info(f"Průlom: {breakout_detected}")
        logger.info(f"Obrat trendu: {reversal_detected}")
        
        # Určení tržních podmínek
        
        # Nejprve kontrola speciálních podmínek
        if reversal_detected:
            return 'reversal'
        
        if breakout_detected:
            return 'breakout'
        
        # Kontrola volatility a trendu
        is_volatile = current_volatility > VOLATILITY_THRESHOLD_HIGH
        is_stable = current_volatility < VOLATILITY_THRESHOLD_LOW
        has_trend = trend_strength > TREND_STRENGTH_THRESHOLD
        
        if is_volatile:
            if trend_direction > 0 and has_trend:
                return 'volatile_bullish'
            elif trend_direction < 0 and has_trend:
                return 'volatile_bearish'
            else:
                return 'volatile_neutral'
        elif is_stable:
            if trend_direction > 0 and has_trend:
                return 'stable_bullish'
            elif trend_direction < 0 and has_trend:
                return 'stable_bearish'
            else:
                return 'stable_neutral'
        else:
            # Střední volatilita bez jasného trendu
            if not has_trend:
                return 'ranging'
            elif trend_direction > 0:
                return 'stable_bullish'
            elif trend_direction < 0:
                return 'stable_bearish'
            else:
                return 'stable_neutral'
    except Exception as e:
        logger.error(f"Chyba při detekci tržních podmínek: {e}")
        return 'unknown'

def get_model_for_condition(condition: MarketConditionType) -> str:
    """
    Vrátí cestu k modelu vhodnému pro dané tržní podmínky.
    
    Args:
        condition: Typ tržních podmínek
        
    Returns:
        Cesta k souboru modelu
    """
    # Mapování podmínek na modely
    model_map = {
        'volatile_bullish': 'alch_model_volatile_bullish.pkl',
        'volatile_bearish': 'alch_model_volatile_bearish.pkl',
        'volatile_neutral': 'alch_model_volatile_neutral.pkl',
        'stable_bullish': 'alch_model_stable_bullish.pkl',
        'stable_bearish': 'alch_model_stable_bearish.pkl',
        'stable_neutral': 'alch_model_stable_neutral.pkl',
        'ranging': 'alch_model_ranging.pkl',
        'breakout': 'alch_model_breakout.pkl',
        'reversal': 'alch_model_reversal.pkl',
        'unknown': 'alch_price_model.pkl'  # Výchozí model
    }
    
    # Kontrola, zda model existuje
    model_path = model_map.get(condition, 'alch_price_model.pkl')
    
    if not os.path.exists(model_path):
        logger.warning(f"Model pro podmínky '{condition}' neexistuje, použije se výchozí model")
        return 'alch_price_model.pkl'
    
    return model_path

def train_condition_specific_model(condition: MarketConditionType) -> bool:
    """
    Trénuje model specifický pro dané tržní podmínky.
    
    Args:
        condition: Typ tržních podmínek
        
    Returns:
        True pokud bylo trénování úspěšné, jinak False
    """
    try:
        # Načtení dat
        df = load_market_data(hours=72)  # Použijeme delší historii
        
        if df.empty:
            logger.warning("Nedostatek dat pro trénování modelu")
            return False
        
        # Detekce tržních podmínek pro každý řádek
        market_conditions = []
        
        # Pro jednoduchost použijeme klouzavé okno
        window_size = 20
        for i in range(window_size, len(df)):
            window_df = df.iloc[i-window_size:i]
            condition_i = detect_market_condition(window_df)
            market_conditions.append(condition_i)
        
        # Přidání podmínek do DataFrame
        condition_df = df.iloc[window_size:].copy()
        condition_df['market_condition'] = market_conditions
        
        # Filtrování dat podle požadovaných podmínek
        filtered_df = condition_df[condition_df['market_condition'] == condition]
        
        if len(filtered_df) < 100:
            logger.warning(f"Nedostatek dat pro trénování modelu pro podmínky '{condition}' (nalezeno {len(filtered_df)} záznamů)")
            return False
        
        # Příprava dat pro trénování
        features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
        X = filtered_df[features]
        y = filtered_df["close_price"].shift(-5)  # Predikce ceny za 5 minut
        
        # Odstranění NaN hodnot
        X = X[:-5]
        y = y[:-5].dropna()
        
        # Zarovnání indexů
        X = X.loc[y.index]
        
        # Import zde, abychom předešli cyklickým závislostem
        from adaptive_learning import retrain_model
        
        # Určení cesty k modelu
        model_path = get_model_for_condition(condition)
        
        # Trénování modelu s vlastními parametry
        custom_params = {
            'n_estimators': 200,
            'learning_rate': 0.05,
            'max_depth': 5,
            'min_child_weight': 3,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0
        }
        
        # Úprava parametrů podle podmínek
        if 'volatile' in condition:
            # Pro volatilní trh zvýšíme regularizaci
            custom_params['reg_alpha'] *= 1.5
            custom_params['reg_lambda'] *= 1.5
            custom_params['learning_rate'] *= 0.8
        elif 'stable' in condition:
            # Pro stabilní trh zvýšíme kapacitu modelu
            custom_params['n_estimators'] *= 1.2
            custom_params['max_depth'] += 1
        elif condition == 'breakout':
            # Pro průlomy potřebujeme rychlejší reakci
            custom_params['learning_rate'] *= 1.2
        elif condition == 'reversal':
            # Pro obraty trendu potřebujeme vyšší přesnost
            custom_params['n_estimators'] *= 1.5
            
        # Trénování modelu
        success = retrain_model(model_path=model_path, days=3, custom_params=custom_params)
        
        if success:
            logger.info(f"Model pro podmínky '{condition}' úspěšně natrénován")
        else:
            logger.warning(f"Trénování modelu pro podmínky '{condition}' selhalo")
        
        return success
    except Exception as e:
        logger.error(f"Chyba při trénování modelu pro podmínky '{condition}': {e}")
        return False

def get_best_model_for_current_conditions() -> str:
    """
    Vrátí nejlepší model pro aktuální tržní podmínky.
    
    Returns:
        Cesta k souboru modelu
    """
    try:
        # Detekce aktuálních tržních podmínek
        condition = detect_market_condition()
        
        logger.info(f"Aktuální tržní podmínky: {condition}")
        
        # Získání vhodného modelu
        model_path = get_model_for_condition(condition)
        
        # Pokud model neexistuje a nejsou to 'unknown' podmínky, zkusíme ho natrénovat
        if not os.path.exists(model_path) and condition != 'unknown':
            logger.info(f"Model pro podmínky '{condition}' neexistuje, pokusím se ho natrénovat")
            success = train_condition_specific_model(condition)
            
            if not success:
                logger.warning(f"Nelze natrénovat model pro podmínky '{condition}', použije se výchozí model")
                model_path = 'alch_price_model.pkl'
        
        logger.info(f"Vybrán model: {model_path}")
        return model_path
    except Exception as e:
        logger.error(f"Chyba při výběru modelu: {e}")
        return 'alch_price_model.pkl'

if __name__ == "__main__":
    # Při spuštění jako samostatný skript detekujeme aktuální tržní podmínky
    condition = detect_market_condition()
    print(f"Aktuální tržní podmínky: {condition}")
    
    # Získání vhodného modelu
    model_path = get_best_model_for_current_conditions()
    print(f"Doporučený model: {model_path}")
