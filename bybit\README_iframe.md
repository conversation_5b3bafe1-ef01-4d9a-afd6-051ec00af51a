# 📊 SOLUSDT Graf pro Iframe

Tento dokument popisuje, jak integrovat SOLUSDT predikční graf do vašeho webu pomocí iframe.

## 🚀 R<PERSON>lý start

### 1. Generování iframe-friendly HTML

Po spuštění `main.py` se automaticky vytvoří dva soubory:
- `solusdt_future_predictions.html` - původní standalone verze
- `solusdt_iframe.html` - optimalizovaná verze pro iframe

### 2. Základní iframe integrace

```html
<iframe 
    src="solusdt_iframe.html"
    width="100%"
    height="600"
    frameborder="0"
    title="SOLUSDT Predikční graf">
</iframe>
```

### 3. Pokročilá integrace

Použijte ukázkový soubor `iframe_example.html` jako šablonu pro váš web.

## 🎨 Vlastnosti iframe verze

### ✅ Optimalizace pro iframe:
- **Responzivní design** - přizpůsobí se velikosti iframe
- **Kompaktní header** - minimální místo pro maximum grafu
- **Touch-friendly** - optimalizováno pro mobilní zařízení
- **Auto-refresh** - automatické obnovování dat
- **Parent komunikace** - posílá statusy do rodičovského okna

### 📱 Responzivní breakpointy:
- **Desktop** (>768px): Plný header, výška 600px
- **Tablet** (≤768px): Kompaktní header, výška 500px  
- **Mobile** (≤480px): Minimální header, výška 400px

### 🎛️ Skryté funkce v iframe:
- Download PNG tlačítko
- Edit in Chart Studio tlačítko
- Některé Plotly modebar funkce

## 💬 Komunikace s parent window

Iframe posílá zprávy do rodičovského okna:

```javascript
// Naslouchání zprávám z iframe
window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'solusdt-chart') {
        console.log('Status:', event.data.data.status);
        // Možné statusy: 'loaded', 'refreshing', 'error'
    }
});
```

## 🔧 Customizace

### Změna velikosti iframe:

```css
.chart-iframe {
    width: 100%;
    height: 600px; /* Upravte podle potřeby */
    border: none;
    border-radius: 8px; /* Volitelné zaoblení */
}
```

### Responsive iframe:

```css
/* Responzivní výška */
@media (max-width: 768px) {
    .chart-iframe {
        height: 500px;
    }
}

@media (max-width: 480px) {
    .chart-iframe {
        height: 400px;
    }
}
```

### Styling kontejneru:

```css
.chart-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    margin: 20px 0;
}
```

## 🌐 Integrace do různých CMS

### WordPress:
```html
<!-- V HTML bloku nebo custom HTML widget -->
<div style="width: 100%; height: 600px;">
    <iframe src="path/to/solusdt_iframe.html" 
            width="100%" height="100%" frameborder="0">
    </iframe>
</div>
```

### React:
```jsx
function SOLUSDTChart() {
    return (
        <iframe
            src="/charts/solusdt_iframe.html"
            width="100%"
            height="600"
            frameBorder="0"
            title="SOLUSDT Predikce"
            style={{ border: 'none', borderRadius: '8px' }}
        />
    );
}
```

### Vue.js:
```vue
<template>
    <iframe
        src="/charts/solusdt_iframe.html"
        width="100%"
        height="600"
        frameborder="0"
        title="SOLUSDT Predikce"
        class="chart-iframe"
    />
</template>
```

## 🔒 Bezpečnost

### CSP (Content Security Policy):
```html
<meta http-equiv="Content-Security-Policy" 
      content="frame-src 'self' https://yourdomain.com;">
```

### X-Frame-Options:
Pokud hostujete iframe na jiné doméně, nastavte:
```
X-Frame-Options: SAMEORIGIN
```

## 📊 Monitoring a analytics

### Google Analytics v iframe:
```javascript
// V iframe HTML
gtag('config', 'GA_MEASUREMENT_ID', {
    'custom_map': {'custom_parameter_1': 'chart_view'}
});
```

### Error tracking:
```javascript
window.addEventListener('error', function(e) {
    // Pošlete error do vašeho monitoring systému
    console.error('Chart iframe error:', e);
});
```

## 🚨 Troubleshooting

### Časté problémy:

1. **Iframe se nenačte:**
   - Zkontrolujte cestu k `solusdt_iframe.html`
   - Ověřte, že soubor existuje a je dostupný

2. **Graf se nezobrazuje:**
   - Zkontrolujte konzoli prohlížeče
   - Ověřte, že Plotly.js se načetl správně

3. **Responzivita nefunguje:**
   - Nastavte `width="100%"` na iframe
   - Použijte CSS media queries

4. **Auto-refresh nefunguje:**
   - Zkontrolujte, že `main.py` běží
   - Ověřte, že se generuje nový `solusdt_iframe.html`

### Debug mode:
Přidejte do iframe URL parametr `?debug=1` pro debug informace.

## 📞 Podpora

Pro další otázky nebo problémy:
1. Zkontrolujte konzoli prohlížeče
2. Ověřte, že všechny soubory jsou na správném místě
3. Otestujte iframe v různých prohlížečích

## 🔄 Aktualizace

Iframe se automaticky obnovuje každých 60 sekund (nebo podle nastavení `cooldown_seconds`).
Pro manuální refresh můžete použít:

```javascript
document.getElementById('your-iframe-id').src += '';
```
