"""
Skript pro přidání sloupce interval_seconds do tabulky PREDICTIONS
pro sledování intervalů mezi predikcemi.
"""

import pyodbc

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

def add_interval_column():
    """Přidá sloupec interval_seconds do tabulky PREDICTIONS."""
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        print("🔧 Kontroluji existenci sloupce interval_seconds...")
        
        # Zkontrolujeme, zda sloupec již existuje
        cursor.execute("""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'PREDICTIONS' 
        AND COLUMN_NAME = 'interval_seconds'
        """)
        
        column_exists = cursor.fetchone()[0] > 0
        
        if column_exists:
            print("✅ Sloupec interval_seconds již existuje.")
        else:
            print("➕ Přidávám sloupec interval_seconds...")
            
            # Přidáme sloupec
            cursor.execute("""
            ALTER TABLE PREDICTIONS
            ADD interval_seconds INT NULL
            """)
            
            print("✅ Sloupec interval_seconds byl úspěšně přidán.")
            
            # Aktualizujeme existující záznamy s default hodnotou
            print("🔄 Aktualizuji existující záznamy...")
            cursor.execute("""
            UPDATE PREDICTIONS
            SET interval_seconds = 60
            WHERE interval_seconds IS NULL
            """)
            
            rows_updated = cursor.rowcount
            print(f"✅ Aktualizováno {rows_updated} existujících záznamů (nastaveno na 60s).")
        
        conn.commit()
        
        # Zobrazíme statistiky
        print("\n📊 Statistiky tabulky PREDICTIONS:")
        cursor.execute("""
        SELECT 
            ISNULL(model_type, 'NULL') as model_type,
            ISNULL(interval_seconds, 0) as interval_seconds,
            COUNT(*) as count
        FROM PREDICTIONS
        GROUP BY model_type, interval_seconds
        ORDER BY model_type, interval_seconds
        """)
        
        stats = cursor.fetchall()
        for row in stats:
            print(f"   {row[0]} ({row[1]}s): {row[2]} záznamů")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Operace dokončena úspěšně!")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    add_interval_column()
