"""
Diagnostický skript pro kontrolu GPU a CUDA.
Tento skript provede podrobnou diagnostiku GPU a CUDA konfigurace.
"""
import os
import sys
import subprocess
import logging
import platform

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_system_info():
    """Zobrazí základní informace o systému."""
    logger.info("=== Systémové informace ===")
    logger.info(f"Operační systém: {platform.system()} {platform.release()} {platform.version()}")
    logger.info(f"Python verze: {platform.python_version()}")
    logger.info(f"Procesor: {platform.processor()}")
    logger.info(f"Architektura: {platform.architecture()[0]}")

def check_nvidia_smi():
    """Zkontroluje GPU pomocí nvidia-smi."""
    logger.info("\n=== Kontrola GPU pomocí nvidia-smi ===")
    try:
        result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        if result.returncode == 0:
            logger.info("nvidia-smi výstup:")
            for line in result.stdout.split('\n'):
                logger.info(f"  {line}")
            return True
        else:
            logger.warning(f"nvidia-smi selhal s chybou: {result.stderr}")
            return False
    except Exception as e:
        logger.warning(f"Nelze spustit nvidia-smi: {e}")
        return False

def check_cuda_nvcc():
    """Zkontroluje CUDA pomocí nvcc."""
    logger.info("\n=== Kontrola CUDA pomocí nvcc ===")
    try:
        result = subprocess.run(['nvcc', '--version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        if result.returncode == 0:
            logger.info("nvcc výstup:")
            for line in result.stdout.split('\n'):
                logger.info(f"  {line}")
            return True
        else:
            logger.warning(f"nvcc selhal s chybou: {result.stderr}")
            return False
    except Exception as e:
        logger.warning(f"Nelze spustit nvcc: {e}")
        return False

def check_environment_variables():
    """Zkontroluje proměnné prostředí související s CUDA."""
    logger.info("\n=== Kontrola proměnných prostředí ===")
    cuda_vars = ['CUDA_HOME', 'CUDA_PATH', 'PATH', 'LD_LIBRARY_PATH']
    for var in cuda_vars:
        if var in os.environ:
            logger.info(f"  {var}: {os.environ[var]}")
        else:
            logger.info(f"  {var}: není nastaven")

def check_pytorch_gpu():
    """Zkontroluje GPU pomocí PyTorch."""
    logger.info("\n=== Kontrola GPU pomocí PyTorch ===")
    try:
        import torch
        logger.info(f"PyTorch verze: {torch.__version__}")

        if torch.cuda.is_available():
            logger.info(f"CUDA je dostupná pro PyTorch")
            logger.info(f"CUDA verze: {torch.version.cuda}")
            logger.info(f"Počet GPU: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            logger.warning("CUDA není dostupná pro PyTorch")
            return False
    except ImportError:
        logger.warning("PyTorch není nainstalován")
        return False
    except Exception as e:
        logger.warning(f"Chyba při kontrole PyTorch GPU: {e}")
        return False

def check_xgboost_gpu():
    """Zkontroluje GPU podporu v XGBoost."""
    logger.info("\n=== Kontrola GPU podpory v XGBoost ===")
    try:
        import xgboost as xgb
        logger.info(f"XGBoost verze: {xgb.__version__}")

        # Kontrola, zda XGBoost byl zkompilován s GPU podporou
        build_info = getattr(xgb, 'build_info', lambda: {})()
        if isinstance(build_info, dict):
            logger.info(f"XGBoost build info: {build_info}")
        else:
            logger.info("XGBoost build info není dostupné")

        # Test GPU podpory
        try:
            import numpy as np
            X = np.random.rand(10, 5)
            y = np.random.rand(10)

            params = {
                'tree_method': 'hist',  # Efektivní algoritmus pro stromy
                'device': 'cuda'        # Použití GPU (nová syntaxe od verze 2.0.0)
            }

            logger.info("Pokus o vytvoření modelu s GPU parametry...")
            model = xgb.XGBRegressor(**params)

            logger.info("Pokus o fit modelu na GPU...")
            model.fit(X, y, verbose=True)

            logger.info("XGBoost úspěšně použil GPU")
            return True
        except Exception as e:
            logger.warning(f"XGBoost nemůže použít GPU: {e}")
            return False
    except ImportError:
        logger.warning("XGBoost není nainstalován")
        return False
    except Exception as e:
        logger.warning(f"Chyba při kontrole XGBoost GPU: {e}")
        return False

def check_installed_packages():
    """Zkontroluje nainstalované balíčky související s GPU."""
    logger.info("\n=== Kontrola nainstalovaných balíčků ===")
    packages = [
        "xgboost",
        "torch",
        "numpy",
        "cupy",
        "pandas",
        "scikit-learn"
    ]

    for package in packages:
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "show", package],
                                   stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            if result.returncode == 0:
                version_line = [line for line in result.stdout.split('\n') if line.startswith('Version:')]
                version = version_line[0].split('Version:')[1].strip() if version_line else "neznámá"
                logger.info(f"  {package}: nainstalován (verze {version})")
            else:
                logger.info(f"  {package}: není nainstalován")
        except Exception as e:
            logger.warning(f"Chyba při kontrole balíčku {package}: {e}")

def check_gpu_compute_capability():
    """Zkontroluje compute capability GPU."""
    logger.info("\n=== Kontrola compute capability GPU ===")
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                # Získání compute capability
                major, minor = torch.cuda.get_device_capability(i)
                logger.info(f"GPU {i} compute capability: {major}.{minor}")

                # Kontrola kompatibility s XGBoost
                if major < 3:
                    logger.warning(f"GPU {i} má compute capability {major}.{minor}, což je příliš nízké pro XGBoost (vyžaduje alespoň 3.0)")
                else:
                    logger.info(f"GPU {i} má dostatečnou compute capability pro XGBoost")
        else:
            logger.warning("CUDA není dostupná pro PyTorch, nelze zjistit compute capability")
    except ImportError:
        logger.warning("PyTorch není nainstalován, nelze zjistit compute capability")
    except Exception as e:
        logger.warning(f"Chyba při zjišťování compute capability: {e}")

def main():
    """Hlavní funkce pro diagnostiku GPU."""
    logger.info("Spouštím diagnostiku GPU...")

    # Základní informace o systému
    check_system_info()

    # Kontrola GPU na úrovni systému
    nvidia_smi_available = check_nvidia_smi()
    cuda_nvcc_available = check_cuda_nvcc()
    check_environment_variables()

    # Kontrola nainstalovaných balíčků
    check_installed_packages()

    # Kontrola GPU v PyTorch
    pytorch_gpu = check_pytorch_gpu()

    # Kontrola compute capability
    if pytorch_gpu:
        check_gpu_compute_capability()

    # Kontrola GPU v XGBoost
    xgboost_gpu = check_xgboost_gpu()

    # Shrnutí
    logger.info("\n=== Shrnutí diagnostiky ===")
    logger.info(f"nvidia-smi: {'Dostupný' if nvidia_smi_available else 'Nedostupný'}")
    logger.info(f"CUDA (nvcc): {'Dostupný' if cuda_nvcc_available else 'Nedostupný'}")
    logger.info(f"PyTorch GPU: {'Dostupná' if pytorch_gpu else 'Nedostupná'}")
    logger.info(f"XGBoost GPU: {'Dostupná' if xgboost_gpu else 'Nedostupná'}")

    # Doporučení
    logger.info("\n=== Doporučení ===")
    if not nvidia_smi_available and not cuda_nvcc_available:
        logger.info("1. Nainstalujte ovladače NVIDIA a CUDA Toolkit")
        logger.info("   - Ovladače NVIDIA: https://www.nvidia.com/Download/index.aspx")
        logger.info("   - CUDA Toolkit: https://developer.nvidia.com/cuda-downloads")
    elif not pytorch_gpu or not xgboost_gpu:
        logger.info("1. Nainstalujte nebo aktualizujte PyTorch a XGBoost s GPU podporou:")
        logger.info("   - pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118")
        logger.info("   - pip install xgboost --upgrade")

        if not pytorch_gpu and nvidia_smi_available:
            logger.info("2. Zkontrolujte, zda je verze CUDA kompatibilní s PyTorch")
            logger.info("   - Viz https://pytorch.org/get-started/locally/ pro kompatibilní verze")

        if not xgboost_gpu and nvidia_smi_available:
            logger.info("2. Zkontrolujte, zda je verze CUDA kompatibilní s XGBoost")
            logger.info("   - XGBoost vyžaduje CUDA 10.0 nebo novější")
            logger.info("   - GPU musí mít compute capability alespoň 3.0")
    else:
        logger.info("Vše vypadá v pořádku, GPU by měla být dostupná pro XGBoost")

    logger.info("\nPokud GPU není dostupná, model bude automaticky používat CPU.")
    logger.info("Pro maximální výkon na CPU bude využito všech dostupných jader.")

if __name__ == "__main__":
    main()
