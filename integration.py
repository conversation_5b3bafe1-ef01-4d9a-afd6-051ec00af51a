"""
Integrační skript pro spojení všech nových funkcí.
Tento skript slouží jako vstupní bod pro spuštění predikčního systému
s využitím všech implementovaných vylepšení.
"""
import os
import logging
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import argparse

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Hlavní funkce pro spuštění predikčního systému."""
    # Zpracování argumentů příkazové řádky
    parser = argparse.ArgumentParser(description='Predikční systém s vylepšeními')
    parser.add_argument('--mode', choices=['predict', 'train', 'backtest', 'dashboard', 'update'],
                        default='predict', help='<PERSON><PERSON><PERSON> spu<PERSON>těn<PERSON>')
    parser.add_argument('--days', type=int, default=7,
                        help='Počet dní historie pro backtest nebo dashboard')
    parser.add_argument('--model', type=str, default='alch_price_model.pkl',
                        help='Cesta k souboru modelu')
    parser.add_argument('--ensemble', action='store_true',
                        help='Použít ensemble model místo standardního modelu')
    parser.add_argument('--use_sentiment', action='store_true',
                        help='Zahrnout sentiment do predikce')
    parser.add_argument('--update_sentiment', action='store_true',
                        help='Aktualizovat data o sentimentu')
    
    args = parser.parse_args()
    
    # Spuštění podle zvoleného režimu
    if args.mode == 'predict':
        run_prediction(args)
    elif args.mode == 'train':
        run_training(args)
    elif args.mode == 'backtest':
        run_backtest(args)
    elif args.mode == 'dashboard':
        run_dashboard(args)
    elif args.mode == 'update':
        run_update(args)
    else:
        logger.error(f"Neznámý režim: {args.mode}")

def run_prediction(args):
    """Spustí predikci s využitím všech vylepšení."""
    logger.info("Spouštím predikci...")
    
    try:
        # Import potřebných modulů
        from combined_script import connect_to_db, predict
        from market_conditions import detect_market_condition, get_best_model_for_current_conditions
        from external_sentiment import update_sentiment_data
        
        # Aktualizace dat o sentimentu, pokud je požadováno
        if args.update_sentiment or args.use_sentiment:
            logger.info("Aktualizuji data o sentimentu...")
            update_sentiment_data("ALCHUSDT")
        
        # Spuštění predikce
        predict()
        
        logger.info("Predikce úspěšně dokončena")
    except Exception as e:
        logger.error(f"Chyba při predikci: {e}")

def run_training(args):
    """Spustí trénování modelu s využitím všech vylepšení."""
    logger.info("Spouštím trénování modelu...")
    
    try:
        # Import potřebných modulů
        from combined_script import connect_to_db, train_and_save_model
        from market_conditions import detect_market_condition
        from adaptive_learning import should_retrain_model
        from ensemble_models import EnsembleModel, create_ensemble_for_market_condition
        
        # Detekce tržních podmínek
        market_condition = detect_market_condition()
        logger.info(f"Aktuální tržní podmínky: {market_condition}")
        
        # Kontrola, zda je potřeba přetrénovat model
        if should_retrain_model():
            logger.info("Model potřebuje přetrénování")
            
            # Trénování standardního modelu
            train_and_save_model()
            
            # Trénování ensemble modelu, pokud je požadováno
            if args.ensemble:
                logger.info("Trénuji ensemble model...")
                ensemble = create_ensemble_for_market_condition(market_condition)
                
                # Import dat pro trénování
                from combined_script import connect_to_db
                import pyodbc
                
                conn = connect_to_db()
                if conn:
                    # Načtení dat
                    cursor = conn.cursor()
                    query = """
                    SELECT * FROM alch_indikatory 
                    ORDER BY timestamp ASC
                    """
                    df = pd.read_sql(query, conn)
                    conn.close()
                    
                    # Příprava dat pro trénování
                    features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
                    df = df.dropna(subset=features + ['close_price'])
                    
                    X = df[features]
                    y = df["close_price"].shift(-5)
                    
                    # Odstranění NaN hodnot po posunu
                    X = X[:-5]
                    y = y[:-5].dropna()
                    
                    # Zarovnání indexů
                    X = X.loc[y.index]
                    
                    # Trénování ensemble modelu
                    ensemble.fit(X, y)
                    
                    # Uložení modelu
                    ensemble.save(f"ensemble_model_{market_condition}.pkl")
                    logger.info(f"Ensemble model uložen jako 'ensemble_model_{market_condition}.pkl'")
        else:
            logger.info("Model nepotřebuje přetrénování")
        
        logger.info("Trénování úspěšně dokončeno")
    except Exception as e:
        logger.error(f"Chyba při trénování: {e}")

def run_backtest(args):
    """Spustí backtesting na historických datech."""
    logger.info(f"Spouštím backtesting na {args.days} dnech historie...")
    
    try:
        # Import potřebných modulů
        from backtest import connect_to_db, load_historical_data, load_historical_indicators, load_historical_sentiment, prepare_backtest_data
        from backtest_strategies import PredictionModelStrategy, EnsembleModelStrategy, SentimentStrategy, CombinedStrategy, run_backtest, compare_strategies, plot_equity_curves
        
        # Připojení k databázi
        conn = connect_to_db()
        if conn:
            # Výpočet dat
            end_date = datetime.now()
            start_date = end_date - timedelta(days=args.days)
            
            # Načtení historických dat
            symbol = "ALCHUSDT"
            price_df = load_historical_data(conn, symbol, start_date, end_date)
            indicators_df = load_historical_indicators(conn, start_date, end_date)
            sentiment_df = load_historical_sentiment(conn, symbol, start_date, end_date)
            
            # Příprava dat pro backtesting
            data = prepare_backtest_data(price_df, indicators_df, sentiment_df)
            
            if not data.empty:
                # Vytvoření strategií
                strategies = []
                
                # Standardní model
                model_strategy = PredictionModelStrategy(args.model, threshold=0.01)
                strategies.append(model_strategy)
                
                # Ensemble model, pokud je požadován
                if args.ensemble and os.path.exists(f"ensemble_model_unknown.pkl"):
                    ensemble_strategy = EnsembleModelStrategy(f"ensemble_model_unknown.pkl", threshold=0.01)
                    strategies.append(ensemble_strategy)
                
                # Sentiment strategie, pokud je požadována
                if args.use_sentiment:
                    sentiment_strategy = SentimentStrategy(threshold=0.3)
                    strategies.append(sentiment_strategy)
                    
                    # Kombinovaná strategie
                    combined_strategy = CombinedStrategy(args.model, model_threshold=0.01, sentiment_threshold=0.3)
                    strategies.append(combined_strategy)
                
                # Porovnání strategií
                comparison = compare_strategies(strategies, data)
                
                print("\nPorovnání strategií:")
                print(comparison)
                
                # Provedení backtestů a uložení výsledků
                results_dict = {}
                for strategy in strategies:
                    results = run_backtest(strategy, data)
                    results_dict[strategy.name] = results
                
                # Vykreslení křivek equity
                plot_equity_curves(results_dict)
            else:
                logger.warning("Nedostatek dat pro backtesting")
            
            conn.close()
        else:
            logger.error("Nelze se připojit k databázi")
    except Exception as e:
        logger.error(f"Chyba při backtestingu: {e}")

def run_dashboard(args):
    """Spustí dashboard pro vizualizaci výsledků."""
    logger.info(f"Spouštím dashboard pro {args.days} dní historie...")
    
    try:
        # Import potřebných modulů
        from dashboard import run_dashboard
        
        # Spuštění dashboardu
        run_dashboard(days=args.days)
    except Exception as e:
        logger.error(f"Chyba při spouštění dashboardu: {e}")

def run_update(args):
    """Aktualizuje predikce a metriky výkonu."""
    logger.info("Aktualizuji predikce a metriky výkonu...")
    
    try:
        # Import potřebných modulů
        from update_predictions import main as update_main
        
        # Spuštění aktualizace
        update_main()
        
        # Aktualizace dat o sentimentu, pokud je požadováno
        if args.update_sentiment:
            from external_sentiment import update_sentiment_data
            update_sentiment_data("ALCHUSDT")
    except Exception as e:
        logger.error(f"Chyba při aktualizaci: {e}")

if __name__ == "__main__":
    main()
