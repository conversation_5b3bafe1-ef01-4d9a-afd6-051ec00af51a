"""
Skript pro aktualizaci technických indikátorů z Bybit API pomocí knihovny pybit.
"""
import os
import logging
import pandas as pd
import numpy as np
import pyodbc
import time
from datetime import datetime, timedelta
import ta  # Knihovna pro výpočet technických indikátorů
import pytz
from pybit.unified_trading import HTTP

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

# API a databazove nastaveni
api_key = "WiC6jz757MgJvviWkC"
secret_key = "fqbslWfRQmVWkU69XbiVCMp7q79no0Zr4JFj"

# Nastavení API
SYMBOL = "SOLUSDT"  # Symbol kryptoměny
INTERVAL = "1"  # Interval (1 minuta)
CATEGORY = "linear"  # Kategorie (spot, linear, inverse)

# Nastavení časových zón
UTC = pytz.UTC
LOCAL_TZ = pytz.timezone('Europe/Prague')  # Česká časová zóna (UTC+1/UTC+2)

def convert_utc_to_local(utc_dt):
    """Konvertuje UTC čas na lokální čas."""
    if utc_dt.tzinfo is None:
        utc_dt = UTC.localize(utc_dt)
    return utc_dt.astimezone(LOCAL_TZ)

def get_current_time_utc():
    """Vrátí aktuální čas v UTC."""
    return datetime.now(UTC)

def get_current_time_local():
    """Vrátí aktuální čas v lokální časové zóně."""
    return datetime.now(LOCAL_TZ)

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def get_bybit_session():
    """Vytvoří a vrátí session pro Bybit API."""
    try:
        # Vytvoření session s větším recv_window
        session = HTTP(
            testnet=False,
            api_key=api_key,
            api_secret=secret_key,
            recv_window=20000
        )
        return session
    except Exception as e:
        logger.error(f"Chyba při vytváření Bybit session: {e}")
        return None

def get_bybit_kline_data(session, symbol=SYMBOL, interval=INTERVAL, limit=1000, category=CATEGORY):
    """
    Získá data z Bybit API - kline (svíčkové grafy) pomocí pybit knihovny.

    Args:
        session: Bybit API session
        symbol: Symbol kryptoměny
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet záznamů (max 1000)
        category: Kategorie (spot, linear, inverse)

    Returns:
        DataFrame s daty
    """
    try:
        # Získání kline dat
        response = session.get_kline(
            category=category,
            symbol=symbol,
            interval=interval,
            limit=limit
        )

        # Kontrola odpovědi
        if response['retCode'] != 0:
            logger.error(f"Chyba v odpovědi Bybit API: {response}")
            return None

        # Extrakce dat
        kline_data = response['result']['list']

        # Kontrola, zda jsou data v odpovědi
        if not kline_data:
            logger.error("Žádná data v odpovědi Bybit API")
            return None

        # Vytvoření DataFrame
        df = pd.DataFrame(kline_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'])

        # Výpis prvního záznamu pro diagnostiku
        logger.info(f"První záznam z Bybit API: {kline_data[0]}")

        # Konverze typů
        # Konverze timestamp (string) na datetime v UTC
        df['timestamp_utc'] = pd.to_datetime(df['timestamp'].astype(float), unit='ms')

        # Konverze z UTC na lokální čas
        df['timestamp_local'] = df['timestamp_utc'].apply(convert_utc_to_local)

        # Odstranění časové zóny pro uložení do databáze
        df['timestamp'] = df['timestamp_local'].apply(lambda x: x.replace(tzinfo=None))

        # Pro výpis
        logger.info(f"Konverze časových značek: první záznam UTC: {df['timestamp_utc'].iloc[0]}, lokální: {df['timestamp_local'].iloc[0]}")

        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)

        # Přejmenování sloupců
        df = df.rename(columns={'close': 'close_price'})

        # Seřazení podle času (od nejnovějšího)
        df = df.sort_values('timestamp', ascending=False)

        # Výpis nejnovějšího záznamu
        if not df.empty:
            latest_row = df.iloc[0]
            logger.info(f"Nejnovější záznam: čas={latest_row['timestamp']}, cena={latest_row['close_price']}")

        logger.info(f"Získáno {len(df)} záznamů z Bybit API")
        return df
    except Exception as e:
        logger.error(f"Chyba při získávání dat z Bybit API: {e}")
        return None

def get_bybit_ticker_data(session, symbol=SYMBOL, category=CATEGORY):
    """
    Získá aktuální ticker data z Bybit API pomocí pybit knihovny.

    Args:
        session: Bybit API session
        symbol: Symbol kryptoměny
        category: Kategorie (spot, linear, inverse)

    Returns:
        DataFrame s aktuálními daty
    """
    try:
        # Získání ticker dat
        response = session.get_tickers(
            category=category,
            symbol=symbol
        )

        # Kontrola odpovědi
        if response['retCode'] != 0:
            logger.error(f"Chyba v odpovědi Bybit API: {response}")
            return None

        # Extrakce dat
        ticker_data = response['result']['list'][0]

        # Výpis ticker dat pro diagnostiku
        logger.info(f"Ticker data: {ticker_data}")

        # Vytvoření DataFrame s jedním řádkem
        current_time_utc = get_current_time_utc()
        current_time_local = convert_utc_to_local(current_time_utc)

        df = pd.DataFrame([{
            'timestamp': current_time_local.replace(tzinfo=None),
            'timestamp_utc': current_time_utc,
            'symbol': ticker_data['symbol'],
            'last_price': float(ticker_data['lastPrice']),
            'high_price_24h': float(ticker_data['highPrice24h']),
            'low_price_24h': float(ticker_data['lowPrice24h']),
            'volume_24h': float(ticker_data['volume24h']),
            'turnover_24h': float(ticker_data['turnover24h']),
            'price_change_percent_24h': float(ticker_data['price24hPcnt']) * 100
        }])

        logger.info(f"Získána aktuální ticker data z Bybit API: {ticker_data['symbol']} = {ticker_data['lastPrice']}")
        return df
    except Exception as e:
        logger.error(f"Chyba při získávání ticker dat z Bybit API: {e}")
        return None

def calculate_indicators(df):
    """
    Vypočítá technické indikátory.

    Args:
        df: DataFrame s daty

    Returns:
        DataFrame s indikátory
    """
    try:
        # Kopie DataFrame
        df_indicators = df.copy()

        # Seřazení dat od nejstaršího k nejnovějšímu pro správný výpočet indikátorů
        df_indicators = df_indicators.sort_values('timestamp', ascending=True)

        # Výpočet RSI
        rsi = ta.momentum.RSIIndicator(df_indicators['close_price'], window=14)
        df_indicators['RSI'] = rsi.rsi()

        # Výpočet EMA
        df_indicators['EMA9'] = ta.trend.ema_indicator(df_indicators['close_price'], window=9)
        df_indicators['EMA20'] = ta.trend.ema_indicator(df_indicators['close_price'], window=20)

        # Výpočet Bollinger Bands
        bollinger = ta.volatility.BollingerBands(df_indicators['close_price'], window=20, window_dev=2)
        df_indicators['boll_high'] = bollinger.bollinger_hband()
        df_indicators['boll_low'] = bollinger.bollinger_lband()

        # Výpočet ATR
        if 'high' in df_indicators.columns and 'low' in df_indicators.columns:
            atr = ta.volatility.AverageTrueRange(df_indicators['high'], df_indicators['low'], df_indicators['close_price'], window=14)
            df_indicators['ATR'] = atr.average_true_range()
        else:
            # Pokud nemáme high a low, použijeme close_price
            df_indicators['ATR'] = df_indicators['close_price'] * 0.01  # Jednoduchá aproximace

        # Vyplnění chybějících hodnot pro nejnovější záznamy
        # Použijeme poslední dostupnou hodnotu pro každý indikátor
        df_indicators['RSI'] = df_indicators['RSI'].ffill()
        df_indicators['EMA9'] = df_indicators['EMA9'].ffill()
        df_indicators['EMA20'] = df_indicators['EMA20'].ffill()
        df_indicators['boll_high'] = df_indicators['boll_high'].ffill()
        df_indicators['boll_low'] = df_indicators['boll_low'].ffill()
        df_indicators['ATR'] = df_indicators['ATR'].ffill()

        # Seřazení zpět od nejnovějšího k nejstaršímu
        df_indicators = df_indicators.sort_values('timestamp', ascending=False)

        # Odstranění řádků s chybějícími hodnotami (pro jistotu)
        df_indicators = df_indicators.dropna()

        logger.info(f"Vypočítáno {len(df_indicators)} indikátorů")
        return df_indicators
    except Exception as e:
        logger.error(f"Chyba při výpočtu indikátorů: {e}")
        return None

def save_indicators(conn, df_indicators, force_update=False):
    """
    Uloží indikátory do databáze.

    Args:
        conn: Připojení k databázi
        df_indicators: DataFrame s indikátory
        force_update: Pokud True, aktualizuje existující záznamy

    Returns:
        True pokud bylo uložení úspěšné, jinak False
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Počet úspěšně uložených záznamů
        success_count = 0
        update_count = 0

        # Výpis nejnovějších časových značek pro diagnostiku
        timestamps = df_indicators['timestamp'].tolist()
        timestamps.sort(reverse=True)  # Seřazení od nejnovějšího
        logger.info(f"Nejnovější časové značky v df_indicators: {timestamps[:5]}")

        # Uložení indikátorů
        for _, row in df_indicators.iterrows():
            # Kontrola, zda záznam již existuje
            cursor.execute("""
            SELECT COUNT(*) FROM dbo.alch_indikatory
            WHERE timestamp = ?
            """, (row['timestamp'],))

            count = cursor.fetchone()[0]
            if count > 0:
                if force_update:
                    # Aktualizace existujícího záznamu
                    cursor.execute("""
                    UPDATE dbo.alch_indikatory
                    SET close_price = ?, RSI = ?, EMA9 = ?, EMA20 = ?, boll_high = ?, boll_low = ?, ATR = ?
                    WHERE timestamp = ?
                    """, (
                        row['close_price'], row['RSI'], row['EMA9'], row['EMA20'],
                        row['boll_high'], row['boll_low'], row['ATR'], row['timestamp']
                    ))
                    update_count += 1
                    logger.info(f"Aktualizuji indikátor: čas={row['timestamp']}, cena={row['close_price']}")
                else:
                    # Záznam již existuje, přeskočíme ho
                    continue
            else:
                # Výpis pro diagnostiku
                logger.info(f"Ukládám indikátor: čas={row['timestamp']}, cena={row['close_price']}")

                # Vložení indikátoru
                cursor.execute("""
                INSERT INTO dbo.alch_indikatory
                (timestamp, close_price, RSI, EMA9, EMA20, boll_high, boll_low, ATR)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row['timestamp'], row['close_price'], row['RSI'], row['EMA9'],
                    row['EMA20'], row['boll_high'], row['boll_low'], row['ATR']
                ))

                success_count += 1

        conn.commit()
        logger.info(f"Uloženo {success_count} nových indikátorů do databáze")
        if update_count > 0:
            logger.info(f"Aktualizováno {update_count} existujících indikátorů v databázi")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při ukládání indikátorů: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def update_indicators_from_bybit(force_update=False):
    """
    Aktualizuje indikátory z Bybit API pomocí pybit knihovny.

    Args:
        force_update: Pokud True, aktualizuje existující záznamy
    """
    try:
        # Připojení k databázi
        conn = connect_to_db()
        if not conn:
            logger.error("Nelze se připojit k databázi")
            return

        # Vytvoření Bybit session
        session = get_bybit_session()
        if not session:
            logger.error("Nelze vytvořit Bybit session")
            conn.close()
            return

        # Získání aktuálních ticker dat
        ticker_df = get_bybit_ticker_data(session)
        if ticker_df is not None:
            logger.info(f"Aktuální cena podle ticker dat: {ticker_df.iloc[0]['last_price']}")

        # Získání kline dat z API
        df = get_bybit_kline_data(session)
        if df is None:
            logger.error("Nelze získat kline data z Bybit API")
            conn.close()
            return

        # Porovnání cen
        if ticker_df is not None and not df.empty:
            kline_price = df.iloc[0]['close_price']
            ticker_price = ticker_df.iloc[0]['last_price']
            price_diff_pct = abs((kline_price - ticker_price) / ticker_price) * 100
            logger.info(f"Porovnání cen: kline={kline_price}, ticker={ticker_price}, rozdíl={price_diff_pct:.2f}%")

            # Pokud je rozdíl větší než 1%, použijeme ticker cenu
            if price_diff_pct > 1:
                logger.warning(f"Velký rozdíl mezi kline a ticker cenou ({price_diff_pct:.2f}%), používám ticker cenu")
                df.loc[0, 'close_price'] = ticker_price

        # Výpočet indikátorů
        df_indicators = calculate_indicators(df)
        if df_indicators is None:
            logger.error("Nelze vypočítat indikátory")
            conn.close()
            return

        # Uložení indikátorů
        save_indicators(conn, df_indicators, force_update)

        conn.close()
    except Exception as e:
        logger.error(f"Neočekávaná chyba při aktualizaci indikátorů: {e}")

def main():
    """Hlavní funkce."""
    print("=== Aktualizace technických indikátorů z Bybit API pomocí pybit knihovny ===")

    # Zpracování argumentů příkazové řádky
    import argparse
    parser = argparse.ArgumentParser(description='Aktualizace technických indikátorů z Bybit API pomocí pybit knihovny')
    parser.add_argument('--interval', type=int, default=60,
                        help='Interval aktualizace v sekundách')
    parser.add_argument('--count', type=int, default=0,
                        help='Počet aktualizací (0 = nekonečno)')
    parser.add_argument('--force', action='store_true',
                        help='Aktualizovat existující záznamy')
    parser.add_argument('--fill-missing', action='store_true',
                        help='Doplnit chybějící záznamy')

    args = parser.parse_args()

    # Počítadlo aktualizací
    update_count = 0

    try:
        # Pokud je požadováno doplnění chybějících záznamů
        if args.fill_missing:
            print("Doplňuji chybějící záznamy...")
            # Získání všech dostupných dat
            update_indicators_from_bybit(force_update=args.force)

        while True:
            # Aktualizace indikátorů
            update_indicators_from_bybit(force_update=args.force)

            # Inkrementace počítadla
            update_count += 1

            # Kontrola počtu aktualizací
            if args.count > 0 and update_count >= args.count:
                logger.info(f"Dosažen požadovaný počet aktualizací ({args.count})")
                break

            # Čekání na další aktualizaci
            logger.info(f"Čekám {args.interval} sekund na další aktualizaci...")
            time.sleep(args.interval)
    except KeyboardInterrupt:
        logger.info("Aktualizace ukončena uživatelem")
    except Exception as e:
        logger.error(f"Neočekávaná chyba: {e}")

if __name__ == "__main__":
    main()
