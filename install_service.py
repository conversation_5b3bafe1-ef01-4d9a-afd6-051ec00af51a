"""
Skript pro instalaci predikčního systému jako služ<PERSON>.
Podporuje Windows i Linux (systemd).
"""
import os
import sys
import platform
import subprocess
import argparse
import shutil

def is_admin():
    """
    <PERSON><PERSON><PERSON><PERSON><PERSON>, zda je skript spuštěn s administrátorskými právy.
    
    Returns:
        True pokud je skript spuštěn s administrátorskými právy, jinak <PERSON>e
    """
    try:
        if platform.system() == 'Windows':
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        else:
            return os.geteuid() == 0
    except:
        return False

def install_windows_service(args):
    """
    Instaluje službu na Windows.
    
    Args:
        args: Argumenty příkazové řádky
    """
    print("Instaluji službu na Windows...")
    
    # Kontrola, zda je skript spuštěn s administrátorskými právy
    if not is_admin():
        print("CHYBA: Tento skript musí být spuštěn s administrátorskými právy.")
        print("Spusťte příkazový řádek jako správce a zkuste to znovu.")
        return False
    
    # Kontrola, zda existuje soubor run_as_service.py
    if not os.path.exists("run_as_service.py"):
        print("CHYBA: Soubor run_as_service.py nebyl nalezen.")
        return False
    
    # Instalace služby
    try:
        cmd = [sys.executable, "run_as_service.py", "install"]
        subprocess.run(cmd, check=True)
        
        print("Služba byla úspěšně nainstalována.")
        print("Můžete ji spustit pomocí příkazu:")
        print("  net start PredictionService")
        
        # Automatické spuštění služby, pokud je požadováno
        if args.start:
            print("Spouštím službu...")
            subprocess.run(["net", "start", "PredictionService"], check=True)
            print("Služba byla úspěšně spuštěna.")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"CHYBA: Instalace služby selhala: {e}")
        return False
    except Exception as e:
        print(f"CHYBA: Neočekávaná chyba při instalaci služby: {e}")
        return False

def install_linux_service(args):
    """
    Instaluje službu na Linux (systemd).
    
    Args:
        args: Argumenty příkazové řádky
    """
    print("Instaluji službu na Linux (systemd)...")
    
    # Kontrola, zda je skript spuštěn s administrátorskými právy
    if not is_admin():
        print("CHYBA: Tento skript musí být spuštěn s právy root.")
        print("Zkuste spustit skript pomocí sudo.")
        return False
    
    # Kontrola, zda existuje soubor prediction-service.service
    if not os.path.exists("prediction-service.service"):
        print("CHYBA: Soubor prediction-service.service nebyl nalezen.")
        return False
    
    try:
        # Získání aktuálního uživatele
        if args.user:
            username = args.user
        else:
            # Pokud je skript spuštěn pomocí sudo, získáme původního uživatele
            if 'SUDO_USER' in os.environ:
                username = os.environ['SUDO_USER']
            else:
                username = os.environ['USER']
        
        # Získání aktuálního adresáře
        current_dir = os.path.abspath(os.path.dirname(__file__))
        
        # Úprava service souboru
        with open("prediction-service.service", "r") as f:
            service_content = f.read()
        
        service_content = service_content.replace("YOUR_USERNAME", username)
        service_content = service_content.replace("/path/to/prediction/system", current_dir)
        
        # Uložení upraveného service souboru
        with open("prediction-service.service", "w") as f:
            f.write(service_content)
        
        # Kopírování service souboru do systemd adresáře
        shutil.copy("prediction-service.service", "/etc/systemd/system/")
        
        # Nastavení oprávnění
        os.chmod("/etc/systemd/system/prediction-service.service", 0o644)
        
        # Reload systemd
        subprocess.run(["systemctl", "daemon-reload"], check=True)
        
        print("Služba byla úspěšně nainstalována.")
        print("Můžete ji spustit pomocí příkazu:")
        print("  sudo systemctl start prediction-service")
        print("Pro automatické spouštění při startu systému použijte:")
        print("  sudo systemctl enable prediction-service")
        
        # Automatické spuštění služby, pokud je požadováno
        if args.start:
            print("Spouštím službu...")
            subprocess.run(["systemctl", "start", "prediction-service"], check=True)
            print("Služba byla úspěšně spuštěna.")
        
        # Automatické povolení služby při startu, pokud je požadováno
        if args.enable:
            print("Povoluji automatické spouštění služby při startu systému...")
            subprocess.run(["systemctl", "enable", "prediction-service"], check=True)
            print("Automatické spouštění služby bylo povoleno.")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"CHYBA: Instalace služby selhala: {e}")
        return False
    except Exception as e:
        print(f"CHYBA: Neočekávaná chyba při instalaci služby: {e}")
        return False

def main():
    """Hlavní funkce."""
    # Zpracování argumentů příkazové řádky
    parser = argparse.ArgumentParser(description='Instalace predikčního systému jako služby')
    parser.add_argument('--start', action='store_true',
                        help='Automaticky spustit službu po instalaci')
    parser.add_argument('--enable', action='store_true',
                        help='Povolit automatické spouštění služby při startu systému (pouze Linux)')
    parser.add_argument('--user', type=str,
                        help='Uživatel, pod kterým bude služba spuštěna (pouze Linux)')
    
    args = parser.parse_args()
    
    # Detekce operačního systému
    system = platform.system()
    
    if system == 'Windows':
        install_windows_service(args)
    elif system == 'Linux':
        install_linux_service(args)
    else:
        print(f"CHYBA: Nepodporovaný operační systém: {system}")
        print("Tento skript podporuje pouze Windows a Linux.")
        return False
    
    return True

if __name__ == "__main__":
    main()
