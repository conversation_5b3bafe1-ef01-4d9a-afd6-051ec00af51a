import numpy as np
from sklearn.neighbors import K<PERSON><PERSON>ghborsRegressor

def takens_embedding(data, emb_dim, tau):
    N = len(data) - (emb_dim - 1) * tau
    if N <= 0:
        raise ValueError("Nedostatek dat na embedding.")
    X = np.array([data[i:i + emb_dim * tau:tau] for i in range(N)])
    y = data[(emb_dim - 1) * tau:]
    return X[:-1], y[1:]

def takens_embedding_last(seq, emb_dim, tau):
    return np.array([seq[-(emb_dim-j)*tau] for j in range(emb_dim)])

# emb_dim (embedding dimension):
# Vyssi hodnoty lepe vystihuji komplexni vzory, ale potrebuji vic dat. 3–8 je bezne.
# tau (delay):
# Vetsinou 1, pokud nechas default, ale muzes zkusit 2–3 podle vzorkovani trhu.
# n_neighbors (pocet sousedu):
# Maly pocet (3–5) = hodne “lo<PERSON><PERSON>” predikce, vetsi pocet (10+) = vic “vyhlazena”.

def predict_future_knn(df, future_steps=10, emb_dim=5, tau=1, n_neighbors=10):
    close_data = np.array(df['close'].values)
    X, y = takens_embedding(close_data, emb_dim, tau)
    knn = KNeighborsRegressor(n_neighbors=n_neighbors)
    knn.fit(X, y)
    preds = []
    curr_seq = close_data.copy()
    for _ in range(future_steps):
        emb = takens_embedding_last(curr_seq, emb_dim, tau)
        pred = knn.predict(emb.reshape(1, -1))[0]
        preds.append(pred)
        curr_seq = np.append(curr_seq, pred)
    return preds


def predict_future_chaos(df, future_steps=10):
    posledni_close = df['close'].iloc[-1]
    std = df['close'].diff().std()
    preds = []
    val = posledni_close
    for _ in range(future_steps):
        val += np.random.normal(0, std)
        preds.append(val)
        print("Chaos predikce:", preds)

    return preds
