"""
Skript pro vytvoření databáze byb a všech potřebných tabulek.
"""
import os
import logging
import pyodbc

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_database_and_tables():
    """Vytvoří databázi a příslu<PERSON> tabulky, pokud neexistují."""
    try:
        # Připojení k master databázi pro vytvoření nové databáze
        master_conn_str = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=master;Trusted_Connection=yes;"
        master_conn = pyodbc.connect(master_conn_str)
        master_cursor = master_conn.cursor()
        
        # Kontrola, zda databáze existuje
        master_cursor.execute("SELECT name FROM sys.databases WHERE name = 'byb'")
        db_exists = master_cursor.fetchone()
        
        if not db_exists:
            logger.info("Databáze 'byb' neexistuje, vytvářím ji...")
            master_cursor.execute("CREATE DATABASE byb")
            master_conn.commit()
            logger.info("Databáze 'byb' byla úspěšně vytvořena")
        else:
            logger.info("Databáze 'byb' již existuje")
        
        master_cursor.close()
        master_conn.close()
        
        # Připojení k nově vytvořené databázi
        conn_str = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Vytvoření tabulky alch_indikatory
        logger.info("Vytvářím tabulku alch_indikatory...")
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='alch_indikatory' AND xtype='U')
        CREATE TABLE dbo.alch_indikatory (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            close_price DECIMAL(18,8) NOT NULL,
            RSI DECIMAL(18,8),
            EMA9 DECIMAL(18,8),
            EMA20 DECIMAL(18,8),
            boll_high DECIMAL(18,8),
            boll_low DECIMAL(18,8),
            ATR DECIMAL(18,8),
            UNIQUE(timestamp)
        )
        """)
        
        # Vytvoření tabulky alch_predictions
        logger.info("Vytvářím tabulku alch_predictions...")
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='alch_predictions' AND xtype='U')
        CREATE TABLE dbo.alch_predictions (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            current_price DECIMAL(18,8) NOT NULL,
            predicted_price DECIMAL(18,8) NOT NULL,
            actual_price DECIMAL(18,8),
            market_condition NVARCHAR(50),
            created_at DATETIME2 DEFAULT GETDATE()
        )
        """)
        
        # Vytvoření tabulky alch_price_history
        logger.info("Vytvářím tabulku alch_price_history...")
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='alch_price_history' AND xtype='U')
        CREATE TABLE dbo.alch_price_history (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            open_price DECIMAL(18,8),
            high_price DECIMAL(18,8),
            low_price DECIMAL(18,8),
            close_price DECIMAL(18,8) NOT NULL,
            volume DECIMAL(18,8),
            UNIQUE(timestamp)
        )
        """)
        
        # Vytvoření tabulky MODEL_PERFORMANCE_LOG
        logger.info("Vytvářím tabulku MODEL_PERFORMANCE_LOG...")
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MODEL_PERFORMANCE_LOG' AND xtype='U')
        CREATE TABLE dbo.MODEL_PERFORMANCE_LOG (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 DEFAULT GETDATE(),
            model_name NVARCHAR(100) NOT NULL,
            market_condition NVARCHAR(50),
            accuracy DECIMAL(5,4),
            mae DECIMAL(18,8),
            rmse DECIMAL(18,8),
            prediction_count INT DEFAULT 0
        )
        """)
        
        # Vytvoření tabulky EXTERNAL_SENTIMENT
        logger.info("Vytvářím tabulku EXTERNAL_SENTIMENT...")
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EXTERNAL_SENTIMENT' AND xtype='U')
        CREATE TABLE dbo.EXTERNAL_SENTIMENT (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 DEFAULT GETDATE(),
            source NVARCHAR(255),
            sentiment_score DECIMAL(5,4),
            confidence DECIMAL(5,4),
            raw_data NVARCHAR(MAX)
        )
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("Všechny potřebné tabulky byly úspěšně zkontrolovány/vytvořeny")
        print("✅ Databáze a tabulky byly úspěšně vytvořeny!")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při vytváření databáze a tabulek: {e}")
        print(f"❌ Chyba při vytváření databáze: {e}")
        return False

def main():
    """Hlavní funkce."""
    print("=== Vytvoření databáze byb a tabulek ===")
    
    # Pokus o vytvoření databáze a tabulek
    success = create_database_and_tables()
    
    if success:
        print("\n🎉 Databáze a všechny tabulky byly úspěšně vytvořeny!")
        print("\nVytvořené tabulky:")
        print("- dbo.alch_indikatory (technické indikátory)")
        print("- dbo.alch_predictions (predikce cen)")
        print("- dbo.alch_price_history (historie cen)")
        print("- dbo.MODEL_PERFORMANCE_LOG (výkonnost modelů)")
        print("- dbo.EXTERNAL_SENTIMENT (externí sentiment)")
    else:
        print("\n❌ Nepodařilo se vytvořit databázi a tabulky.")
        print("Zkontrolujte, zda:")
        print("- SQL Server běží")
        print("- Máte oprávnění k vytvoření databáze")
        print("- Připojovací řetězec je správný")

if __name__ == "__main__":
    main()
