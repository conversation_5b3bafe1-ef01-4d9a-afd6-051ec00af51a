import logging
import time
import os
import pyodbc
import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
from pybit.unified_trading import HTTP
import ta  # Knihovna pro technické indikátory
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error
import xgboost as xgb

# Import vlastních modulů pro vylepšení predikce
from trend_indicators import add_trend_indicators
from adaptive_learning import adaptive_learning_step, should_retrain_model
from market_conditions import detect_market_condition
from sentiment_analysis import add_sentiment_features, update_sentiment_data, calculate_market_sentiment

# --- Nastavení logování ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Konfigurace API a databáze ---
api_key = "WiC6jz757MgJvviWkC"
api_secret = "fqbslWfRQmVWkU69XbiVCMp7q79no0Zr4JFj"
symbol = "ALCHUSDT"
session = HTTP(testnet=False, api_key=api_key, api_secret=api_secret, recv_window=20000)

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.0.100,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

def connect_to_db():
    return pyodbc.connect(conn_str)

def ensure_tables(cursor):
    cursor.execute(
        """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'kline_data')
        BEGIN
            CREATE TABLE kline_data (
                timestamp DATETIME NOT NULL,
                symbol NVARCHAR(20) NOT NULL,
                open_price FLOAT,
                high_price FLOAT,
                low_price FLOAT,
                close_price FLOAT,
                volume FLOAT,
                turnover FLOAT,
                PRIMARY KEY (timestamp, symbol)
            )
        END
        """
    )
    cursor.execute(
        """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'alch_price_history')
        BEGIN
            CREATE TABLE alch_price_history (
                timestamp DATETIME NOT NULL,
                open_price FLOAT,
                high_price FLOAT,
                low_price FLOAT,
                close_price FLOAT,
                volume FLOAT,
                PRIMARY KEY (timestamp)
            )
        END
        """
    )
    cursor.execute(
        """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'alch_indikatory')
        BEGIN
            CREATE TABLE alch_indikatory (
                timestamp DATETIME NOT NULL,
                close_price FLOAT,
                RSI FLOAT,
                EMA9 FLOAT,
                EMA20 FLOAT,
                boll_high FLOAT,
                boll_low FLOAT,
                ATR FLOAT,
                PRIMARY KEY (timestamp)
            )
        END
        """
    )
    cursor.commit()

def fetch_and_save_latest_kline(symbol):
    now = int(time.time() * 1000)
    one_minute_ago = now - 2 * 60 * 1000
    conn = connect_to_db()
    cursor = conn.cursor()

    try:
        response = session.get_kline(
            category="linear",
            symbol=symbol,
            interval="1",
            start=one_minute_ago,
            end=now,
            limit=1,
        )

        if response["retCode"] == 0 and response["result"]["list"]:
            k = response["result"]["list"][0]
            ts = datetime.fromtimestamp(int(k[0]) / 1000)
            logging.info(f"Fetched kline data for {symbol} at {ts} with Open: {k[1]}, High: {k[2]}, Low: {k[3]}, Close: {k[4]}, Volume: {k[5]}")
            cursor.execute(
                """
                IF NOT EXISTS (SELECT 1 FROM alch_price_history WHERE timestamp = ?)
                BEGIN
                    INSERT INTO alch_price_history (
                        timestamp, open_price, high_price, low_price,
                        close_price, volume
                    ) VALUES (?, ?, ?, ?, ?, ?)
                END
                """,
                (
                    ts,  # timestamp for the IF NOT EXISTS check
                    ts,  # timestamp
                    float(k[1]),  # open_price
                    float(k[2]),  # high_price
                    float(k[3]),  # low_price
                    float(k[4]),  # close_price
                    float(k[5]),  # volume
                ),
            )
            conn.commit()
            logging.info(f"[✓] {symbol} - {ts} ulozeno do alch_price_history")
        else:
            logging.warning(f"[-] {symbol} - zadna data pro kline_data")
    except Exception as e:
        logging.error(f"✗ Chyba pri zpracovani {symbol}: {e}")
    finally:
        cursor.close()
        conn.close()

def stahni_minutove_svicky():
    """
    Funkce pro stažení minutových svíček za posledních 7 dní pomocí Bybit API
    """
    try:
        conn = connect_to_db()
        cursor = conn.cursor()

        # Zajištění existence tabulky alch_price_history
        cursor.execute(
            """
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'alch_price_history')
            BEGIN
                CREATE TABLE alch_price_history (
                    timestamp DATETIME NOT NULL,
                    open_price FLOAT,
                    high_price FLOAT,
                    low_price FLOAT,
                    close_price FLOAT,
                    volume FLOAT,
                    PRIMARY KEY (timestamp)
                )
            END
            """
        )
        conn.commit()

        # Získání časového rozsahu - posledních 7 dní
        konec_dne = datetime.now()
        zacatek = konec_dne - timedelta(days=1)

        logging.info(f"Stahuji minutové svíčky za posledních 7 dní")
        logging.info(f"Časový rozsah: {zacatek} až {konec_dne}")

        # Formátování času pro API (Bybit používá milisekundy)
        zacatek_timestamp = int(zacatek.timestamp()) * 1000
        konec_timestamp = int(konec_dne.timestamp()) * 1000

        # Vymazání starých dat za daný časový rozsah
        delete_query = """
        DELETE FROM alch_price_history
        WHERE timestamp >= ? AND timestamp <= ?
        """
        cursor.execute(delete_query, (zacatek, konec_dne))
        conn.commit()
        logging.info("Vymazány staré svíčky za posledních 7 dní")

        # Stahujeme data po částech kvůli limitům API
        current_start = zacatek_timestamp
        celkem_vlozeno = 0

        while current_start < konec_timestamp:
            current_end = min(current_start + (1000 * 60 * 1000), konec_timestamp)  # Max 1000 minut

            # Použití Bybit API pomocí existující session
            response = session.get_kline(
                category="linear",
                symbol=symbol,  # Použití symbolu definovaného na začátku souboru
                interval="1",   # 1-minutové svíčky
                start=current_start,
                end=current_end,
                limit=1000,
            )

            if response["retCode"] == 0 and response["result"]["list"]:
                klines = response["result"]["list"]
                logging.info(f"Staženo {len(klines)} minutových svíček pro interval {datetime.fromtimestamp(current_start/1000)} - {datetime.fromtimestamp(current_end/1000)}")

                # Příprava nových dat
                count = 0
                for k in klines:
                    timestamp = datetime.fromtimestamp(int(k[0]) / 1000)

                    # Vložení do databáze
                    insert_query = """
                    INSERT INTO alch_price_history (timestamp, open_price, high_price, low_price, close_price, volume)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """
                    cursor.execute(
                        insert_query,
                        (
                            timestamp,
                            float(k[1]),  # open
                            float(k[2]),  # high
                            float(k[3]),  # low
                            float(k[4]),  # close
                            float(k[5]),  # volume
                        )
                    )
                    count += 1

                    # Commit po každých 100 záznamech
                    if count % 100 == 0:
                        conn.commit()
                        logging.info(f"Vloženo {count} záznamů...")

                conn.commit()
                celkem_vlozeno += count
                logging.info(f"Vloženo {count} svíček pro aktuální interval")
            else:
                logging.warning(f"Žádná data pro interval {datetime.fromtimestamp(current_start/1000)} - {datetime.fromtimestamp(current_end/1000)}")

            # Posun na další časový úsek
            current_start = current_end
            time.sleep(1)  # Pauza mezi požadavky

        logging.info(f"Celkem vloženo {celkem_vlozeno} nových minutových svíček za posledních 7 dní")
    except Exception as e:
        logging.error(f"✗ Chyba pri zpracovani {symbol}: {e}")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

def aktualizuj_indikatory():
    try:
        conn = connect_to_db()
        cursor = conn.cursor()

        poslední_query = """
        SELECT MAX(timestamp) as poslední_čas FROM alch_indikatory
        """
        cursor.execute(poslední_query)
        poslední_záznam = cursor.fetchone()
        poslední_čas = poslední_záznam[0] if poslední_záznam[0] else None

        if poslední_čas:
            logging.info(f"Poslední záznam indikátorů v databázi: {poslední_čas}")
            history_window = 30
            výpočetní_start = poslední_čas - timedelta(days=history_window)

            history_query = f"""
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM alch_price_history
            WHERE timestamp >= ?
            ORDER BY timestamp ASC
            """
            df_history = pd.read_sql(history_query, conn, params=[výpočetní_start])

            insert_start = poslední_čas
            logging.info(f"Načteno {len(df_history)} záznamů pro výpočet indikátorů (včetně historie) od {výpočetní_start}")
            logging.info(f"Vkládat budeme záznamy od {insert_start}")
        else:
            logging.info("Tabulka indikátorů je prázdná, načítám všechna data.")
            history_query = """
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM alch_price_history
            ORDER BY timestamp ASC
            """
            df_history = pd.read_sql(history_query, conn)
            insert_start = None
            logging.info(f"Načteno {len(df_history)} záznamů pro první výpočet indikátorů")

        if len(df_history) == 0:
            logging.info("Žádná nová data k dispozici.")
            return

        logging.info("Počítám technické indikátory...")
        # Základní indikátory
        df_history["RSI"] = ta.momentum.rsi(df_history["close_price"], window=14)
        df_history["EMA9"] = ta.trend.ema_indicator(df_history["close_price"], window=9)
        df_history["EMA20"] = ta.trend.ema_indicator(df_history["close_price"], window=20)

        bollinger = ta.volatility.BollingerBands(df_history["close_price"], window=20)
        df_history["boll_high"] = bollinger.bollinger_hband()
        df_history["boll_low"] = bollinger.bollinger_lband()

        df_history["ATR"] = ta.volatility.average_true_range(
            df_history["high_price"], df_history["low_price"], df_history["close_price"], window=14
        )

        # Přidání pokročilých trendových indikátorů
        logging.info("Přidávám pokročilé trendové indikátory...")
        try:
            df_history = add_trend_indicators(df_history)
            logging.info("Trendové indikátory úspěšně přidány")
        except Exception as e:
            logging.error(f"Chyba při přidávání trendových indikátorů: {e}")

        # Přidání příznaků sentimentu trhu
        logging.info("Přidávám příznaky sentimentu trhu...")
        try:
            df_history = add_sentiment_features(df_history)
            logging.info("Příznaky sentimentu úspěšně přidány")
        except Exception as e:
            logging.error(f"Chyba při přidávání příznaků sentimentu: {e}")

        if insert_start:
            df_insert = df_history[df_history["timestamp"] > insert_start].copy()
        else:
            df_insert = df_history.copy()

        df_insert = df_insert.dropna()
        logging.info(f"Prepared {len(df_insert)} new records for insertion into alch_indikatory.")
        logging.info(f"K vložení připraveno {len(df_insert)} nových záznamů.")

        if len(df_insert) > 0:
            počet_vložených = 0
            logging.info("Vkládám nové záznamy do databáze...")

            for _, row in df_insert.iterrows():
                try:
                    cursor.execute(
                        """
                        IF NOT EXISTS (SELECT 1 FROM alch_indikatory WHERE timestamp = ?)
                        BEGIN
                            INSERT INTO alch_indikatory (timestamp, close_price, RSI, EMA9, EMA20, boll_high, boll_low, ATR)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        END
                        """,
                        row["timestamp"],
                        row["timestamp"],
                        float(row["close_price"]),
                        float(row["RSI"]),
                        float(row["EMA9"]),
                        float(row["EMA20"]),
                        float(row["boll_high"]),
                        float(row["boll_low"]),
                        float(row["ATR"]),
                    )
                    počet_vložených += 1

                    if počet_vložených % 1000 == 0:
                        conn.commit()
                        logging.info(f"Vloženo {počet_vložených} záznamů...")

                except Exception as e:
                    logging.error(f"CHYBA při vkládání záznamu {row['timestamp']}: {e}")

            conn.commit()
            logging.info(f"Celkem vloženo {počet_vložených} nových záznamů do tabulky alch_indikatory")
        else:
            logging.info("Žádné nové záznamy k vložení.")

    except Exception as e:
        logging.error(f"CHYBA při zpracování: {e}")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        logging.info("Aktualizace indikátorů dokončena.")

def train_and_save_model():
    try:
        # Kontrola, zda je potřeba přetrénovat model
        if not should_retrain_model():
            logging.info("Model funguje dobře, přetrénování není potřeba")
            return

        # Detekce aktuálních tržních podmínek
        market_condition = detect_market_condition()
        logging.info(f"Aktuální tržní podmínky: {market_condition}")

        conn = connect_to_db()
        query = """
        SELECT * FROM alch_indikatory
        ORDER BY timestamp ASC
        """
        df = pd.read_sql(query, conn)
        conn.close()

        # Rozšíření seznamu příznaků o nové indikátory
        # Základní příznaky
        base_features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]

        # Přidání trendových indikátorů
        trend_features = []
        for col in df.columns:
            if any(prefix in col for prefix in ['MACD', 'ADX', 'trend_', 'PSAR', 'supertrend']):
                trend_features.append(col)

        # Přidání příznaků sentimentu
        sentiment_features = []
        for col in df.columns:
            if any(prefix in col for prefix in ['sentiment', 'momentum', 'buy_sell']):
                sentiment_features.append(col)

        # Kombinace všech příznaků
        all_features = base_features.copy()

        # Přidání trendových indikátorů, pokud existují
        if trend_features:
            logging.info(f"Přidávám {len(trend_features)} trendových indikátorů do modelu")
            all_features.extend(trend_features)

        # Přidání příznaků sentimentu, pokud existují
        if sentiment_features:
            logging.info(f"Přidávám {len(sentiment_features)} příznaků sentimentu do modelu")
            all_features.extend(sentiment_features)

        # Odstranění duplicit
        features = list(dict.fromkeys(all_features))
        logging.info(f"Celkem použito {len(features)} příznaků pro trénování modelu")

        # Kontrola, zda všechny příznaky existují v datech
        missing_features = [f for f in features if f not in df.columns]
        if missing_features:
            logging.warning(f"Chybí následující příznaky: {missing_features}")
            # Odstranění chybějících příznaků
            features = [f for f in features if f in df.columns]
            logging.info(f"Použito {len(features)} dostupných příznaků")

        # Remove any rows with NaN values before splitting
        df = df.dropna(subset=features + ['close_price'])

        X = df[features]
        y = df["close_price"].shift(-5)

        # Ensure alignment of X and y after shifting
        X = X[:-5]
        y = y[:-5]

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

        # Přímá kontrola dostupnosti GPU pro XGBoost
        gpu_available = True
        try:
            # Explicitní test XGBoost s GPU (nová syntaxe od verze 2.0.0)
            import numpy as np
            test_params = {
                'objective': 'reg:squarederror',
                'tree_method': 'hist',  # Efektivní algoritmus pro stromy
                'device': 'cuda'        # Použití GPU
            }
            test_model = xgb.XGBRegressor(**test_params)
            # Vytvoření malého testovacího datasetu
            test_X = np.random.rand(10, 5)
            test_y = np.random.rand(10)
            # Pokus o fit - pokud GPU není dostupná, vyhodí výjimku
            test_model.fit(test_X, test_y, verbose=False)
            logging.info("GPU je dostupná a funkční pro XGBoost, bude použita pro trénování")
        except Exception as e:
            gpu_available = False
            logging.warning(f"GPU není dostupná pro XGBoost: {e}")
            logging.info("Použiji CPU pro trénování modelu")

            # Optimalizace pro CPU
            import multiprocessing
            num_cores = multiprocessing.cpu_count()
            logging.info(f"Detekováno {num_cores} CPU jader, všechna budou využita pro trénování")

        # Adaptivní parametry na základě tržních podmínek
        param_grid = {
            'n_estimators': [100, 200, 500],
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 5, 7],
            'min_child_weight': [1, 3, 5],
            'subsample': [0.7, 0.8, 0.9],
            'colsample_bytree': [0.7, 0.8, 0.9],
            'reg_alpha': [0.1, 0.5, 1.0],
            'reg_lambda': [0.1, 1.0, 5.0]
        }

        # Přidání parametrů pro GPU nebo CPU
        if gpu_available:
            # Nastavení pevných parametrů pro GPU (nová syntaxe od verze 2.0.0)
            gpu_params = {
                'tree_method': 'hist',  # Efektivní algoritmus pro stromy
                'device': 'cuda',       # Použití GPU
                # Optimalizace pro GPU
                'max_bin': 256,        # Zvýšení pro lepší využití GPU
                'n_jobs': 1,           # Na GPU je lepší použít 1 vlákno
                'objective': 'reg:squarederror',
                'random_state': 42
            }

            # Odstranění parametrů z grid search, které budou pevné
            for param in gpu_params:
                if param in param_grid:
                    del param_grid[param]

            logging.info(f"Použity GPU parametry: {gpu_params}")

            # Vytvoření základního modelu s GPU parametry
            base_model = xgb.XGBRegressor(**gpu_params)
        else:
            # Optimalizace pro CPU
            import multiprocessing
            num_cores = multiprocessing.cpu_count()

            # CPU parametry
            cpu_params = {
                'tree_method': 'hist',  # Efektivní algoritmus pro CPU
                'n_jobs': num_cores,    # Využití všech CPU jader
                'predictor': 'cpu_predictor',
                'objective': 'reg:squarederror',
                'random_state': 42
            }

            # Odstranění parametrů z grid search, které budou pevné
            for param in cpu_params:
                if param in param_grid:
                    del param_grid[param]

            logging.info(f"Použity CPU parametry: {cpu_params}")

            # Vytvoření základního modelu s CPU parametry
            base_model = xgb.XGBRegressor(**cpu_params)

        # Úprava parametrů podle tržních podmínek
        if 'volatile' in market_condition:
            # Pro volatilní trh zvýšíme regularizaci
            param_grid['reg_alpha'] = [0.5, 1.0, 2.0]
            param_grid['reg_lambda'] = [1.0, 5.0, 10.0]
            param_grid['learning_rate'] = [0.01, 0.03, 0.05]
        elif 'stable' in market_condition:
            # Pro stabilní trh zvýšíme kapacitu modelu
            param_grid['n_estimators'] = [200, 500, 1000]
            param_grid['max_depth'] = [5, 7, 9]
        elif market_condition == 'breakout':
            # Pro průlomy potřebujeme rychlejší reakci
            param_grid['learning_rate'] = [0.05, 0.1, 0.15]
        elif market_condition == 'reversal':
            # Pro obraty trendu potřebujeme vyšší přesnost
            param_grid['n_estimators'] = [500, 1000, 1500]

        # Omezení počtu kombinací pro grid search, aby byl výpočet rychlejší
        # Omezíme počet hodnot pro každý parametr na 2
        for param in param_grid:
            if isinstance(param_grid[param], list) and len(param_grid[param]) > 2:
                param_grid[param] = [param_grid[param][0], param_grid[param][-1]]

        logging.info(f"Optimalizovaný param_grid: {param_grid}")

        # Příprava dat pro GridSearchCV
        if gpu_available:
            # Pokud používáme GPU, přeskočíme GridSearchCV a použijeme přímo model s výchozími parametry
            # Toto je rychlejší a efektivnější řešení pro GPU
            logging.info("Přeskakuji GridSearchCV a používám přímo model s GPU parametry")
            grid_search = None
            best_model = base_model
        else:
            # Pro CPU použijeme standardní GridSearchCV
            # Použijeme všechna dostupná CPU jádra
            logging.info("Používám GridSearchCV pro nalezení optimálních parametrů na CPU")
            grid_search = GridSearchCV(estimator=base_model, param_grid=param_grid, cv=3, scoring='neg_mean_squared_error', verbose=1, n_jobs=-1)

        logging.info("Hledám optimální parametry modelu...")

        if grid_search is not None:
            # Pro CPU použijeme GridSearchCV
            grid_search.fit(X_train, y_train)
            logging.info(f"Nejlepší parametry: {grid_search.best_params_}")
            best_model = grid_search.best_estimator_
            logging.info("Trénuji model s optimálními parametry...")
            best_model.fit(X_train, y_train)
        else:
            # Pro GPU použijeme přímo model s GPU parametry
            logging.info("Trénuji model s GPU parametry...")
            # Vytvoření DMatrix pro lepší výkon na GPU
            dtrain = xgb.DMatrix(X_train, label=y_train)
            # Získání parametrů z modelu
            params = best_model.get_params()
            # Odstranění parametrů, které nejsou potřeba pro nízkoúrovňový XGBoost
            for param in ['n_estimators', 'base_score', 'booster', 'callbacks', 'early_stopping_rounds',
                          'enable_categorical', 'feature_types', 'gamma', 'grow_policy', 'importance_type',
                          'interaction_constraints', 'max_cat_threshold', 'max_cat_to_onehot',
                          'max_delta_step', 'max_leaves', 'missing', 'monotone_constraints',
                          'num_parallel_tree', 'random_state', 'verbosity']:
                if param in params:
                    del params[param]

            # Trénování modelu pomocí nízkoúrovňového API
            num_round = params.get('n_estimators', 200)
            booster = xgb.train(params, dtrain, num_round)

            # Aktualizace boosteru v modelu
            best_model._Booster = booster

        # Predikce s ohledem na zařízení
        if gpu_available:
            # Pro GPU použijeme DMatrix a nízkoúrovňový booster
            dtest = xgb.DMatrix(X_test)
            y_pred = best_model._Booster.predict(dtest)
        else:
            # Pro CPU použijeme standardní predikci
            y_pred = best_model.predict(X_test)

        mse = mean_squared_error(y_test, y_pred)
        rmse = mse**0.5
        mae = mean_absolute_error(y_test, y_pred)
        logging.info(f"Vypočtené chyby modelu:")
        logging.info(f" - RMSE: {rmse}")
        logging.info(f" - MAE: {mae}")

        plt.figure(figsize=(10, 6))
        xgb.plot_importance(best_model)
        plt.savefig('feature_importance.png')
        logging.info("Graf důležitosti příznaků uložen jako 'feature_importance.png'")

        plt.figure(figsize=(12, 6))
        plt.plot(y_test.values, label='Skutečné ceny')
        plt.plot(y_pred, label='Predikované ceny')
        plt.legend()
        plt.title('Predikce vs. Skutečnost')
        plt.savefig('prediction_vs_actual.png')
        logging.info("Graf predikce vs. realita uložen jako 'prediction_vs_actual.png'")

        # Uložení modelu podle tržních podmínek
        model_filename = f"alch_model_{market_condition}.pkl"
        joblib.dump(best_model, model_filename)
        logging.info(f"Model byl uložen do '{model_filename}'")

        # Uložení také jako výchozí model
        joblib.dump(best_model, "alch_price_model.pkl")
        logging.info("Model byl také uložen jako výchozí 'alch_price_model.pkl'")

        info = {
            'date_trained': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_range': f"{df['timestamp'].min()} až {df['timestamp'].max()}",
            'rmse': rmse,
            'mae': mae,
            'num_records': len(df),
            'market_condition': market_condition,
            'features_used': len(features),
            'model_filename': model_filename
        }

        with open('model_info.txt', 'w') as f:
            for key, value in info.items():
                f.write(f"{key}: {value}\n")

        logging.info("Informace o modelu uloženy do 'model_info.txt'")

        # Aktualizace dat o sentimentu
        try:
            update_sentiment_data()
            logging.info("Data o sentimentu aktualizována")
        except Exception as e:
            logging.error(f"Chyba při aktualizaci dat o sentimentu: {e}")

        # Provedení kroku adaptivního učení
        try:
            adaptive_learning_step()
            logging.info("Krok adaptivního učení dokončen")
        except Exception as e:
            logging.error(f"Chyba při adaptivním učení: {e}")

    except Exception as e:
        logging.error(f"Chyba při trénování modelu: {e}")

def predict():
    try:
        conn = connect_to_db()
        price_query = """
        SELECT TOP 1 close_price
        FROM alch_price_history
        ORDER BY timestamp DESC
        """
        current_price_df = pd.read_sql(price_query, conn)
        current_price = current_price_df['close_price'].iloc[0] if not current_price_df.empty else "N/A"

        # Rozšíření dotazu o všechny dostupné indikátory
        query = """
        SELECT TOP 1 *
        FROM alch_indikatory
        ORDER BY timestamp DESC
        """
        df = pd.read_sql(query, conn)
        conn.close()

        if df.empty:
            logging.error("Nedostatek dat pro predikci")
            return

        logging.info(f"=== Diagnostické informace ({datetime.now()}) ===")
        logging.info(f"Aktuální cena: {current_price}")

        # Detekce aktuálních tržních podmínek
        try:
            market_condition = detect_market_condition()
            logging.info(f"Aktuální tržní podmínky: {market_condition}")

            # Získání sentimentu trhu
            sentiment, _ = calculate_market_sentiment()  # Ignorujeme detaily sentimentu
            logging.info(f"Sentiment trhu: {sentiment:.2f} ({interpret_sentiment(sentiment)})")
        except Exception as e:
            logging.error(f"Chyba při detekci tržních podmínek nebo sentimentu: {e}")
            market_condition = "unknown"
            sentiment = 0.0

        # Výběr modelu podle tržních podmínek
        model_path = f"alch_model_{market_condition}.pkl"
        if not os.path.exists(model_path):
            logging.info(f"Model pro podmínky '{market_condition}' neexistuje, použiji výchozí model")
            model_path = "alch_price_model.pkl"

        logging.info(f"Používám model: {model_path}")
        model = joblib.load(model_path)
        logging.info("Model úspěšně načten")

        # Získání všech příznaků, které model očekává
        expected_features = model.get_booster().feature_names
        if expected_features is None:
            # Pokud model nemá uložené názvy příznaků, použijeme základní sadu
            expected_features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
            logging.warning(f"Model nemá uložené názvy příznaků, použiji základní sadu: {expected_features}")

        # Kontrola, zda máme všechny potřebné příznaky
        missing_features = [f for f in expected_features if f not in df.columns]
        if missing_features:
            logging.warning(f"Chybí následující příznaky: {missing_features}")
            # Pokud chybí příznaky, použijeme základní sadu
            expected_features = [f for f in expected_features if f in df.columns]
            if not expected_features:
                expected_features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
                logging.warning(f"Použiji základní sadu příznaků: {expected_features}")

        # Příprava dat pro predikci
        nove_data = df[expected_features]

        # Logování použitých příznaků
        logging.info(f"Použito {len(expected_features)} příznaků pro predikci")
        logging.info("Použité hodnoty indikátorů pro predikci:")
        for column in expected_features[:5]:  # Zobrazíme jen prvních 5 pro přehlednost
            logging.info(f"  {column}: {df[column].iloc[0]}")
        if len(expected_features) > 5:
            logging.info(f"  ... a dalších {len(expected_features) - 5} příznaků")

        timestamp = df['timestamp'].iloc[0] if not df.empty else "N/A"
        logging.info(f"Čas posledního záznamu indikátorů: {timestamp}")

        # Kontrola, zda model používá GPU
        is_gpu_model = False
        try:
            model_params = model.get_params()
            is_gpu_model = model_params.get('device') == 'cuda'
            if is_gpu_model:
                logging.info("Model používá GPU (CUDA) pro predikci")
        except:
            pass

        # Predikce
        try:
            # Vytvoření DMatrix pro lepší kontrolu zařízení
            import xgboost as xgb
            if is_gpu_model:
                # Explicitní nastavení zařízení pro DMatrix na GPU
                dtest = xgb.DMatrix(nove_data)
                # Nastavení zařízení pro predikci
                booster = model.get_booster()
                predikovana_cena = booster.predict(dtest)
                logging.info("Predikce provedena s využitím GPU (DMatrix)")
            else:
                # Pro CPU model použijeme standardní predikci
                predikovana_cena = model.predict(nove_data)
                logging.info("Predikce provedena s využitím CPU")
        except Exception as e:
            logging.warning(f"Chyba při predikci: {e}")
            # Fallback na standardní predikci
            try:
                predikovana_cena = model.predict(nove_data)
                logging.info("Predikce provedena standardním způsobem (fallback)")
            except Exception as e2:
                logging.error(f"Chyba i při standardní predikci: {e2}")
                # Poslední pokus s explicitním nastavením CPU
                predikovana_cena = model.predict(nove_data, device='cpu')

        # Výpočet jistoty predikce (aproximace)
        try:
            # Pokud model podporuje predict_proba, použijeme ji
            prediction_confidence = 0.95  # Výchozí hodnota

            # Úprava jistoty podle sentimentu a tržních podmínek
            if 'volatile' in market_condition:
                prediction_confidence *= 0.8  # Snížení jistoty pro volatilní trh
            elif 'stable' in market_condition:
                prediction_confidence *= 1.1  # Zvýšení jistoty pro stabilní trh

            # Omezení na rozsah 0-1
            prediction_confidence = min(0.99, max(0.5, prediction_confidence))
        except:
            prediction_confidence = 0.9  # Výchozí hodnota, pokud nelze vypočítat

        # Výstup predikce
        logging.info(f"\n📊 Aktuální cena: {current_price}")
        logging.info(f"🔮 Predikovaná cena za 5 minut: {predikovana_cena[0]}")
        logging.info(f"🔄 Rozdíl: {predikovana_cena[0] - current_price} ({((predikovana_cena[0] - current_price) / current_price * 100):.2f}%)")
        logging.info(f"Jistota predikce: {prediction_confidence:.2%}")

        # Určení směru
        if predikovana_cena[0] > current_price:
            direction = "růst"
            emoji = "📈"
        elif predikovana_cena[0] < current_price:
            direction = "pokles"
            emoji = "📉"
        else:
            direction = "stagnace"
            emoji = "🔄"

        # Kombinace s tržními podmínkami a sentimentem
        sentiment_direction = "neutrální"
        if sentiment > 0.3:
            sentiment_direction = "pozitivní"
        elif sentiment < -0.3:
            sentiment_direction = "negativní"

        logging.info(f"{emoji} SIGNÁL: Očekáván {direction} ceny")
        logging.info(f"Tržní podmínky: {market_condition}, Sentiment: {sentiment_direction}")

        # Uložení predikce do databáze pro pozdější vyhodnocení
        try:
            conn = connect_to_db()
            cursor = conn.cursor()

            # Kontrola, zda tabulka existuje
            cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'alch_predictions')
            BEGIN
                CREATE TABLE alch_predictions (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    timestamp DATETIME NOT NULL,
                    current_price FLOAT,
                    predicted_price FLOAT,
                    market_condition NVARCHAR(50),
                    sentiment FLOAT,
                    confidence FLOAT,
                    actual_price FLOAT NULL,
                    error_pct FLOAT NULL
                )
            END
            """)

            # Vložení predikce
            cursor.execute("""
            INSERT INTO alch_predictions (timestamp, current_price, predicted_price, market_condition, sentiment, confidence)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            datetime.now(),
            float(current_price),
            float(predikovana_cena[0]),
            market_condition,
            float(sentiment),
            float(prediction_confidence)
            )

            conn.commit()
            cursor.close()
            conn.close()
            logging.info("Predikce uložena do databáze")
        except Exception as e:
            logging.error(f"Chyba při ukládání predikce do databáze: {e}")

    except Exception as e:
        logging.error(f"❌ Chyba při načítání modelu nebo predikci: {e}")

def interpret_sentiment(sentiment: float) -> str:
    """Interpretuje hodnotu sentimentu jako text."""
    if sentiment > 0.6:
        return "velmi pozitivní"
    elif sentiment > 0.2:
        return "pozitivní"
    elif sentiment > -0.2:
        return "neutrální"
    elif sentiment > -0.6:
        return "negativní"
    else:
        return "velmi negativní"

def main():
    try:
        # Inicializace databáze
        conn = connect_to_db()
        cursor = conn.cursor()
        ensure_tables(cursor)
        cursor.close()
        conn.close()

        # Aktualizace dat
        fetch_and_save_latest_kline(symbol)
        aktualizuj_indikatory()

        # Aktualizace dat o sentimentu
        try:
            update_sentiment_data()
            logging.info("Data o sentimentu aktualizována")
        except Exception as e:
            logging.error(f"Chyba při aktualizaci dat o sentimentu: {e}")

        # Kontrola, zda je potřeba přetrénovat model
        if should_retrain_model():
            logging.info("Model potřebuje přetrénování")
            train_and_save_model()
        else:
            logging.info("Model funguje dobře, přetrénování není potřeba")

            # Provedení kroku adaptivního učení i když nepřetrénováváme
            try:
                adaptive_learning_step()
                logging.info("Krok adaptivního učení dokončen")
            except Exception as e:
                logging.error(f"Chyba při adaptivním učení: {e}")

        # Predikce
        predict()

        logging.info("Skript úspěšně dokončen")
    except Exception as e:
        logging.error(f"Chyba v hlavním skriptu: {e}")

if __name__ == "__main__":
    main()
