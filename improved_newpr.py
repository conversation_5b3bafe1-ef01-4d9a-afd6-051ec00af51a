"""
Vyle<PERSON><PERSON><PERSON><PERSON> predikční systém s ensemble modely a pokročilými features.
Kombinuje XGBoost, Random Forest a Linear Regression pro lep<PERSON><PERSON> přes<PERSON>t.
"""

import pandas as pd
import numpy as np
import ta
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import pyodbc
import ccxt
import time
from datetime import datetime, timezone, timedelta

# Konfigurace
SYMBOL = 'SOLUSDT'
SYMBOL_DB = 'SOLUSDT'
TIMEFRAME = '1m'
PREDICTION_HORIZON = 5  # 5 minut
DB_TABLE_NAME = f'{SYMBOL_DB}_OHLCV_{TIMEFRAME}'

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

# Ensemble váhy (budou se adaptivně upravovat)
ENSEMBLE_WEIGHTS = {
    'xgb': 0.5,
    'rf': 0.3,
    'lr': 0.2
}

class EnsemblePredictionSystem:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        self.performance_history = []
        
    def connect_to_db(self):
        """Připojení k databázi."""
        try:
            return pyodbc.connect(CONN_STR)
        except Exception as e:
            print(f"Chyba připojení k databázi: {e}")
            return None
    
    def get_latest_data(self, limit=1000):
        """Získá nejnovější data z databáze."""
        conn = self.connect_to_db()
        if not conn:
            return None
            
        try:
            query = f"""
            SELECT TOP {limit} timestamp_utc, open_price, high_price, low_price, close_price, volume_amt
            FROM {DB_TABLE_NAME}
            ORDER BY timestamp_utc DESC
            """
            df = pd.read_sql(query, conn)
            df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
            df.set_index('timestamp_utc', inplace=True)
            df.sort_index(inplace=True)
            return df
        except Exception as e:
            print(f"Chyba při načítání dat: {e}")
            return None
        finally:
            conn.close()
    
    def add_advanced_features(self, df):
        """Přidá pokročilé technické indikátory."""
        try:
            # Základní moving averages
            for window in [5, 10, 20, 50]:
                df[f'sma_{window}'] = ta.trend.sma_indicator(df['close_price'], window=window)
                df[f'ema_{window}'] = ta.trend.ema_indicator(df['close_price'], window=window)
            
            # RSI s různými okny
            for window in [6, 14, 21]:
                df[f'rsi_{window}'] = ta.momentum.rsi(df['close_price'], window=window)
            
            # MACD
            df['macd'] = ta.trend.macd(df['close_price'])
            df['macd_signal'] = ta.trend.macd_signal(df['close_price'])
            df['macd_hist'] = ta.trend.macd_diff(df['close_price'])
            
            # Bollinger Bands
            df['bb_high'] = ta.volatility.bollinger_hband(df['close_price'], window=20)
            df['bb_low'] = ta.volatility.bollinger_lband(df['close_price'], window=20)
            df['bb_mid'] = ta.volatility.bollinger_mavg(df['close_price'], window=20)
            df['bb_width'] = (df['bb_high'] - df['bb_low']) / df['bb_mid']
            df['bb_position'] = (df['close_price'] - df['bb_low']) / (df['bb_high'] - df['bb_low'])
            
            # ATR
            for window in [5, 14, 21]:
                df[f'atr_{window}'] = ta.volatility.average_true_range(
                    df['high_price'], df['low_price'], df['close_price'], window=window
                )
            
            # Stochastic
            df['stoch_k'] = ta.momentum.stoch(df['high_price'], df['low_price'], df['close_price'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['high_price'], df['low_price'], df['close_price'])
            
            # Williams %R
            df['williams_r'] = ta.momentum.williams_r(df['high_price'], df['low_price'], df['close_price'])
            
            # Rate of Change
            for window in [5, 10, 20]:
                df[f'roc_{window}'] = ta.momentum.roc(df['close_price'], window=window)
            
            # Price action features
            df['price_change'] = df['close_price'].pct_change()
            df['volume_change'] = df['volume_amt'].pct_change()
            df['high_low_ratio'] = (df['high_price'] - df['low_price']) / df['close_price']
            df['open_close_ratio'] = (df['close_price'] - df['open_price']) / df['open_price']
            
            # Volatility features
            for window in [5, 10, 20]:
                df[f'price_std_{window}'] = df['close_price'].rolling(window=window).std()
                df[f'volume_std_{window}'] = df['volume_amt'].rolling(window=window).std()
            
            # Lag features
            for lag in [1, 2, 3, 5, 10, 15, 20, 30]:
                df[f'close_lag_{lag}'] = df['close_price'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume_amt'].shift(lag)
                df[f'price_change_lag_{lag}'] = df['price_change'].shift(lag)
            
            # Target variable
            df['target'] = df['close_price'].shift(-PREDICTION_HORIZON)
            
            # Vyplnění NaN hodnot
            df.fillna(method='ffill', inplace=True)
            df.fillna(method='bfill', inplace=True)
            df.fillna(0, inplace=True)
            
            return df
            
        except Exception as e:
            print(f"Chyba při přidávání features: {e}")
            return df
    
    def prepare_data(self, df):
        """Připraví data pro trénování."""
        # Odstranění řádků s NaN v target
        df_clean = df.dropna(subset=['target']).copy()
        
        # Výběr feature sloupců (všechny kromě target a původních OHLCV)
        exclude_cols = ['target', 'open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']
        feature_cols = [col for col in df_clean.columns if col not in exclude_cols]
        
        X = df_clean[feature_cols]
        y = df_clean['target']
        
        # Uložení názvů features
        self.feature_names = feature_cols
        
        return X, y
    
    def train_models(self, X, y):
        """Natrénuje ensemble modelů."""
        print("Trénování ensemble modelů...")
        
        # Rozdělení dat
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # Škálování dat
        self.scalers['main'] = MinMaxScaler()
        X_train_scaled = self.scalers['main'].fit_transform(X_train)
        X_test_scaled = self.scalers['main'].transform(X_test)
        
        # XGBoost
        print("Trénování XGBoost...")
        self.models['xgb'] = xgb.XGBRegressor(
            n_estimators=300,
            learning_rate=0.01,
            max_depth=6,
            subsample=0.9,
            colsample_bytree=0.9,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            n_jobs=-1
        )
        self.models['xgb'].fit(X_train, y_train)
        
        # Random Forest
        print("Trénování Random Forest...")
        self.models['rf'] = RandomForestRegressor(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        self.models['rf'].fit(X_train_scaled, y_train)
        
        # Linear Regression
        print("Trénování Linear Regression...")
        self.models['lr'] = LinearRegression()
        self.models['lr'].fit(X_train_scaled, y_train)
        
        # Evaluace modelů
        print("\nEvaluace modelů:")
        for name, model in self.models.items():
            if name == 'xgb':
                pred = model.predict(X_test)
            else:
                pred = model.predict(X_test_scaled)
            
            mae = mean_absolute_error(y_test, pred)
            rmse = np.sqrt(mean_squared_error(y_test, pred))
            print(f"{name.upper()}: MAE={mae:.4f}, RMSE={rmse:.4f}")
        
        print("Trénování dokončeno!")
    
    def predict(self, latest_data):
        """Provede ensemble predikci."""
        try:
            # Příprava dat
            df_features = self.add_advanced_features(latest_data.copy())
            
            # Získání posledního řádku pro predikci
            last_row = df_features.iloc[-1:][self.feature_names]
            
            # Škálování
            last_row_scaled = self.scalers['main'].transform(last_row)
            
            # Predikce jednotlivých modelů
            predictions = {}
            predictions['xgb'] = self.models['xgb'].predict(last_row)[0]
            predictions['rf'] = self.models['rf'].predict(last_row_scaled)[0]
            predictions['lr'] = self.models['lr'].predict(last_row_scaled)[0]
            
            # Ensemble predikce
            ensemble_pred = (
                predictions['xgb'] * ENSEMBLE_WEIGHTS['xgb'] +
                predictions['rf'] * ENSEMBLE_WEIGHTS['rf'] +
                predictions['lr'] * ENSEMBLE_WEIGHTS['lr']
            )
            
            return ensemble_pred, predictions
            
        except Exception as e:
            print(f"Chyba při predikci: {e}")
            return None, None
    
    def save_prediction(self, current_price, predicted_price, individual_preds):
        """Uloží predikci do databáze."""
        conn = self.connect_to_db()
        if not conn:
            return False
            
        try:
            cursor = conn.cursor()
            prediction_time = datetime.now(timezone.utc)
            target_time = prediction_time + timedelta(minutes=PREDICTION_HORIZON)
            
            cursor.execute("""
            INSERT INTO PREDICTIONS (symbol, prediction_time, target_time, predicted_price, horizon_minutes)
            VALUES (?, ?, ?, ?, ?)
            """, (SYMBOL, prediction_time, target_time, predicted_price, PREDICTION_HORIZON))
            
            conn.commit()
            print(f"Predikce uložena: {predicted_price:.4f} USDT")
            return True
            
        except Exception as e:
            print(f"Chyba při ukládání predikce: {e}")
            return False
        finally:
            conn.close()
    
    def run_prediction_loop(self):
        """Hlavní smyčka pro kontinuální predikce."""
        print(f"🚀 Spouštím vylepšený predikční systém pro {SYMBOL}")
        print(f"📊 Predikční horizont: {PREDICTION_HORIZON} minut")
        print(f"🤖 Ensemble modely: XGBoost + Random Forest + Linear Regression")
        print("=" * 60)
        
        while True:
            try:
                # Načtení dat
                print("📥 Načítám data...")
                data = self.get_latest_data(limit=2000)
                if data is None or len(data) < 100:
                    print("❌ Nedostatek dat pro trénování")
                    time.sleep(60)
                    continue
                
                # Příprava features
                print("🔧 Připravuji features...")
                df_with_features = self.add_advanced_features(data.copy())
                X, y = self.prepare_data(df_with_features)
                
                if len(X) < 50:
                    print("❌ Nedostatek dat po přípravě")
                    time.sleep(60)
                    continue
                
                # Trénování modelů
                self.train_models(X, y)
                
                # Predikce
                current_price = data['close_price'].iloc[-1]
                predicted_price, individual_preds = self.predict(data)
                
                if predicted_price is None:
                    print("❌ Predikce selhala")
                    time.sleep(60)
                    continue
                
                # Výsledky
                price_change = predicted_price - current_price
                price_change_pct = (price_change / current_price) * 100
                
                print(f"\n📈 PREDIKCE PRO {SYMBOL}")
                print(f"⏰ Čas: {datetime.now().strftime('%H:%M:%S')}")
                print(f"💰 Aktuální cena: {current_price:.4f} USDT")
                print(f"🔮 Predikce za {PREDICTION_HORIZON}min: {predicted_price:.4f} USDT")
                print(f"📊 Změna: {price_change:+.4f} USDT ({price_change_pct:+.2f}%)")
                print(f"🎯 Individuální predikce:")
                for model, pred in individual_preds.items():
                    print(f"   {model.upper()}: {pred:.4f} USDT")
                
                # Uložení predikce
                self.save_prediction(current_price, predicted_price, individual_preds)
                
                print("⏳ Čekám 60 sekund do další predikce...")
                print("=" * 60)
                time.sleep(60)
                
            except KeyboardInterrupt:
                print("\n🛑 Ukončení na žádost uživatele")
                break
            except Exception as e:
                print(f"❌ Chyba v hlavní smyčce: {e}")
                time.sleep(60)

def main():
    """Hlavní funkce."""
    system = EnsemblePredictionSystem()
    system.run_prediction_loop()

if __name__ == "__main__":
    main()
