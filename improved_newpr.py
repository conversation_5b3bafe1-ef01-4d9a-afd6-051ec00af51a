"""
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> predikč<PERSON><PERSON> systém s ensemble modely a pokročilými features.
Kombinuje XGBoost, Random Forest a Linear Regression pro lep<PERSON><PERSON> přes<PERSON>t.
"""

import pandas as pd
import numpy as np
import ta
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import pyodbc
import ccxt
import time
from datetime import datetime, timezone, timedelta

# ANSI color codes for output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

# Konfigurace
SYMBOL = 'SOLUSDT'
SYMBOL_DB = 'SOLUSDT'
TIMEFRAME = '1m'
PREDICTION_HORIZON = 5  # 5 minut
DB_TABLE_NAME = f'{SYMBOL_DB}_OHLCV_{TIMEFRAME}'

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

# Ensemble váhy (budou se adaptivně upravovat)
ENSEMBLE_WEIGHTS = {
    'xgb': 0.5,
    'rf': 0.3,
    'lr': 0.2
}

class EnsemblePredictionSystem:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        self.performance_history = []

    def connect_to_db(self):
        """Připojení k databázi."""
        try:
            return pyodbc.connect(CONN_STR)
        except Exception as e:
            print(f"Chyba připojení k databázi: {e}")
            return None

    def get_latest_data(self, limit=1000):
        """Získá nejnovější data z databáze."""
        conn = self.connect_to_db()
        if not conn:
            return None

        try:
            cursor = conn.cursor()
            query = f"""
            SELECT TOP {limit} timestamp_utc, open_price, high_price, low_price, close_price, volume_amt
            FROM {DB_TABLE_NAME}
            ORDER BY timestamp_utc DESC
            """
            cursor.execute(query)
            rows = cursor.fetchall()

            if not rows:
                return None

            # Vytvoření DataFrame manuálně místo pd.read_sql
            columns = ['timestamp_utc', 'open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']
            df = pd.DataFrame.from_records(rows, columns=columns)
            df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
            df.set_index('timestamp_utc', inplace=True)
            df.sort_index(inplace=True)
            cursor.close()
            return df
        except Exception as e:
            print(f"Chyba při načítání dat: {e}")
            return None
        finally:
            conn.close()

    def add_advanced_features(self, df):
        """Přidá pokročilé technické indikátory."""
        try:
            # Základní moving averages
            for window in [5, 10, 20, 50]:
                df[f'sma_{window}'] = ta.trend.sma_indicator(df['close_price'], window=window)
                df[f'ema_{window}'] = ta.trend.ema_indicator(df['close_price'], window=window)

            # RSI s různými okny
            for window in [6, 14, 21]:
                df[f'rsi_{window}'] = ta.momentum.rsi(df['close_price'], window=window)

            # MACD
            df['macd'] = ta.trend.macd(df['close_price'])
            df['macd_signal'] = ta.trend.macd_signal(df['close_price'])
            df['macd_hist'] = ta.trend.macd_diff(df['close_price'])

            # Bollinger Bands
            df['bb_high'] = ta.volatility.bollinger_hband(df['close_price'], window=20)
            df['bb_low'] = ta.volatility.bollinger_lband(df['close_price'], window=20)
            df['bb_mid'] = ta.volatility.bollinger_mavg(df['close_price'], window=20)
            df['bb_width'] = (df['bb_high'] - df['bb_low']) / df['bb_mid']
            df['bb_position'] = (df['close_price'] - df['bb_low']) / (df['bb_high'] - df['bb_low'])

            # ATR
            for window in [5, 14, 21]:
                df[f'atr_{window}'] = ta.volatility.average_true_range(
                    df['high_price'], df['low_price'], df['close_price'], window=window
                )

            # Stochastic
            df['stoch_k'] = ta.momentum.stoch(df['high_price'], df['low_price'], df['close_price'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['high_price'], df['low_price'], df['close_price'])

            # Williams %R
            df['williams_r'] = ta.momentum.williams_r(df['high_price'], df['low_price'], df['close_price'])

            # Rate of Change
            for window in [5, 10, 20]:
                df[f'roc_{window}'] = ta.momentum.roc(df['close_price'], window=window)

            # Price action features
            df['price_change'] = df['close_price'].pct_change()
            df['volume_change'] = df['volume_amt'].pct_change()
            df['high_low_ratio'] = (df['high_price'] - df['low_price']) / df['close_price']
            df['open_close_ratio'] = (df['close_price'] - df['open_price']) / df['open_price']

            # Volatility features
            for window in [5, 10, 20]:
                df[f'price_std_{window}'] = df['close_price'].rolling(window=window).std()
                df[f'volume_std_{window}'] = df['volume_amt'].rolling(window=window).std()

            # Lag features
            for lag in [1, 2, 3, 5, 10, 15, 20, 30]:
                df[f'close_lag_{lag}'] = df['close_price'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume_amt'].shift(lag)
                df[f'price_change_lag_{lag}'] = df['price_change'].shift(lag)

            # Target variable
            df['target'] = df['close_price'].shift(-PREDICTION_HORIZON)

            # Vyplnění NaN hodnot
            df.ffill(inplace=True)
            df.bfill(inplace=True)
            df.fillna(0, inplace=True)

            return df

        except Exception as e:
            print(f"Chyba při přidávání features: {e}")
            return df

    def prepare_data(self, df):
        """Připraví data pro trénování."""
        # Odstranění řádků s NaN v target
        df_clean = df.dropna(subset=['target']).copy()

        # Výběr feature sloupců (všechny kromě target a původních OHLCV)
        exclude_cols = ['target', 'open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']
        feature_cols = [col for col in df_clean.columns if col not in exclude_cols]

        X = df_clean[feature_cols]
        y = df_clean['target']

        # Uložení názvů features
        self.feature_names = feature_cols

        return X, y

    def train_models(self, X, y):
        """Natrénuje ensemble modelů."""
        print("Trénování ensemble modelů...")

        # Rozdělení dat
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

        # Škálování dat
        self.scalers['main'] = MinMaxScaler()
        X_train_scaled = self.scalers['main'].fit_transform(X_train)
        X_test_scaled = self.scalers['main'].transform(X_test)

        # XGBoost
        print("Trénování XGBoost...")
        self.models['xgb'] = xgb.XGBRegressor(
            n_estimators=300,
            learning_rate=0.01,
            max_depth=6,
            subsample=0.9,
            colsample_bytree=0.9,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            n_jobs=-1
        )
        self.models['xgb'].fit(X_train, y_train)

        # Random Forest
        print("Trénování Random Forest...")
        self.models['rf'] = RandomForestRegressor(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        self.models['rf'].fit(X_train_scaled, y_train)

        # Linear Regression
        print("Trénování Linear Regression...")
        self.models['lr'] = LinearRegression()
        self.models['lr'].fit(X_train_scaled, y_train)

        # Evaluace modelů
        print("\nEvaluace modelů:")
        for name, model in self.models.items():
            if name == 'xgb':
                pred = model.predict(X_test)
            else:
                pred = model.predict(X_test_scaled)

            mae = mean_absolute_error(y_test, pred)
            rmse = np.sqrt(mean_squared_error(y_test, pred))
            print(f"{name.upper()}: MAE={mae:.4f}, RMSE={rmse:.4f}")

        print("Trénování dokončeno!")

    def predict(self, latest_data):
        """Provede ensemble predikci."""
        try:
            # Příprava dat
            df_features = self.add_advanced_features(latest_data.copy())

            # Získání posledního řádku pro predikci
            last_row = df_features.iloc[-1:][self.feature_names]

            # Škálování
            last_row_scaled = self.scalers['main'].transform(last_row)

            # Predikce jednotlivých modelů
            predictions = {}
            predictions['xgb'] = self.models['xgb'].predict(last_row)[0]
            predictions['rf'] = self.models['rf'].predict(last_row_scaled)[0]
            predictions['lr'] = self.models['lr'].predict(last_row_scaled)[0]

            # Ensemble predikce
            ensemble_pred = (
                predictions['xgb'] * ENSEMBLE_WEIGHTS['xgb'] +
                predictions['rf'] * ENSEMBLE_WEIGHTS['rf'] +
                predictions['lr'] * ENSEMBLE_WEIGHTS['lr']
            )

            return ensemble_pred, predictions

        except Exception as e:
            print(f"Chyba při predikci: {e}")
            return None, None

    def save_prediction(self, current_price, predicted_price, individual_preds):
        """Uloží predikci do databáze."""
        conn = self.connect_to_db()
        if not conn:
            return False

        try:
            cursor = conn.cursor()
            prediction_time = datetime.now(timezone.utc)
            target_time = prediction_time + timedelta(minutes=PREDICTION_HORIZON)

            # Přidáme identifikátor modelu pro rozlišení
            model_identifier = "ENSEMBLE_IMPROVED"

            # Zkusíme vložit s model_type, pokud sloupec neexistuje, vložíme bez něj
            try:
                cursor.execute("""
                INSERT INTO PREDICTIONS (symbol, prediction_time, target_time, predicted_price, horizon_minutes, model_type)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (SYMBOL, prediction_time, target_time, predicted_price, PREDICTION_HORIZON, model_identifier))
            except:
                # Fallback pokud sloupec model_type neexistuje
                cursor.execute("""
                INSERT INTO PREDICTIONS (symbol, prediction_time, target_time, predicted_price, horizon_minutes)
                VALUES (?, ?, ?, ?, ?)
                """, (SYMBOL, prediction_time, target_time, predicted_price, PREDICTION_HORIZON))

            conn.commit()
            print(f"Predikce uložena: {predicted_price:.4f} USDT")
            return True

        except Exception as e:
            print(f"Chyba při ukládání predikce: {e}")
            return False
        finally:
            conn.close()

    def update_prediction_results(self):
        """Aktualizuje skutečné hodnoty a chyby pro předchozí predikce."""
        conn = self.connect_to_db()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # Nejprve zkontrolujeme kolik predikcí čeká na aktualizaci
            cursor.execute("""
            SELECT COUNT(*)
            FROM PREDICTIONS p
            WHERE p.actual_price IS NULL
            AND p.target_time <= GETUTCDATE()
            AND p.symbol = ?
            """, (SYMBOL,))
            pending_count = cursor.fetchone()[0]

            if pending_count > 0:
                print(f"📊 Aktualizuji {pending_count} predikcí...")

                # Aktualizujeme skutečné hodnoty
                cursor.execute("""
                UPDATE p
                SET
                    actual_price = o.close_price,
                    prediction_error = ABS(o.close_price - p.predicted_price),
                    prediction_error_pct = ABS((o.close_price - p.predicted_price) / p.predicted_price * 100)
                FROM PREDICTIONS p
                INNER JOIN SOLUSDT_OHLCV_1m o
                    ON DATEADD(SECOND, -30, p.target_time) <= o.timestamp_utc
                    AND o.timestamp_utc <= DATEADD(SECOND, 30, p.target_time)
                WHERE p.actual_price IS NULL
                    AND p.target_time <= GETUTCDATE()
                    AND p.symbol = ?
                """, (SYMBOL,))

                rows_affected = cursor.rowcount
                conn.commit()
                print(f"✅ Aktualizováno {rows_affected} predikcí skutečnými hodnotami.")

        except Exception as e:
            print(f"❌ Chyba při aktualizaci výsledků: {e}")
        finally:
            conn.close()

    def display_prediction_table(self):
        """Zobrazí tabulku s historií predikcí."""
        conn = self.connect_to_db()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # Získáme statistiky za posledních 24 hodin pouze pro ENSEMBLE model
            cursor.execute("""
            SELECT
                prediction_time,
                target_time,
                predicted_price,
                actual_price,
                ABS(ISNULL(actual_price - predicted_price, 0)) as error,
                ISNULL(ABS((actual_price - predicted_price) / predicted_price * 100), 0) as prediction_error_pct,
                ISNULL(model_type, 'UNKNOWN') as model_type
            FROM PREDICTIONS
            WHERE prediction_time >= DATEADD(hour, -24, GETUTCDATE())
                AND symbol = ?
                AND (model_type = 'ENSEMBLE_IMPROVED' OR model_type IS NULL)
            ORDER BY prediction_time DESC
            """, (SYMBOL,))

            historical = cursor.fetchall()

            if historical:
                print(f"\n{Colors.BOLD}📈 VÝSLEDKY PREDIKCÍ ZA POSLEDNÍCH 24 HODIN (ENSEMBLE MODEL){Colors.ENDC}")
                print(f"{Colors.BLUE}{'Čas predikce':^19} | {'Cílový čas':^19} | {'Predikce':^10} | {'Skutečná':^10} | {'Chyba':^8} | {'Chyba %':^8} | {'Model':^8} | Status{Colors.ENDC}")
                print("-" * 110)

                for row in historical:
                    # Převedeme stringy na datetime objekty pokud je potřeba
                    pred_time_obj = row[0]
                    target_time_obj = row[1]

                    if isinstance(pred_time_obj, str):
                        pred_time_obj = pd.to_datetime(pred_time_obj)
                    if isinstance(target_time_obj, str):
                        target_time_obj = pd.to_datetime(target_time_obj)

                    pred_time = pred_time_obj.strftime('%Y-%m-%d %H:%M')
                    target_time = target_time_obj.strftime('%Y-%m-%d %H:%M')
                    pred_price = f"{row[2]:.4f}" if row[2] is not None else "N/A"
                    actual = f"{row[3]:.4f}" if row[3] is not None else "čeká"
                    error = f"{row[4]:.4f}" if row[4] is not None and row[3] is not None else "N/A"
                    error_pct = f"{row[5]:.2f}%" if row[5] is not None and row[3] is not None else "N/A"
                    model_type = row[6] if len(row) > 6 and row[6] else "BASIC"
                    model_short = "ENS" if "ENSEMBLE" in model_type else "BAS"

                    # Barevné označení podle chyby
                    if row[3] is None:
                        status = f"{Colors.BLUE}ČEKÁ{Colors.ENDC}"
                    elif row[4] < 0.5:  # Výborná přesnost
                        status = f"{Colors.GREEN}PŘESNÁ{Colors.ENDC}"
                    elif row[4] < 2.0:  # Dobrá přesnost
                        status = f"{Colors.YELLOW}DOBRÁ{Colors.ENDC}"
                    else:  # Vysoká chyba
                        status = f"{Colors.RED}NEPŘESNÁ{Colors.ENDC}"

                    print(f"{pred_time:^19} | {target_time:^19} | {Colors.YELLOW}{pred_price:^10}{Colors.ENDC} | {actual:^10} | {error:^8} | {error_pct:^8} | {Colors.HEADER}{model_short:^8}{Colors.ENDC} | {status}")

                # Souhrnné statistiky
                completed = [r for r in historical if r[3] is not None]
                if completed:
                    avg_error = sum(r[4] for r in completed if r[4] is not None) / len(completed)
                    avg_error_pct = sum(r[5] for r in completed if r[5] is not None) / len(completed)

                    print(f"\n{Colors.BOLD}📊 STATISTIKY:{Colors.ENDC}")
                    print(f"📈 Celkem predikcí: {Colors.BLUE}{len(historical)}{Colors.ENDC}")
                    print(f"✅ Dokončených: {Colors.GREEN}{len(completed)}{Colors.ENDC}")
                    print(f"🎯 Průměrná chyba: {Colors.YELLOW}{avg_error:.4f} USDT{Colors.ENDC}")
                    print(f"📊 Průměrná chyba %: {Colors.YELLOW}{avg_error_pct:.2f}%{Colors.ENDC}")

                    # Přesnost podle kategorií
                    excellent = len([r for r in completed if r[4] < 0.5])
                    good = len([r for r in completed if 0.5 <= r[4] < 2.0])
                    poor = len([r for r in completed if r[4] >= 2.0])

                    print(f"🎯 Přesnost:")
                    print(f"   {Colors.GREEN}Výborná (<0.5 USDT): {excellent} ({excellent/len(completed)*100:.1f}%){Colors.ENDC}")
                    print(f"   {Colors.YELLOW}Dobrá (0.5-2.0 USDT): {good} ({good/len(completed)*100:.1f}%){Colors.ENDC}")
                    print(f"   {Colors.RED}Slabá (>2.0 USDT): {poor} ({poor/len(completed)*100:.1f}%){Colors.ENDC}")

                # Zobrazení posledních 5 predikcí
                print(f"\n{Colors.BOLD}🔮 POSLEDNÍCH 5 PREDIKCÍ:{Colors.ENDC}")
                cursor.execute("""
                SELECT TOP 5
                    prediction_time,
                    target_time,
                    predicted_price,
                    actual_price,
                    prediction_error,
                    prediction_error_pct,
                    LAG(predicted_price) OVER (ORDER BY prediction_time) as prev_prediction
                FROM PREDICTIONS
                WHERE symbol = ?
                ORDER BY prediction_time DESC
                """, (SYMBOL,))

                print(f"{Colors.BLUE}{'Čas predikce':^12} | {'Cílový čas':^12} | {'Predikce':^10} | {'Skutečná':^10} | {'Chyba':^8} | {'Chyba %':^8} | {'Trend':^6}{Colors.ENDC}")
                print("-" * 78)

                for row in cursor.fetchall():
                    # Převedeme stringy na datetime objekty pokud je potřeba
                    pred_time_obj = row[0]
                    target_time_obj = row[1]

                    if isinstance(pred_time_obj, str):
                        pred_time_obj = pd.to_datetime(pred_time_obj)
                    if isinstance(target_time_obj, str):
                        target_time_obj = pd.to_datetime(target_time_obj)

                    pred_time = pred_time_obj.strftime('%H:%M:%S')
                    target_time = target_time_obj.strftime('%H:%M:%S')
                    pred_price = f"{row[2]:.4f}" if row[2] is not None else "N/A"
                    actual = f"{row[3]:.4f}" if row[3] is not None else "čeká"
                    error = f"{row[4]:.4f}" if row[4] is not None and row[3] is not None else "N/A"
                    error_pct = f"{row[5]:.2f}%" if row[5] is not None and row[3] is not None else "N/A"

                    # Přidání šipky trendu
                    if row[6] is not None:
                        trend_arrow = ("↑" if row[2] > row[6] else
                                     "↓" if row[2] < row[6] else "→")
                        trend_color = (Colors.GREEN if row[2] > row[6] else
                                     Colors.RED if row[2] < row[6] else
                                     Colors.BLUE)
                    else:
                        trend_arrow = "•"
                        trend_color = Colors.BLUE

                    print(f"{pred_time:^12} | {target_time:^12} | {Colors.YELLOW}{pred_price:^10}{Colors.ENDC} | "
                          f"{actual:^10} | {error:^8} | {error_pct:^8} | {trend_color}{trend_arrow:^6}{Colors.ENDC}")
            else:
                print(f"{Colors.YELLOW}📊 Žádné predikce za posledních 24 hodin.{Colors.ENDC}")

        except Exception as e:
            print(f"❌ Chyba při zobrazení tabulky: {e}")
        finally:
            conn.close()

    def display_prediction_summary(self):
        """Zobrazí shrnutí predikcí podobné newpr.py."""
        conn = self.connect_to_db()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # Získáme posledních 10 predikcí pro analýzu trendu
            cursor.execute("""
            SELECT TOP 10
                predicted_price,
                prediction_time
            FROM PREDICTIONS
            WHERE symbol = ?
            ORDER BY prediction_time DESC
            """, (SYMBOL,))

            recent_predictions = cursor.fetchall()

            if len(recent_predictions) >= 2:
                prices = [float(row[0]) for row in recent_predictions]

                # Analýza trendu
                min_price = min(prices)
                max_price = max(prices)
                latest_price = prices[0]

                # Určení trendu
                if len(prices) >= 3:
                    recent_trend = prices[0] - prices[2]  # Porovnání s předpředchozí
                    if recent_trend > 0.1:
                        trend = "ROSTOUCÍ ↗"
                        trend_color = Colors.GREEN
                    elif recent_trend < -0.1:
                        trend = "KLESAJÍCÍ ↘"
                        trend_color = Colors.RED
                    else:
                        trend = "STABILNÍ →"
                        trend_color = Colors.BLUE
                else:
                    trend = "STABILNÍ →"
                    trend_color = Colors.BLUE

                print(f"\n{Colors.BOLD}📊 SHRNUTÍ PREDIKCÍ{Colors.ENDC}")
                print(f"• Trend predikcí: {trend_color}{trend}{Colors.ENDC}")
                print(f"• Rozsah predikcí: {Colors.YELLOW}{min_price:.4f} - {max_price:.4f} USDT{Colors.ENDC}")
                print(f"• Nejnovější predikce: {Colors.BLUE}{latest_price:.4f} USDT{Colors.ENDC}")

                # Volatilita predikcí
                if len(prices) > 1:
                    price_std = np.std(prices)
                    volatility_pct = (price_std / np.mean(prices)) * 100

                    if volatility_pct < 1:
                        volatility_desc = f"{Colors.GREEN}NÍZKÁ{Colors.ENDC}"
                    elif volatility_pct < 3:
                        volatility_desc = f"{Colors.YELLOW}STŘEDNÍ{Colors.ENDC}"
                    else:
                        volatility_desc = f"{Colors.RED}VYSOKÁ{Colors.ENDC}"

                    print(f"• Volatilita predikcí: {volatility_desc} ({volatility_pct:.2f}%)")

        except Exception as e:
            print(f"❌ Chyba při zobrazení shrnutí: {e}")
        finally:
            conn.close()

    def run_prediction_loop(self):
        """Hlavní smyčka pro kontinuální predikce."""
        print(f"🚀 Spouštím vylepšený predikční systém pro {SYMBOL}")
        print(f"📊 Predikční horizont: {PREDICTION_HORIZON} minut")
        print(f"🤖 Ensemble modely: XGBoost + Random Forest + Linear Regression")
        print("=" * 60)

        iteration = 0

        while True:
            try:
                iteration += 1
                print(f"\n{Colors.HEADER}🔄 ITERACE #{iteration} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Colors.ENDC}")

                # Aktualizace výsledků předchozích predikcí
                if iteration > 1:  # Přeskočíme první iteraci
                    self.update_prediction_results()

                # Načtení dat
                print("📥 Načítám data...")
                data = self.get_latest_data(limit=2000)
                if data is None or len(data) < 100:
                    print("❌ Nedostatek dat pro trénování")
                    time.sleep(60)
                    continue

                # Příprava features
                print("🔧 Připravuji features...")
                df_with_features = self.add_advanced_features(data.copy())
                X, y = self.prepare_data(df_with_features)

                if len(X) < 50:
                    print("❌ Nedostatek dat po přípravě")
                    time.sleep(60)
                    continue

                # Trénování modelů
                self.train_models(X, y)

                # Predikce
                current_price = data['close_price'].iloc[-1]
                predicted_price, individual_preds = self.predict(data)

                if predicted_price is None:
                    print("❌ Predikce selhala")
                    time.sleep(60)
                    continue

                # Výsledky
                price_change = predicted_price - current_price
                price_change_pct = (price_change / current_price) * 100

                print(f"\n{Colors.BOLD}📈 PREDIKCE PRO {SYMBOL}{Colors.ENDC}")
                print(f"⏰ Čas: {Colors.BLUE}{datetime.now().strftime('%H:%M:%S')}{Colors.ENDC}")
                print(f"💰 Aktuální cena: {Colors.YELLOW}{current_price:.4f} USDT{Colors.ENDC}")
                print(f"🔮 Predikce za {PREDICTION_HORIZON}min: {Colors.GREEN if price_change > 0 else Colors.RED}{predicted_price:.4f} USDT{Colors.ENDC}")
                print(f"📊 Změna: {Colors.GREEN if price_change > 0 else Colors.RED}{price_change:+.4f} USDT ({price_change_pct:+.2f}%){Colors.ENDC}")
                print(f"🎯 Individuální predikce:")
                for model, pred in individual_preds.items():
                    change = pred - current_price
                    change_pct = (change / current_price) * 100
                    color = Colors.GREEN if change > 0 else Colors.RED if change < 0 else Colors.BLUE
                    print(f"   {model.upper()}: {color}{pred:.4f} USDT ({change:+.4f}, {change_pct:+.2f}%){Colors.ENDC}")

                # Uložení predikce
                self.save_prediction(current_price, predicted_price, individual_preds)

                # Zobrazení tabulky s historií (každých 3 iterací nebo na začátku)
                if iteration == 1 or iteration % 3 == 0:
                    self.display_prediction_table()
                    self.display_prediction_summary()

                print(f"\n{Colors.BLUE}⏳ Čekám 30 sekund do další predikce...{Colors.ENDC}")
                print("=" * 80)
                time.sleep(30)

            except KeyboardInterrupt:
                print(f"\n{Colors.YELLOW}🛑 Ukončení na žádost uživatele{Colors.ENDC}")
                # Zobrazíme finální tabulku
                self.display_prediction_table()
                break
            except Exception as e:
                print(f"❌ Chyba v hlavní smyčce: {e}")
                time.sleep(60)

def main():
    """Hlavní funkce."""
    system = EnsemblePredictionSystem()
    system.run_prediction_loop()

if __name__ == "__main__":
    main()
