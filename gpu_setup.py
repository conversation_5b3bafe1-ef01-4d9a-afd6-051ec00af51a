"""
Skript pro kontrolu a nastavení GPU pro XGBoost.
Tento skript zkontroluje, zda je GPU dostupná a správně nakonfigurovaná pro XGBoost.
"""
import os
import sys
import subprocess
import logging

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_gpu_availability():
    """Zkontroluje, zda je GPU dostupná."""
    try:
        # Pokus o import torch a kontrolu GPU
        import torch
        if torch.cuda.is_available():
            logger.info(f"PyTorch detekoval GPU: {torch.cuda.get_device_name(0)}")
            logger.info(f"Počet dostupných GPU: {torch.cuda.device_count()}")
            logger.info(f"CUDA verze: {torch.version.cuda}")
            return True
        else:
            logger.warning("PyTorch nedetekoval žádnou GPU")
            return False
    except ImportError:
        logger.warning("PyTorch nen<PERSON> na<PERSON>, zkouš<PERSON><PERSON> jiné metody")
        
    try:
        # Pokus o import cupy
        import cupy
        logger.info(f"CuPy detekoval GPU, CUDA verze: {cupy.cuda.runtime.runtimeGetVersion()}")
        return True
    except ImportError:
        logger.warning("CuPy není nainstalován, zkouším jiné metody")
    
    # Kontrola pomocí nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        if result.returncode == 0:
            logger.info("nvidia-smi detekoval GPU")
            logger.info(result.stdout.split('\n')[0])
            return True
        else:
            logger.warning("nvidia-smi nedetekoval žádnou GPU")
            return False
    except:
        logger.warning("nvidia-smi není dostupný")
    
    return False

def check_xgboost_gpu_support():
    """Zkontroluje, zda XGBoost podporuje GPU."""
    try:
        import xgboost as xgb
        
        # Vytvoření testovacího modelu s GPU parametry
        try:
            import numpy as np
            
            # Vytvoření malého testovacího datasetu
            X = np.random.rand(10, 5)
            y = np.random.rand(10)
            
            # Vytvoření modelu s GPU parametry
            params = {
                'tree_method': 'gpu_hist',
                'gpu_id': 0
            }
            model = xgb.XGBRegressor(**params)
            
            # Pokus o fit
            model.fit(X, y, verbose=False)
            
            logger.info("XGBoost úspěšně použil GPU")
            return True
        except Exception as e:
            logger.warning(f"XGBoost nemůže použít GPU: {e}")
            return False
    except ImportError:
        logger.warning("XGBoost není nainstalován")
        return False

def install_required_packages():
    """Nainstaluje potřebné balíčky pro GPU podporu."""
    packages = [
        "xgboost>=1.4.0",
        "numpy",
        "scikit-learn",
        "pandas",
        "matplotlib"
    ]
    
    # Kontrola CUDA verze
    cuda_version = None
    try:
        import torch
        cuda_version = torch.version.cuda
        logger.info(f"Detekována CUDA verze: {cuda_version}")
    except:
        try:
            result = subprocess.run(['nvcc', '--version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'release' in line:
                        cuda_version = line.split('release ')[1].split(',')[0]
                        logger.info(f"Detekována CUDA verze: {cuda_version}")
                        break
        except:
            pass
    
    # Přidání GPU balíčků podle dostupné CUDA verze
    if cuda_version:
        major_version = cuda_version.split('.')[0]
        if major_version == '11':
            packages.append("cupy-cuda11x")
        elif major_version == '10':
            packages.append("cupy-cuda10x")
        else:
            logger.warning(f"Nepodporovaná CUDA verze: {cuda_version}")
            packages.append("cupy")
    else:
        logger.warning("Nelze detekovat CUDA verzi, zkusím obecnou instalaci")
        packages.append("cupy")
    
    # Instalace balíčků
    for package in packages:
        try:
            logger.info(f"Instaluji {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            logger.info(f"{package} úspěšně nainstalován")
        except subprocess.CalledProcessError as e:
            logger.error(f"Chyba při instalaci {package}: {e}")

def main():
    """Hlavní funkce pro kontrolu a nastavení GPU."""
    logger.info("Kontroluji dostupnost GPU...")
    gpu_available = check_gpu_availability()
    
    if gpu_available:
        logger.info("GPU je dostupná")
        
        logger.info("Kontroluji podporu GPU v XGBoost...")
        xgboost_gpu_support = check_xgboost_gpu_support()
        
        if xgboost_gpu_support:
            logger.info("XGBoost podporuje GPU, vše je připraveno")
        else:
            logger.warning("XGBoost nepodporuje GPU, pokusím se nainstalovat potřebné balíčky")
            install_required_packages()
            
            # Kontrola po instalaci
            logger.info("Kontroluji podporu GPU v XGBoost po instalaci...")
            xgboost_gpu_support = check_xgboost_gpu_support()
            
            if xgboost_gpu_support:
                logger.info("XGBoost nyní podporuje GPU, vše je připraveno")
            else:
                logger.error("XGBoost stále nepodporuje GPU, je potřeba ruční konfigurace")
                logger.info("Zkuste spustit: pip install xgboost --upgrade")
    else:
        logger.warning("GPU není dostupná nebo není správně nakonfigurovaná")
        logger.info("Zkontrolujte, zda máte nainstalované ovladače NVIDIA a CUDA")

if __name__ == "__main__":
    main()
