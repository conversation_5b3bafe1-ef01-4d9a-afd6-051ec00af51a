"""
Modul pro pokročilé technické indikátory zachycující trend trhu.
Obsahuje implementace různ<PERSON>ch indik<PERSON>torů pro detekci a měření trendu.
"""
import pandas as pd
import numpy as np
import pandas_ta as ta

def add_macd(df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9, 
             column: str = 'close_price') -> pd.DataFrame:
    """
    Přidá Moving Average Convergence Divergence (MACD) indikátor.
    
    Args:
        df: DataFrame s cenovými daty
        fast: Perioda pro rychlý EMA
        slow: Perioda pro pomalý EMA
        signal: Perioda pro signální linii
        column: Název sloupce s cenou
        
    Returns:
        DataFrame s přidanými MACD indikátory
    """
    try:
        # Vytvoření kopie DataFrame
        df = df.copy()
        
        # Výpočet MACD pomocí pandas_ta
        macd = ta.macd(df[column], fast=fast, slow=slow, signal=signal)
        
        # Přidání MACD sloupců do původního DataFrame
        df[f'MACD_line'] = macd['MACD_{fast}_{slow}_{signal}']
        df[f'MACD_signal'] = macd[f'MACDs_{fast}_{slow}_{signal}']
        df[f'MACD_hist'] = macd[f'MACDh_{fast}_{slow}_{signal}']
        
        # Přidání MACD směru (1 pro rostoucí, -1 pro klesající, 0 pro neutrální)
        df['MACD_direction'] = np.where(df[f'MACD_hist'] > df[f'MACD_hist'].shift(1), 1, 
                               np.where(df[f'MACD_hist'] < df[f'MACD_hist'].shift(1), -1, 0))
        
        return df
    except Exception as e:
        print(f"Chyba při výpočtu MACD: {e}")
        # Vrátíme původní DataFrame, pokud dojde k chybě
        return df

def add_adx(df: pd.DataFrame, length: int = 14, 
            high_col: str = 'high_price', 
            low_col: str = 'low_price', 
            close_col: str = 'close_price') -> pd.DataFrame:
    """
    Přidá Average Directional Index (ADX) pro měření síly trendu.
    
    Args:
        df: DataFrame s cenovými daty
        length: Perioda pro výpočet ADX
        high_col: Název sloupce s nejvyšší cenou
        low_col: Název sloupce s nejnižší cenou
        close_col: Název sloupce s uzavírací cenou
        
    Returns:
        DataFrame s přidanými ADX indikátory
    """
    try:
        # Vytvoření kopie DataFrame
        df = df.copy()
        
        # Výpočet ADX pomocí pandas_ta
        adx = ta.adx(high=df[high_col], low=df[low_col], close=df[close_col], length=length)
        
        # Přidání ADX sloupců do původního DataFrame
        df[f'ADX_{length}'] = adx[f'ADX_{length}']
        df[f'DI+_{length}'] = adx[f'DMP_{length}']  # Positive Directional Indicator
        df[f'DI-_{length}'] = adx[f'DMN_{length}']  # Negative Directional Indicator
        
        # Přidání indikátoru síly trendu (0-100)
        # ADX < 20: slabý trend, ADX > 25: silný trend, ADX > 40: velmi silný trend
        df['trend_strength'] = df[f'ADX_{length}']
        
        # Přidání směru trendu na základě DI+ a DI-
        df['trend_direction'] = np.where(df[f'DI+_{length}'] > df[f'DI-_{length}'], 1, -1)
        
        return df
    except Exception as e:
        print(f"Chyba při výpočtu ADX: {e}")
        # Vrátíme původní DataFrame, pokud dojde k chybě
        return df

def add_parabolic_sar(df: pd.DataFrame, 
                      acceleration: float = 0.02, 
                      maximum: float = 0.2,
                      high_col: str = 'high_price', 
                      low_col: str = 'low_price') -> pd.DataFrame:
    """
    Přidá Parabolic SAR (Stop And Reverse) indikátor pro identifikaci bodů obratu trendu.
    
    Args:
        df: DataFrame s cenovými daty
        acceleration: Faktor zrychlení
        maximum: Maximální hodnota faktoru zrychlení
        high_col: Název sloupce s nejvyšší cenou
        low_col: Název sloupce s nejnižší cenou
        
    Returns:
        DataFrame s přidaným Parabolic SAR indikátorem
    """
    try:
        # Vytvoření kopie DataFrame
        df = df.copy()
        
        # Výpočet Parabolic SAR pomocí pandas_ta
        psar = ta.psar(high=df[high_col], low=df[low_col], acceleration=acceleration, maximum=maximum)
        
        # Přidání PSAR sloupců do původního DataFrame
        df['PSAR'] = psar[f'PSARl_{acceleration}_{maximum}'].fillna(psar[f'PSARs_{acceleration}_{maximum}'])
        df['PSAR_direction'] = np.where(psar[f'PSARl_{acceleration}_{maximum}'].notnull(), 1, -1)
        
        return df
    except Exception as e:
        print(f"Chyba při výpočtu Parabolic SAR: {e}")
        # Vrátíme původní DataFrame, pokud dojde k chybě
        return df

def add_ichimoku(df: pd.DataFrame, 
                 tenkan: int = 9, 
                 kijun: int = 26, 
                 senkou_span_b: int = 52,
                 high_col: str = 'high_price', 
                 low_col: str = 'low_price', 
                 close_col: str = 'close_price') -> pd.DataFrame:
    """
    Přidá Ichimoku Cloud indikátor pro komplexní analýzu trendu.
    
    Args:
        df: DataFrame s cenovými daty
        tenkan: Perioda pro Tenkan-sen (konverzní linie)
        kijun: Perioda pro Kijun-sen (základní linie)
        senkou_span_b: Perioda pro Senkou Span B (druhá část cloudu)
        high_col: Název sloupce s nejvyšší cenou
        low_col: Název sloupce s nejnižší cenou
        close_col: Název sloupce s uzavírací cenou
        
    Returns:
        DataFrame s přidanými Ichimoku indikátory
    """
    try:
        # Vytvoření kopie DataFrame
        df = df.copy()
        
        # Výpočet Ichimoku Cloud pomocí pandas_ta
        ichimoku = ta.ichimoku(high=df[high_col], low=df[low_col], close=df[close_col], 
                              tenkan=tenkan, kijun=kijun, senkou=senkou_span_b)
        
        # Přidání Ichimoku sloupců do původního DataFrame
        df['tenkan_sen'] = ichimoku[f'ITS_{tenkan}']  # Tenkan-sen (konverzní linie)
        df['kijun_sen'] = ichimoku[f'IKS_{kijun}']    # Kijun-sen (základní linie)
        df['senkou_span_a'] = ichimoku[f'ISA_{tenkan}_{kijun}']  # Senkou Span A (první část cloudu)
        df['senkou_span_b'] = ichimoku[f'ISB_{senkou_span_b}']   # Senkou Span B (druhá část cloudu)
        df['chikou_span'] = ichimoku[f'ICS_{kijun}']  # Chikou Span (zpožděná cena)
        
        # Přidání indikátoru pozice ceny vůči cloudu
        # 1: nad cloudem (býčí), -1: pod cloudem (medvědí), 0: v cloudu (neutrální)
        df['cloud_position'] = np.where(df[close_col] > df['senkou_span_a'], 
                                np.where(df[close_col] > df['senkou_span_b'], 1, 0),
                                np.where(df[close_col] < df['senkou_span_b'], -1, 0))
        
        # Přidání indikátoru TK cross (Tenkan-sen vs Kijun-sen)
        # 1: Tenkan nad Kijun (býčí), -1: Tenkan pod Kijun (medvědí)
        df['tk_cross'] = np.where(df['tenkan_sen'] > df['kijun_sen'], 1, -1)
        
        return df
    except Exception as e:
        print(f"Chyba při výpočtu Ichimoku Cloud: {e}")
        # Vrátíme původní DataFrame, pokud dojde k chybě
        return df

def add_supertrend(df: pd.DataFrame, 
                  period: int = 10, 
                  multiplier: float = 3.0,
                  high_col: str = 'high_price', 
                  low_col: str = 'low_price', 
                  close_col: str = 'close_price') -> pd.DataFrame:
    """
    Přidá SuperTrend indikátor pro identifikaci trendu a potenciálních bodů obratu.
    
    Args:
        df: DataFrame s cenovými daty
        period: Perioda pro výpočet ATR
        multiplier: Násobitel pro výpočet pásem
        high_col: Název sloupce s nejvyšší cenou
        low_col: Název sloupce s nejnižší cenou
        close_col: Název sloupce s uzavírací cenou
        
    Returns:
        DataFrame s přidanými SuperTrend indikátory
    """
    try:
        # Vytvoření kopie DataFrame
        df = df.copy()
        
        # Výpočet SuperTrend pomocí pandas_ta
        supertrend = ta.supertrend(high=df[high_col], low=df[low_col], close=df[close_col], 
                                  length=period, multiplier=multiplier)
        
        # Přidání SuperTrend sloupců do původního DataFrame
        st_col = f'SUPERT_{period}_{multiplier}'
        df['supertrend'] = supertrend[st_col]
        df['supertrend_direction'] = supertrend[f'{st_col}_d']  # 1 pro býčí trend, -1 pro medvědí trend
        
        return df
    except Exception as e:
        print(f"Chyba při výpočtu SuperTrend: {e}")
        # Vrátíme původní DataFrame, pokud dojde k chybě
        return df

def add_trend_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Přidá všechny implementované trendové indikátory do DataFrame.
    
    Args:
        df: DataFrame s cenovými daty
        
    Returns:
        DataFrame s přidanými trendovými indikátory
    """
    print("Přidávám pokročilé trendové indikátory...")
    
    # Postupné přidání všech trendových indikátorů
    df = add_macd(df)
    df = add_adx(df)
    df = add_parabolic_sar(df)
    df = add_supertrend(df)
    
    # Ichimoku přidáme pouze pokud máme dostatek dat (potřebuje více historických dat)
    if len(df) > 60:  # Minimálně 60 řádků pro Ichimoku
        df = add_ichimoku(df)
    
    # Vytvoření souhrnného indikátoru trendu
    # Kombinuje signály z různých indikátorů pro robustnější detekci trendu
    try:
        trend_signals = []
        
        # MACD směr
        if 'MACD_direction' in df.columns:
            trend_signals.append(df['MACD_direction'])
        
        # ADX směr a síla
        if 'trend_direction' in df.columns and 'trend_strength' in df.columns:
            # Vážíme směr trendu jeho silou (normalizovanou na 0-1)
            adx_signal = df['trend_direction'] * (df['trend_strength'] / 100)
            trend_signals.append(adx_signal)
        
        # SuperTrend směr
        if 'supertrend_direction' in df.columns:
            trend_signals.append(df['supertrend_direction'])
        
        # PSAR směr
        if 'PSAR_direction' in df.columns:
            trend_signals.append(df['PSAR_direction'])
        
        # Ichimoku cloud pozice
        if 'cloud_position' in df.columns:
            trend_signals.append(df['cloud_position'])
        
        # Výpočet souhrnného indikátoru jako průměr všech signálů
        if trend_signals:
            df['trend_composite'] = sum(trend_signals) / len(trend_signals)
            
            # Klasifikace trendu: 1 (silný býčí), 0.5 (mírný býčí), 0 (neutrální), 
            # -0.5 (mírný medvědí), -1 (silný medvědí)
            df['trend_classification'] = pd.cut(
                df['trend_composite'], 
                bins=[-1.1, -0.6, -0.2, 0.2, 0.6, 1.1], 
                labels=[-1, -0.5, 0, 0.5, 1]
            ).astype(float)
    except Exception as e:
        print(f"Chyba při výpočtu souhrnného indikátoru trendu: {e}")
    
    print(f"Přidáno {len(df.columns) - len(df.columns)} nových trendových indikátorů")
    return df
