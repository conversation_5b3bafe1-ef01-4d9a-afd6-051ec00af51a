import pyodbc
import pandas as pd
import joblib
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import ConfusionMatrixDisplay
# --- nacteni modelu ---
model = joblib.load("alch_class_model.pkl")

# --- pripo<PERSON><PERSON> k databazi ---
conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"  # Například "*************\\SQLEXPRESS"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)
conn = pyodbc.connect(conn_str)

# --- nacteni poslednich 6 radku pro vypocet ---
query = """
SELECT TOP 100 * FROM alch_indikatory ORDER BY timestamp DESC
"""
df = pd.read_sql(query, conn)
conn.close()

# seradit vzestupne podle casu
df = df.sort_values("timestamp")

# --- dopocty vstupu (stejne jako ve treninku) ---
df['momentum'] = df['close_price'] - df['close_price'].shift(5)
df['price_change_pct'] = df['close_price'].pct_change()
df['rsi_slope'] = df['RSI'].diff()
df['ema_diff'] = df['EMA9'] - df['EMA20']

# --- definice vstupnich prvku ---
features = ['RSI', 'EMA9', 'EMA20', 'boll_high', 'boll_low', 'ATR',
            'momentum', 'price_change_pct', 'rsi_slope', 'ema_diff']

# odstran NaN radky
X = df[features].dropna()

if X.empty:
    print("❌ Nedostatek dat pro predikci.")
else:
    pred_prob = model.predict_proba(X)
    pred_class = np.argmax(pred_prob[-1])

    label_map = {0: "Pokles", 1: "Stagnace", 2: "Rust"}
    confidence = pred_prob[-1][pred_class] * 100

    print(f"\n🔮 Model predikuje: {label_map[pred_class]} ({confidence:.2f} % jistota)")
    print(f"Rozlozeni pravdepodobnosti: Pokles {pred_prob[-1][0]:.2%}, Stagnace {pred_prob[-1][1]:.2%}, Rust {pred_prob[-1][2]:.2%}")

    # --- vizualizace ---
    y_pred = np.argmax(pred_prob, axis=1)
    y_labels = [0, 1, 2]

    # graf predikovanych smeru
    plt.figure(figsize=(10, 4))
    plt.plot(y_pred, marker='o', label="Predikovane smery")
    plt.title("Predikovane smery v poslednich krocich")
    plt.yticks(ticks=[0,1,2], labels=["Pokles", "Stagnace", "Rust"])
    plt.xlabel("Index")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()

    # matice zamen
    # protoze nemame skutecne hodnoty (y_true), jen dummy pro ukazku:
    y_true_dummy = [1]*len(y_pred)
    ConfusionMatrixDisplay.from_predictions(y_true_dummy, y_pred, display_labels=["Pokles", "Stagnace", "Rust"])
    plt.title("Matice zaměn (dummy y_true)")
    plt.tight_layout()
    plt.show()