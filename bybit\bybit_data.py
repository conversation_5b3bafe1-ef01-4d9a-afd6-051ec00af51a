from pybit.unified_trading import HTTP
import pandas as pd
from config import API_KEY, API_SECRET, SYMBOL, INTERVAL, KLINE_LIMIT

def get_klines():
    session = HTTP(testnet=False, api_key=API_KEY, api_secret=API_SECRET)
    klines = session.get_kline(
        category="linear",
        symbol=SYMBOL,
        interval=INTERVAL,
        limit=KLINE_LIMIT
    )
    df = pd.DataFrame(klines['result']['list'])
    df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover']
    for col in ['open', 'high', 'low', 'close', 'volume', 'turnover']:
        df[col] = df[col].astype(float)
    df['timestamp'] = df['timestamp'].astype(int)
    # *** <PERSON><PERSON> zajisti správné pořadí ***
    df = df.sort_values('timestamp').reset_index(drop=True)
    return df
