"""
Skript pro vytvoření tabulky alch_predictions v databázi.
"""
import os
import logging
import pyodbc
from datetime import datetime

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def create_predictions_table(conn):
    """Vytvoří tabulku alch_predictions v databázi."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Kontrola, zda tabulka již existuje
        cursor.execute("""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'alch_predictions'
        """)

        if cursor.fetchone()[0] > 0:
            logger.info("Tabulka alch_predictions již existuje.")
            return True

        # Vytvoření tabulky
        cursor.execute("""
        CREATE TABLE dbo.alch_predictions (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            current_price FLOAT NOT NULL,
            predicted_price FLOAT NOT NULL,
            actual_price FLOAT NULL,
            error_pct FLOAT NULL,
            market_condition VARCHAR(50) NULL
        )
        """)

        # Vytvoření indexů pro rychlejší vyhledávání
        cursor.execute("""
        CREATE INDEX idx_predictions_timestamp ON dbo.alch_predictions (timestamp)
        """)

        conn.commit()
        logger.info("Tabulka alch_predictions byla úspěšně vytvořena.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při vytváření tabulky alch_predictions: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def insert_test_prediction(conn):
    """Vloží testovací predikci do tabulky."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Aktuální čas
        timestamp = datetime.now()

        # Testovací data
        current_price = 0.15
        predicted_price = 0.152
        market_condition = "stable_bullish"

        # Vložení predikce
        cursor.execute("""
        INSERT INTO dbo.alch_predictions (timestamp, current_price, predicted_price, market_condition)
        VALUES (?, ?, ?, ?)
        """, (timestamp, current_price, predicted_price, market_condition))

        conn.commit()
        logger.info("Testovací predikce byla úspěšně vložena.")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při vkládání testovací predikce: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def main():
    """Hlavní funkce."""
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi.")
        return

    try:
        # Vytvoření tabulky
        if create_predictions_table(conn):
            # Vložení testovací predikce
            insert_test_prediction(conn)

            # Výpis informací o tabulce
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM dbo.alch_predictions")
            count = cursor.fetchone()[0]
            logger.info(f"Počet predikcí v tabulce: {count}")

            # Výpis posledních 5 predikcí
            cursor.execute("""
            SELECT TOP 5 id, timestamp, current_price, predicted_price, actual_price, error_pct, market_condition
            FROM dbo.alch_predictions
            ORDER BY timestamp DESC
            """)

            rows = cursor.fetchall()
            if rows:
                logger.info("Poslední predikce:")
                for row in rows:
                    logger.info(f"  ID: {row[0]}, Čas: {row[1]}, Aktuální: {row[2]}, Predikce: {row[3]}, Skutečná: {row[4]}, Chyba: {row[5]}%, Podmínky: {row[6]}")

            cursor.close()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
