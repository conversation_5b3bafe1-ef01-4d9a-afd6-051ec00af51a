import pyodbc
import pandas as pd
import joblib
from datetime import datetime

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.0.100,1433;"  # Například "192.168.1.100\\SQLEXPRESS"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)
conn = pyodbc.connect(conn_str)

# Zjištěn<PERSON> aktu<PERSON> (poslední) ceny pro porovnání
price_query = """
SELECT TOP 1 close_price
FROM alch_price_history
ORDER BY timestamp DESC
"""
current_price_df = pd.read_sql(price_query, conn)
current_price = current_price_df['close_price'].iloc[0] if not current_price_df.empty else "N/A"

# Oprava SQL dotazu - přidána mezera po "top 1"
query = """
SELECT TOP 1 timestamp, RSI, EMA9, EMA20, boll_high, boll_low, ATR
FROM alch_indikatory
ORDER BY timestamp DESC
"""
df = pd.read_sql(query, conn)
conn.close()

# Vypsat aktuální indikátory pro diagnostiku
print(f"=== Diagnostické informace ({datetime.now()}) ===")
print(f"Aktuální cena: {current_price}")
print("Použité hodnoty indikátorů pro predikci:")
for column in df.columns:
    if column != 'timestamp':
        print(f"  {column}: {df[column].iloc[0]}")
        
timestamp = df['timestamp'].iloc[0] if not df.empty else "N/A"
print(f"Čas posledního záznamu indikátorů: {timestamp}")

# Načtení modelu
try:
    model = joblib.load("alch_price_model.pkl")
    print("Model úspěšně načten")

    # Data pro predikci
    nove_data = df[["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]]

    # Predikce
    predikovana_cena = model.predict(nove_data)
    
    print(f"\n📊 Aktuální cena: {current_price}")
    print(f"🔮 Predikovaná cena za 5 minut: {predikovana_cena[0]}")
    print(f"🔄 Rozdíl: {predikovana_cena[0] - current_price} ({((predikovana_cena[0] - current_price) / current_price * 100):.2f}%)")
    
    if predikovana_cena[0] > current_price:
        print("📈 SIGNÁL: Očekáván růst ceny")
    elif predikovana_cena[0] < current_price:
        print("📉 SIGNÁL: Očekáván pokles ceny") 
    else:
        print("🔄 SIGNÁL: Bez změny")

except Exception as e:
    print(f"❌ Chyba při načítání modelu nebo predikci: {e}")
