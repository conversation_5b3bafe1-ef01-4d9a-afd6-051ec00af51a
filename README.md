# Vylepšený predikční systém pro kryptoměny

Tento systém poskytuje pokročilé nástroje pro predikci cen kryptoměn s využitím strojového učení, technick<PERSON><PERSON> indik<PERSON>ů, analýzy sentimentu a adaptivního učení.

## Hlavn<PERSON> funkce

- **Pokročilé technické indikátory** - MACD, ADX, Parabolic SAR, Ichimoku Cloud, SuperTrend
- **Adaptivní učení** - průběžné dolaďování modelu podle výkonu
- **Modely pro různé tržní podmínky** - specializované modely pro volatilní/stabilní trh
- **Analýza sentimentu** - interní a externí indikátory sentimentu trhu
- **Ensemble modely** - kombinace různých modelů pro lepší predikce
- **Backtesting** - testování strategií na historických datech
- **Dashboard** - vizualizace výsledků a metrik výkonu
- **GPU akcelerace** - využití GPU pro rychlejší trénování a predikci

## Struktura systému

- `combined_script.py` - hlavní skript pro predikci cen
- `trend_indicators.py` - implementace pokročilých technických indikátorů
- `adaptive_learning.py` - adaptivní učení a průběžné dolaďování modelu
- `market_conditions.py` - detekce tržních podmínek a výběr vhodného modelu
- `sentiment_analysis.py` - analýza sentimentu trhu
- `ensemble_models.py` - implementace ensemble modelů
- `external_sentiment.py` - získávání externích dat o sentimentu
- `backtest.py` - základní framework pro backtesting
- `backtest_strategies.py` - implementace různých obchodních strategií
- `dashboard.py` - vizualizace výsledků a metrik výkonu
- `update_predictions.py` - aktualizace predikcí a porovnání s reálnými výsledky
- `integration.py` - integrační skript pro spojení všech funkcí
- `run_system.py` - hlavní vstupní bod pro spuštění systému
- `gpu_diagnostics.py` - diagnostika GPU a CUDA konfigurace

## Požadavky

- Python 3.8+
- PyTorch
- XGBoost 2.0+
- pandas, numpy, matplotlib
- scikit-learn
- pyodbc
- CUDA Toolkit (pro GPU akceleraci)

## Instalace

```bash
# Instalace základních knihoven
pip install pandas numpy matplotlib scikit-learn pyodbc requests

# Instalace PyTorch s GPU podporou
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# Instalace XGBoost s GPU podporou
pip install xgboost>=2.0.0
```

## Použití

### Spuštění predikce

```bash
python run_system.py predict
```

### Trénování modelu

```bash
python run_system.py train
```

### Backtesting

```bash
python run_system.py backtest --days 30
```

### Spuštění dashboardu

```bash
python run_system.py dashboard --days 7
```

### Aktualizace predikcí a metrik

```bash
python run_system.py update
```

### Spuštění všech akcí

```bash
python run_system.py all
```

### Další možnosti

- `--ensemble` - použití ensemble modelů
- `--use_sentiment` - zahrnutí sentimentu do predikce
- `--update_sentiment` - aktualizace dat o sentimentu

## Konfigurace GPU

Pro kontrolu a nastavení GPU:

```bash
python gpu_diagnostics.py
```

## Databázová struktura

Systém používá následující tabulky v databázi:

- `alch_price_history` - historická cenová data
- `alch_indikatory` - technické indikátory
- `alch_predictions` - predikce a jejich vyhodnocení
- `EXTERNAL_SENTIMENT` - externí data o sentimentu
- `MODEL_PERFORMANCE_LOG` - metriky výkonu modelu

## Příklady použití

### Predikce s využitím sentimentu

```bash
python run_system.py predict --use_sentiment --update_sentiment
```

### Trénování ensemble modelu

```bash
python run_system.py train --ensemble
```

### Backtesting s ensemble modelem a sentimentem

```bash
python run_system.py backtest --days 30 --ensemble --use_sentiment
```

## Rozšíření a úpravy

Systém je navržen modulárně, takže je snadné přidat nové funkce nebo upravit stávající:

- Přidání nových technických indikátorů do `trend_indicators.py`
- Implementace nových strategií v `backtest_strategies.py`
- Přidání nových zdrojů dat o sentimentu do `external_sentiment.py`
- Úprava parametrů modelů v `adaptive_learning.py`

## Licence

Tento projekt je licencován pod MIT licencí.
