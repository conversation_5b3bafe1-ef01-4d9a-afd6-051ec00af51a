# db.py
# Prace s MSSQL: v<PERSON><PERSON><PERSON> tabulek, uk<PERSON><PERSON> a nacitani dat

import pyodbc
import pandas as pd
from config import CONN_STR

def create_tables():
    conn = pyodbc.connect(CONN_STR)
    cursor = conn.cursor()
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sol_klines' AND xtype='U')
        CREATE TABLE sol_klines (
            id INT IDENTITY(1,1) PRIMARY KEY,
            [timestamp] BIGINT NOT NULL,
            [open] FLOAT,
            [high] FLOAT,
            [low] FLOAT,
            [close] FLOAT,
            [volume] FLOAT,
            [turnover] FLOAT
        )
    """)
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sol_predictions' AND xtype='U')
        CREATE TABLE sol_predictions (
            id INT IDENTITY(1,1) PRIMARY KEY,
            [timestamp_predikce] BIGINT NOT NULL,
            [future_time] INT NOT NULL,
            [future_lstm] FLOAT,
            [future_chaos] FLOAT,
            [real_price] FLOAT NULL
        )
    """)
    conn.commit()
    cursor.close()
    conn.close()

def insert_klines(df):
    conn = pyodbc.connect(CONN_STR)
    cursor = conn.cursor()
    for _, row in df.iterrows():
        cursor.execute("""
            IF NOT EXISTS (SELECT 1 FROM sol_klines WHERE [timestamp]=?)
            INSERT INTO sol_klines ([timestamp], [open], [high], [low], [close], [volume], [turnover])
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, row['timestamp'], row['timestamp'], row['open'], row['high'], row['low'], row['close'], row['volume'], row['turnover'])
    conn.commit()
    cursor.close()
    conn.close()

def fetch_klines(limit=60):
    conn = pyodbc.connect(CONN_STR)
    # Nejaktualnejsi data spravne serazene pro graf zleva doprava
    query = f"""
        SELECT * FROM (
            SELECT TOP {limit} * FROM sol_klines ORDER BY [timestamp] DESC
        ) AS sub
        ORDER BY [timestamp] ASC
    """
    df = pd.read_sql(query, conn)
    conn.close()
    return df

def insert_predictions(timestamp_predikce, lstm_pred, chaos_pred):
    conn = pyodbc.connect(CONN_STR)
    cursor = conn.cursor()
    for i in range(len(lstm_pred)):
        cursor.execute("""
            INSERT INTO sol_predictions ([timestamp_predikce], [future_time], [future_lstm], [future_chaos], [real_price])
            VALUES (?, ?, ?, ?, NULL)
        """, timestamp_predikce, i+1, float(lstm_pred[i]), float(chaos_pred[i]))
    conn.commit()
    cursor.close()
    conn.close()

def update_real_prices():
    conn = pyodbc.connect(CONN_STR)
    cursor = conn.cursor()
    cursor.execute("""
        UPDATE sol_predictions
        SET [real_price] = kl.[close]
        FROM sol_predictions p
        JOIN sol_klines kl
            ON kl.[timestamp] = p.[timestamp_predikce] + (p.[future_time] * 60 * 1000)
        WHERE p.[real_price] IS NULL
    """)
    conn.commit()
    cursor.close()
    conn.close()
