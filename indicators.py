import pandas as pd
import numpy as np

def add_sma(df: pd.DataFrame, length: int, column: str = 'close') -> pd.DataFrame:
    """Add Simple Moving Average to dataframe."""
    df[f'SMA_{length}'] = df[column].rolling(window=length).mean()
    return df

def add_rsi(df: pd.DataFrame, length: int = 14, column: str = 'close') -> pd.DataFrame:
    """Add Relative Strength Index to dataframe."""
    delta = df[column].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=length).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=length).mean()
    
    rs = gain / loss
    df[f'RSI_{length}'] = 100 - (100 / (1 + rs))
    return df
