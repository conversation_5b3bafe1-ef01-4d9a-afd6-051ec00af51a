"""
Jednoduchý skript pro vykreslení grafu predikce vs. skutečnost.
"""
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pyodbc
from datetime import datetime, timedelta

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def update_actual_prices(conn):
    """Aktualizuje skutečné ceny v tabulce predikcí."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Načtení predikcí bez skutečných cen
        cursor.execute("""
        SELECT id, timestamp, predicted_price
        FROM dbo.alch_predictions
        WHERE actual_price IS NULL
        """)

        predictions = cursor.fetchall()

        if not predictions:
            logger.info("Žádné predikce k aktualizaci")
            return True

        logger.info(f"Nalezeno {len(predictions)} predikcí k aktualizaci")

        # Aktualizace skutečných cen
        for pred_id, pred_time, pred_price in predictions:
            # Výpočet času pro skutečnou cenu (5 minut po predikci)
            # Konverze pred_time na datetime, pokud je to řetězec
            if isinstance(pred_time, str):
                try:
                    # Zkusíme různé formáty data
                    try:
                        pred_time = datetime.fromisoformat(pred_time.replace(' ', 'T'))
                    except ValueError:
                        # Pokud selhal isoformat, zkusíme jiný formát
                        # SQL Server může mít různé formáty data
                        try:
                            pred_time = datetime.strptime(pred_time, '%Y-%m-%d %H:%M:%S.%f')
                        except ValueError:
                            try:
                                pred_time = datetime.strptime(pred_time, '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                # Pokud má čas více než 6 desetinných míst, oříneme je
                                parts = pred_time.split('.')
                                if len(parts) == 2 and len(parts[1]) > 6:
                                    pred_time = parts[0] + '.' + parts[1][:6]
                                    pred_time = datetime.strptime(pred_time, '%Y-%m-%d %H:%M:%S.%f')
                                else:
                                    raise
                except ValueError:
                    # Pokud selhaly všechny pokusy, použijeme aktuální čas
                    logger.warning(f"Nelze převést čas '{pred_time}' na datetime, používám aktuální čas")
                    pred_time = datetime.now()
            # Časová značka v tabulce je již posunuta o 5 minut dopředu, takže actual_time = pred_time
            actual_time = pred_time

            # Načtení skutečné ceny
            # Nejprve zkusíme najít cenu přesně 5 minut po predikci
            cursor.execute("""
            SELECT TOP 1 close_price
            FROM dbo.alch_indikatory
            WHERE timestamp >= ? AND timestamp <= DATEADD(minute, 1, ?)
            ORDER BY ABS(DATEDIFF(second, timestamp, ?))
            """, (actual_time, actual_time, actual_time))

            actual_price_row = cursor.fetchone()

            # Pokud není k dispozici cena přesně 5 minut po predikci, použijeme nejnovější dostupnou cenu
            if not actual_price_row:
                cursor.execute("""
                SELECT TOP 1 close_price
                FROM dbo.alch_indikatory
                ORDER BY timestamp DESC
                """)

            actual_price_row = cursor.fetchone()

            if actual_price_row:
                actual_price = actual_price_row[0]

                # Výpočet chyby
                error_pct = ((pred_price - actual_price) / actual_price) * 100

                # Aktualizace záznamu
                cursor.execute("""
                UPDATE dbo.alch_predictions
                SET actual_price = ?, error_pct = ?
                WHERE id = ?
                """, (actual_price, error_pct, pred_id))

                logger.info(f"Aktualizována predikce {pred_id}: skutečná cena = {actual_price}, chyba = {error_pct:.2f}%")

        conn.commit()
        logger.info("Skutečné ceny úspěšně aktualizovány")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při aktualizaci skutečných cen: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def plot_prediction_graph(days=1):
    """
    Vykreslí graf predikce vs. skutečnost.

    Args:
        days: Počet dní historie
    """
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return

    # Aktualizace skutečných cen
    update_actual_prices(conn)

    cursor = None
    try:
        cursor = conn.cursor()

        # Výpočet data
        start_date = datetime.now() - timedelta(days=days)

        # Načtení predikcí
        cursor.execute("""
        SELECT timestamp, current_price, predicted_price, actual_price
        FROM dbo.alch_predictions
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))

        prediction_rows = cursor.fetchall()

        # Diagnostika dat
        print(f"Počet predikcí: {len(prediction_rows)}")
        if prediction_rows:
            print(f"První predikce: {prediction_rows[0]}")

        # Vytvoření jednoduchého grafu
        plt.figure(figsize=(12, 6))

        if prediction_rows:
            # Vytvoření seznamů pro osy x a y
            timestamps = []
            current_prices = []
            predicted_prices = []
            predicted_timestamps = []  # Nový seznam pro časové značky predikcí (o 5 minut později)
            actual_prices = []

            for row in prediction_rows:
                # Konverze časové značky na datetime, pokud je to řetězec
                timestamp = row[0]
                if isinstance(timestamp, str):
                    try:
                        # Zkusíme různé formáty data
                        try:
                            timestamp = datetime.fromisoformat(timestamp.replace(' ', 'T'))
                        except ValueError:
                            # Pokud selhal isoformat, zkusíme jiný formát
                            # SQL Server může mít různé formáty data
                            try:
                                timestamp = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S.%f')
                            except ValueError:
                                try:
                                    timestamp = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                                except ValueError:
                                    # Pokud má čas více než 6 desetinných míst, oříneme je
                                    parts = timestamp.split('.')
                                    if len(parts) == 2 and len(parts[1]) > 6:
                                        timestamp = parts[0] + '.' + parts[1][:6]
                                        timestamp = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S.%f')
                                    else:
                                        raise
                    except ValueError:
                        # Pokud selhaly všechny pokusy, použijeme aktuální čas
                        logger.warning(f"Nelze převést čas '{timestamp}' na datetime, používám aktuální čas")
                        timestamp = datetime.now()

                # Časová značka v tabulce je již posunuta o 5 minut dopředu
                # Timestamp je čas predikce (již posunutý o 5 minut dopředu)
                # Pro zobrazení aktuální ceny potřebujeme čas o 5 minut zpět
                current_price_timestamp = timestamp - timedelta(minutes=5)

                timestamps.append(current_price_timestamp)  # Čas aktuální ceny
                predicted_timestamps.append(timestamp)  # Čas predikce (již posunutý o 5 minut dopředu)
                current_prices.append(row[1])
                predicted_prices.append(row[2])
                if row[3] is not None:
                    actual_prices.append(row[3])
                else:
                    actual_prices.append(None)

            # Vykreslení grafu
            plt.plot(timestamps, current_prices, 'b-', marker='o', label='Aktuální cena')
            plt.plot(predicted_timestamps, predicted_prices, 'r--', marker='x', label='Predikovaná cena (za 5 minut)')

            # Vykreslení skutečných cen (pouze pro body, kde jsou k dispozici)
            valid_timestamps = []
            valid_actual_prices = []

            for i in range(len(timestamps)):
                if actual_prices[i] is not None:
                    # Skutečná cena je ve stejném čase jako predikce
                    valid_timestamps.append(predicted_timestamps[i])
                    valid_actual_prices.append(actual_prices[i])

            if valid_timestamps:
                plt.plot(valid_timestamps, valid_actual_prices, 'g-', marker='s', label='Skutečná cena (za 5 minut)')

            plt.title(f'Predikce vs. Skutečnost (posledních {days} dní)')
            plt.xlabel('Datum a čas')
            plt.ylabel('Cena')
            plt.grid(True)
            plt.legend()

            # Formátování osy x
            plt.gcf().autofmt_xdate()

            logger.info(f"Vykresleno {len(timestamps)} predikcí")
        else:
            plt.text(0.5, 0.5, 'Žádné predikce k dispozici',
                     horizontalalignment='center', verticalalignment='center',
                     transform=plt.gca().transAxes, fontsize=14)
            logger.warning("Žádné predikce k dispozici")

        plt.tight_layout()

        # Uložení grafu
        graph_path = os.path.join(os.getcwd(), 'prediction_graph.png')
        plt.savefig(graph_path)
        logger.info(f"Graf predikce vs. skutečnost uložen jako: {graph_path}")
        print(f"Graf uložen do: {graph_path}")
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu predikce vs. skutečnost: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    plot_prediction_graph()
