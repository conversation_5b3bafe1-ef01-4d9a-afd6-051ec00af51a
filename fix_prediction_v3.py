"""
Vylepšený skript pro predikci ceny s omezením extrémních predikcí a GPU akcelerací.
"""
import os
import logging
import pandas as pd
import numpy as np
import joblib
import xgboost as xgb
import pyodbc
import requests
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import sys
import pytz

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

# Nastavení API
BYBIT_API_URL = "https://api.bybit.com"
SYMBOL = "SOLUSDT"  # Symbol kryptoměny
INTERVAL = "1"  # Interval (1 minuta)

# Nasta<PERSON><PERSON> časov<PERSON>ch zón
UTC = pytz.UTC
LOCAL_TZ = pytz.timezone('Europe/Prague')  # Česk<PERSON> časová zóna (UTC+1/UTC+2)

def convert_utc_to_local(utc_dt):
    """Konvertuje UTC čas na lokální čas."""
    if utc_dt.tzinfo is None:
        utc_dt = UTC.localize(utc_dt)
    return utc_dt.astimezone(LOCAL_TZ)

def get_current_time_utc():
    """Vrátí aktuální čas v UTC."""
    return datetime.now(UTC)

def get_current_time_local():
    """Vrátí aktuální čas v lokální časové zóně."""
    return datetime.now(LOCAL_TZ)

def create_database_and_tables():
    """Vytvoří databázi a příslušné tabulky, pokud neexistují."""
    try:
        # Připojení k master databázi pro vytvoření nové databáze
        master_conn_str = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=master;Trusted_Connection=yes;"
        master_conn = pyodbc.connect(master_conn_str)
        master_cursor = master_conn.cursor()

        # Kontrola, zda databáze existuje
        master_cursor.execute("SELECT name FROM sys.databases WHERE name = 'byb'")
        db_exists = master_cursor.fetchone()

        if not db_exists:
            logger.info("Databáze 'byb' neexistuje, vytvářím ji...")
            master_cursor.execute("CREATE DATABASE byb")
            master_conn.commit()
            logger.info("Databáze 'byb' byla úspěšně vytvořena")
        else:
            logger.info("Databáze 'byb' již existuje")

        master_cursor.close()
        master_conn.close()

        # Připojení k nově vytvořené databázi
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        # Vytvoření tabulky alch_indikatory
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='alch_indikatory' AND xtype='U')
        CREATE TABLE dbo.alch_indikatory (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            close_price DECIMAL(18,8) NOT NULL,
            RSI DECIMAL(18,8),
            EMA9 DECIMAL(18,8),
            EMA20 DECIMAL(18,8),
            boll_high DECIMAL(18,8),
            boll_low DECIMAL(18,8),
            ATR DECIMAL(18,8),
            UNIQUE(timestamp)
        )
        """)

        # Vytvoření tabulky alch_predictions
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='alch_predictions' AND xtype='U')
        CREATE TABLE dbo.alch_predictions (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            current_price DECIMAL(18,8) NOT NULL,
            predicted_price DECIMAL(18,8) NOT NULL,
            actual_price DECIMAL(18,8),
            market_condition NVARCHAR(50),
            created_at DATETIME2 DEFAULT GETDATE()
        )
        """)

        # Vytvoření tabulky alch_price_history
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='alch_price_history' AND xtype='U')
        CREATE TABLE dbo.alch_price_history (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 NOT NULL,
            open_price DECIMAL(18,8),
            high_price DECIMAL(18,8),
            low_price DECIMAL(18,8),
            close_price DECIMAL(18,8) NOT NULL,
            volume DECIMAL(18,8),
            UNIQUE(timestamp)
        )
        """)

        # Vytvoření tabulky MODEL_PERFORMANCE_LOG
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MODEL_PERFORMANCE_LOG' AND xtype='U')
        CREATE TABLE dbo.MODEL_PERFORMANCE_LOG (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 DEFAULT GETDATE(),
            model_name NVARCHAR(100) NOT NULL,
            market_condition NVARCHAR(50),
            accuracy DECIMAL(5,4),
            mae DECIMAL(18,8),
            rmse DECIMAL(18,8),
            prediction_count INT DEFAULT 0
        )
        """)

        # Vytvoření tabulky EXTERNAL_SENTIMENT
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EXTERNAL_SENTIMENT' AND xtype='U')
        CREATE TABLE dbo.EXTERNAL_SENTIMENT (
            id INT IDENTITY(1,1) PRIMARY KEY,
            timestamp DATETIME2 DEFAULT GETDATE(),
            source NVARCHAR(255),
            sentiment_score DECIMAL(5,4),
            confidence DECIMAL(5,4),
            raw_data NVARCHAR(MAX)
        )
        """)

        conn.commit()
        cursor.close()
        conn.close()

        logger.info("Všechny potřebné tabulky byly zkontrolovány/vytvořeny")
        return True

    except Exception as e:
        logger.error(f"Chyba při vytváření databáze a tabulek: {e}")
        return False

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        # Pokus o vytvoření databáze a tabulek
        logger.info("Pokusím se vytvořit databázi a tabulky...")
        if create_database_and_tables():
            # Druhý pokus o připojení
            try:
                conn = pyodbc.connect(CONN_STR)
                logger.info("Připojení k databázi úspěšné po vytvoření")
                return conn
            except Exception as e2:
                logger.error(f"Chyba při druhém pokusu o připojení: {e2}")
                return None
        else:
            return None

def load_model(model_path):
    """Načte model ze souboru."""
    try:
        # Načtení modelu
        model = joblib.load(model_path)
        logger.info(f"Model úspěšně načten z {model_path}")

        # Pokud je model XGBoost, nastavíme zařízení na CPU
        # Toto zabrání varování o nesouladu zařízení
        if hasattr(model, 'get_params'):
            params = model.get_params()
            if 'device' in params:
                # Nastavení zařízení na CPU
                model.set_params(device='cpu')
                logger.info("Zařízení modelu nastaveno na CPU")

        return model
    except Exception as e:
        logger.error(f"Chyba při načítání modelu: {e}")
        return None

def get_bybit_ticker_data(symbol=SYMBOL):
    """
    Získá aktuální ticker data z Bybit API.

    Args:
        symbol: Symbol kryptoměny

    Returns:
        DataFrame s aktuálními daty
    """
    try:
        # Endpoint pro ticker data
        endpoint = f"/v5/market/tickers"

        # Parametry požadavku
        params = {
            'category': 'linear',
            'symbol': symbol
        }

        # Odeslání požadavku
        url = BYBIT_API_URL + endpoint
        logger.info(f"Vysílám požadavek na ticker URL: {url} s parametry: {params}")
        response = requests.get(url, params=params)

        # Kontrola odpovědi
        if response.status_code != 200:
            logger.error(f"Chyba při získávání ticker dat z Bybit API: {response.text}")
            return None

        # Zpracování odpovědi
        data = response.json()

        # Kontrola, zda jsou data v odpovědi
        if data['retCode'] != 0 or 'result' not in data or 'list' not in data['result']:
            logger.error(f"Chyba v odpovědi Bybit API: {data}")
            return None

        # Extrakce dat
        ticker_data = data['result']['list'][0]

        # Vytvoření DataFrame s jedním řádkem
        current_time_utc = get_current_time_utc()
        current_time_local = convert_utc_to_local(current_time_utc)

        df = pd.DataFrame([{
            'timestamp': current_time_local,
            'timestamp_utc': current_time_utc,
            'symbol': ticker_data['symbol'],
            'close_price': float(ticker_data['lastPrice']),
            'high_price_24h': float(ticker_data['highPrice24h']),
            'low_price_24h': float(ticker_data['lowPrice24h']),
            'volume_24h': float(ticker_data['volume24h']),
            'turnover_24h': float(ticker_data['turnover24h']),
            'price_change_percent_24h': float(ticker_data['price24hPcnt']) * 100
        }])

        logger.info(f"Aktuální čas UTC: {current_time_utc}, lokální čas: {current_time_local}")

        logger.info(f"Získána aktuální ticker data z Bybit API: {ticker_data['symbol']} = {ticker_data['lastPrice']}")
        return df
    except Exception as e:
        logger.error(f"Chyba při získávání ticker dat z Bybit API: {e}")
        return None

def get_bybit_kline_data(symbol=SYMBOL, interval=INTERVAL, limit=30):
    """
    Získá data z Bybit API - kline (svíčkové grafy).

    Args:
        symbol: Symbol kryptoměny
        interval: Interval (1, 3, 5, 15, 30, 60, 120, 240, 360, 720, D, W, M)
        limit: Počet záznamů (max 200)

    Returns:
        DataFrame s daty
    """
    try:
        # Endpoint pro kline data
        endpoint = f"/v5/market/kline"

        # Parametry požadavku
        params = {
            'category': 'linear',
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        # Odeslání požadavku
        url = BYBIT_API_URL + endpoint
        logger.info(f"Vysílám požadavek na URL: {url} s parametry: {params}")
        response = requests.get(url, params=params)

        # Kontrola odpovědi
        if response.status_code != 200:
            logger.error(f"Chyba při získávání dat z Bybit API: {response.text}")
            return None

        # Zpracování odpovědi
        data = response.json()

        # Kontrola, zda jsou data v odpovědi
        if data['retCode'] != 0 or 'result' not in data or 'list' not in data['result']:
            logger.error(f"Chyba v odpovědi Bybit API: {data}")
            return None

        # Extrakce dat
        kline_data = data['result']['list']

        # Vytvoření DataFrame
        df = pd.DataFrame(kline_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'])

        # Konverze typů
        # Konverze timestamp (string) na datetime
        df['timestamp_utc'] = pd.to_datetime(df['timestamp'].astype(float), unit='ms')

        # Konverze z UTC na lokální čas
        df['timestamp'] = df['timestamp_utc'].apply(convert_utc_to_local)

        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)

        logger.info(f"Konverze časových značek: první záznam UTC: {df['timestamp_utc'].iloc[0]}, lokální: {df['timestamp'].iloc[0]}")

        # Přejmenování sloupců
        df = df.rename(columns={'close': 'close_price'})

        # Seřazení podle času (od nejnovějšího)
        df = df.sort_values('timestamp', ascending=False)

        # Výpis nejnovějšího záznamu
        if not df.empty:
            latest_row = df.iloc[0]
            logger.info(f"Nejnovější záznam z Bybit API: čas={latest_row['timestamp']}, cena={latest_row['close_price']}")

        logger.info(f"Získáno {len(df)} záznamů z Bybit API")
        return df
    except Exception as e:
        logger.error(f"Chyba při získávání dat z Bybit API: {e}")
        return None

def load_recent_data(conn, minutes=10, max_rows=5, use_api=True):
    """Načte nedávná data z databáze nebo API."""
    if use_api:
        # Získání dat z API
        ticker_df = get_bybit_ticker_data()
        kline_df = get_bybit_kline_data(limit=30)  # Omezení na 30 záznamů

        if ticker_df is None or kline_df is None or kline_df.empty:
            logger.error("Nelze získat data z Bybit API, zkusím použít databázi")
            use_api = False
        else:
            # Použití ticker ceny pro nejnovější záznam
            current_price = ticker_df.iloc[0]['close_price']
            logger.info(f"Aktuální cena podle ticker dat: {current_price}")

            # Aktualizace nejnovějšího záznamu v kline_df
            if not kline_df.empty:
                kline_df.loc[0, 'close_price'] = current_price

            # Vrácení dat z API
            return kline_df

    # Pokud nepoužíváme API nebo API selhalo, použijeme databázi
    if not use_api:
        cursor = None
        try:
            cursor = conn.cursor()

            # Výpočet data
            start_date = datetime.now() - timedelta(minutes=minutes)

            # Načtení indikátorů
            cursor.execute("""
            SELECT TOP (?) * FROM dbo.alch_indikatory
            WHERE timestamp >= ?
            ORDER BY timestamp DESC
            """, (max_rows, start_date))

            rows = cursor.fetchall()

            if not rows:
                logger.warning(f"Nenalezeny žádné indikátory za posledních {minutes} minut")
                return None

            # Vytvoření DataFrame
            columns = [column[0] for column in cursor.description]
            df = pd.DataFrame.from_records(rows, columns=columns)

            # Konverze datumů
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            # Výpis nejnovějšího záznamu
            if not df.empty:
                latest_row = df.iloc[0]
                logger.info(f"Nejnovější záznam z databáze: čas={latest_row['timestamp']}, cena={latest_row['close_price']}")

            logger.info(f"Načteno {len(df)} indikátorů z databáze za posledních {minutes} minut")
            return df
        except pyodbc.Error as e:
            logger.error(f"Chyba při načítání dat z databáze: {e}")
            return None
        finally:
            if cursor:
                cursor.close()

def detect_market_condition(price_df):
    """Detekuje aktuální tržní podmínky."""
    try:
        # Výpočet volatility
        returns = price_df['close_price'].pct_change().dropna()
        volatility = returns.std()

        # Výpočet trendu
        price_df['ma_short'] = price_df['close_price'].rolling(window=5).mean()
        price_df['ma_long'] = price_df['close_price'].rolling(window=20).mean()

        # Odstranění řádků s chybějícími hodnotami
        price_df = price_df.dropna(subset=['ma_short', 'ma_long'])

        if price_df.empty:
            return "unknown"

        # Výpočet rozdílu mezi krátkým a dlouhým průměrem
        ma_diff = price_df['ma_short'].iloc[-1] - price_df['ma_long'].iloc[-1]

        # Určení tržních podmínek
        if volatility > 0.005:  # Vysoká volatilita
            if ma_diff > 0:
                condition = "volatile_bullish"
            else:
                condition = "volatile_bearish"
        else:  # Nízká volatilita
            if ma_diff > 0:
                condition = "stable_bullish"
            else:
                condition = "stable_bearish"

        logger.info(f"Detekované tržní podmínky: {condition}")
        return condition
    except Exception as e:
        logger.error(f"Chyba při detekci tržních podmínek: {e}")
        return "unknown"

def get_best_model_path(condition):
    """Vrátí cestu k nejlepšímu modelu pro dané tržní podmínky."""
    # Mapování tržních podmínek na modely
    model_map = {
        "volatile_bullish": "model_volatile_bullish.pkl",
        "volatile_bearish": "model_volatile_bearish.pkl",
        "stable_bullish": "model_stable_bullish.pkl",
        "stable_bearish": "model_stable_bearish.pkl",
        "unknown": "alch_price_model.pkl"
    }

    # Kontrola, zda model existuje
    model_path = model_map.get(condition, "alch_price_model.pkl")

    if not os.path.exists(model_path):
        logger.warning(f"Model {model_path} neexistuje, používám výchozí model")
        model_path = "alch_price_model.pkl"

    return model_path

def predict_price(model, data_df, feature_columns):
    """Predikuje cenu na základě modelu a dat."""
    try:
        # Kontrola, zda máme všechny potřebné sloupce
        missing_columns = [col for col in feature_columns if col not in data_df.columns]
        if missing_columns:
            logger.error(f"Chybí sloupce: {missing_columns}")
            return None

        # Výběr posledního řádku dat
        last_row = data_df.iloc[0]
        current_price = last_row['close_price']

        # Vytvoření příznakového vektoru
        X = np.array([last_row[col] for col in feature_columns]).reshape(1, -1)

        # Standardní predikce bez pokusu o GPU akceleraci
        # XGBoost 2.0+ automaticky použije GPU, pokud je k dispozici a model byl natrénován na GPU
        try:
            # Kontrola, zda model je XGBoost model
            if hasattr(model, 'get_booster'):
                logger.info("Model je XGBoost model, používám standardní predikci")

                # Standardní predikce
                raw_prediction = model.predict(X)[0]
                logger.info("Predikce provedena pomocí standardního rozhraní XGBoost")
            else:
                # Standardní predikce pro jiné modely
                raw_prediction = model.predict(X)[0]
        except Exception as e:
            logger.warning(f"Chyba při predikci: {e}, používám fallback predikci")
            # Fallback na standardní predikci
            raw_prediction = model.predict(X)[0]

        # Kalibrace predikce - multiplikativní korekční faktor
        # Vynásobíme predikci faktorem 1.25 (zvýšení o 25%), abychom kompenzovali systematický bias
        calibration_factor = 1.25
        calibrated_prediction = raw_prediction * calibration_factor
        logger.info(f"Kalibrace predikce: {raw_prediction} * {calibration_factor} = {calibrated_prediction}")

        # Omezení extrémních predikcí
        # Omezíme predikci na maximální změnu -1% až +3% za 5 minut
        # Asymetrické omezení, protože model má tendenci předpovídat nízké hodnoty
        min_change = 0.01  # 1%
        max_change = 0.03  # 3%
        min_price = current_price * (1 - min_change)
        max_price = current_price * (1 + max_change)

        # Omezení predikce na rozumný rozsah
        prediction = max(min(calibrated_prediction, max_price), min_price)

        # Výpis původní a omezené predikce
        if prediction != calibrated_prediction:
            if prediction == min_price:
                logger.warning(f"Kalibrovaná predikce {calibrated_prediction} byla omezena na {prediction} (min. změna -{min_change*100}%)")
            elif prediction == max_price:
                logger.warning(f"Kalibrovaná predikce {calibrated_prediction} byla omezena na {prediction} (max. změna +{max_change*100}%)")
            else:
                logger.warning(f"Kalibrovaná predikce {calibrated_prediction} byla omezena na {prediction}")
        else:
            logger.info(f"Predikce ceny: {prediction}")

        return prediction
    except Exception as e:
        logger.error(f"Chyba při predikci ceny: {e}")
        return None

def save_prediction(conn, timestamp, current_price, predicted_price, market_condition):
    """Uloží predikci do databáze."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Kontrola, zda timestamp má časovou zónu
        if timestamp.tzinfo is not None:
            # Odstranění časové zóny pro uložení do databáze
            # SQL Server ukládá datetime bez časové zóny
            timestamp = timestamp.replace(tzinfo=None)

        # Kontrola, zda timestamp je v budoucnosti
        current_time = get_current_time_local().replace(tzinfo=None)
        if timestamp > current_time:
            logger.info(f"Ukládám predikci s časem v budoucnosti: {timestamp} (aktuální čas: {current_time})")
        else:
            logger.info(f"Ukládám predikci s časem: {timestamp}")

        # Vložení predikce
        cursor.execute("""
        INSERT INTO dbo.alch_predictions (timestamp, current_price, predicted_price, market_condition)
        VALUES (?, ?, ?, ?)
        """, (timestamp, float(current_price), float(predicted_price), market_condition))

        conn.commit()
        logger.info("Predikce úspěšně uložena do databáze")
        return True
    except pyodbc.Error as e:
        logger.error(f"Chyba při ukládání predikce: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def main():
    """Hlavní funkce."""
    print("=== Oprava a vylepšení predikčního systému (v3) ===")

    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return

    try:
        # Načtení dat (použití API)
        data_df = load_recent_data(conn, use_api=True)
        if data_df is None or data_df.empty:
            logger.error("Nedostatek dat pro predikci")
            return

        # Detekce tržních podmínek
        market_condition = detect_market_condition(data_df)

        # Výběr nejlepšího modelu
        model_path = get_best_model_path(market_condition)

        # Načtení modelu
        model = load_model(model_path)
        if model is None:
            logger.error("Nelze načíst model")
            return

        # Definice příznaků
        feature_columns = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]

        # Kontrola, zda máme všechny potřebné sloupce
        missing_columns = [col for col in feature_columns if col not in data_df.columns]
        if missing_columns:
            logger.info(f"Dopáčítávám chybějící indikátory: {missing_columns}")

            # Pokud chybí indikátory, zkusíme je vypočítat
            try:
                import ta

                # RSI
                if 'RSI' not in data_df.columns:
                    rsi = ta.momentum.RSIIndicator(data_df['close_price'], window=14)
                    data_df['RSI'] = rsi.rsi()

                # EMA
                if 'EMA9' not in data_df.columns:
                    data_df['EMA9'] = ta.trend.ema_indicator(data_df['close_price'], window=9)
                if 'EMA20' not in data_df.columns:
                    data_df['EMA20'] = ta.trend.ema_indicator(data_df['close_price'], window=20)

                # Bollinger Bands
                if 'boll_high' not in data_df.columns or 'boll_low' not in data_df.columns:
                    bollinger = ta.volatility.BollingerBands(data_df['close_price'], window=20, window_dev=2)
                    data_df['boll_high'] = bollinger.bollinger_hband()
                    data_df['boll_low'] = bollinger.bollinger_lband()

                # ATR
                if 'ATR' not in data_df.columns and 'high' in data_df.columns and 'low' in data_df.columns:
                    atr = ta.volatility.AverageTrueRange(data_df['high'], data_df['low'], data_df['close_price'], window=14)
                    data_df['ATR'] = atr.average_true_range()
                elif 'ATR' not in data_df.columns:
                    # Pokud nemáme high a low, použijeme close_price
                    data_df['ATR'] = data_df['close_price'] * 0.01  # Jednoduchá aproximace

                # Odstranění řádků s chybějícími hodnotami
                data_df = data_df.dropna()

                logger.info("Indikátory byly úspěšně dopočítány")
            except Exception as e:
                logger.error(f"Chyba při výpočtu indikátorů: {e}")
                return

        # Predikce ceny
        predicted_price = predict_price(model, data_df, feature_columns)
        if predicted_price is None:
            logger.error("Nelze provést predikci")
            return

        # Aktuální cena
        current_price = data_df['close_price'].iloc[0]

        # Aktuální čas v lokální časové zóně
        current_timestamp = get_current_time_local()

        # Časová značka pro predikci (o 5 minut později)
        prediction_timestamp = current_timestamp + timedelta(minutes=5)

        # Uložení predikce s časovou značkou posunutou o 5 minut dopředu
        save_prediction(conn, prediction_timestamp, current_price, predicted_price, market_condition)

        # Výpis výsledků
        print(f"Aktuální cena: {current_price}")
        print(f"Predikovaná cena (za 5 minut): {predicted_price}")
        print(f"Tržní podmínky: {market_condition}")
        print(f"Použitý model: {model_path}")

        # Výpočet očekávané změny
        expected_change = (predicted_price - current_price) / current_price * 100
        print(f"Očekávaná změna: {expected_change:.2f}%")

        # Doporučení
        if expected_change > 0.5:
            print("Doporučení: NÁKUP")
        elif expected_change < -0.5:
            print("Doporučení: PRODEJ")
        else:
            print("Doporučení: DRŽET")
    except Exception as e:
        logger.error(f"Neočekávaná chyba: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
