"""
Diagnostický skript pro analýzu přesnosti predikcí.
"""
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pyodbc
import joblib
from datetime import datetime, timedelta
import matplotlib.dates as mdates

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def load_model(model_path):
    """Načte model ze souboru."""
    try:
        model = joblib.load(model_path)
        logger.info(f"Model úspěšně načten z {model_path}")
        return model
    except Exception as e:
        logger.error(f"Chyba při načítání modelu: {e}")
        return None

def load_recent_data(conn, hours=24):
    """Načte nedávná data z databáze."""
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Výpočet data
        start_date = datetime.now() - timedelta(hours=hours)
        
        # Načtení OHLCV dat
        cursor.execute("""
        SELECT * FROM alch_price_history
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))
        
        price_rows = cursor.fetchall()
        
        if not price_rows:
            logger.warning(f"Nenalezena žádná cenová data za posledních {hours} hodin")
            return None, None
        
        # Vytvoření DataFrame pro ceny
        price_columns = [column[0] for column in cursor.description]
        price_df = pd.DataFrame.from_records(price_rows, columns=price_columns)
        price_df['timestamp'] = pd.to_datetime(price_df['timestamp'])
        price_df.set_index('timestamp', inplace=True)
        
        # Načtení indikátorů
        cursor.execute("""
        SELECT * FROM alch_indikatory
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))
        
        indicator_rows = cursor.fetchall()
        
        if not indicator_rows:
            logger.warning(f"Nenalezeny žádné indikátory za posledních {hours} hodin")
            return price_df, None
        
        # Vytvoření DataFrame pro indikátory
        indicator_columns = [column[0] for column in cursor.description]
        indicator_df = pd.DataFrame.from_records(indicator_rows, columns=indicator_columns)
        indicator_df['timestamp'] = pd.to_datetime(indicator_df['timestamp'])
        indicator_df.set_index('timestamp', inplace=True)
        
        logger.info(f"Načteno {len(price_df)} cenových záznamů a {len(indicator_df)} indikátorů")
        return price_df, indicator_df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání dat: {e}")
        return None, None
    finally:
        if cursor:
            cursor.close()

def load_predictions(conn, hours=24):
    """Načte nedávné predikce z databáze."""
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Výpočet data
        start_date = datetime.now() - timedelta(hours=hours)
        
        # Načtení predikcí
        cursor.execute("""
        SELECT * FROM alch_predictions
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))
        
        rows = cursor.fetchall()
        
        if not rows:
            logger.warning(f"Nenalezeny žádné predikce za posledních {hours} hodin")
            return None
        
        # Vytvoření DataFrame
        columns = [column[0] for column in cursor.description]
        df = pd.DataFrame.from_records(rows, columns=columns)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        logger.info(f"Načteno {len(df)} predikcí za posledních {hours} hodin")
        return df
    except pyodbc.Error as e:
        logger.error(f"Chyba při načítání predikcí: {e}")
        return None
    finally:
        if cursor:
            cursor.close()

def analyze_feature_importance(model, feature_names):
    """Analyzuje důležitost příznaků v modelu."""
    try:
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
            indices = np.argsort(importances)[::-1]
            
            print("\nDůležitost příznaků:")
            for i in range(len(feature_names)):
                print(f"{i+1}. {feature_names[indices[i]]}: {importances[indices[i]]:.4f}")
            
            # Vykreslení grafu
            plt.figure(figsize=(10, 6))
            plt.title('Důležitost příznaků')
            plt.bar(range(len(feature_names)), importances[indices], align='center')
            plt.xticks(range(len(feature_names)), [feature_names[i] for i in indices], rotation=90)
            plt.tight_layout()
            plt.savefig('feature_importance.png')
            logger.info("Graf důležitosti příznaků uložen jako 'feature_importance.png'")
        else:
            logger.warning("Model nemá atribut feature_importances_")
    except Exception as e:
        logger.error(f"Chyba při analýze důležitosti příznaků: {e}")

def analyze_prediction_errors(predictions_df):
    """Analyzuje chyby predikcí."""
    try:
        if predictions_df is None or predictions_df.empty:
            logger.warning("Žádné predikce k analýze")
            return
        
        # Filtrování predikcí s aktuální cenou
        valid_df = predictions_df.dropna(subset=['actual_price'])
        
        if valid_df.empty:
            logger.warning("Žádné predikce s aktuální cenou k analýze")
            return
        
        # Výpočet chyb
        valid_df['error'] = valid_df['predicted_price'] - valid_df['actual_price']
        valid_df['error_pct'] = (valid_df['error'] / valid_df['actual_price']) * 100
        valid_df['abs_error_pct'] = valid_df['error_pct'].abs()
        
        # Výpočet směru
        valid_df['predicted_direction'] = np.sign(valid_df['predicted_price'] - valid_df['current_price'])
        valid_df['actual_direction'] = np.sign(valid_df['actual_price'] - valid_df['current_price'])
        valid_df['direction_correct'] = valid_df['predicted_direction'] == valid_df['actual_direction']
        
        # Statistiky
        mean_error = valid_df['error'].mean()
        mean_abs_error = valid_df['error'].abs().mean()
        mean_error_pct = valid_df['error_pct'].mean()
        mean_abs_error_pct = valid_df['abs_error_pct'].mean()
        direction_accuracy = valid_df['direction_correct'].mean() * 100
        
        print("\nStatistiky chyb predikcí:")
        print(f"Počet predikcí: {len(valid_df)}")
        print(f"Průměrná chyba: {mean_error:.6f}")
        print(f"Průměrná absolutní chyba: {mean_abs_error:.6f}")
        print(f"Průměrná procentuální chyba: {mean_error_pct:.2f}%")
        print(f"Průměrná absolutní procentuální chyba: {mean_abs_error_pct:.2f}%")
        print(f"Přesnost predikce směru: {direction_accuracy:.2f}%")
        
        # Analýza chyb v čase
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 1, 1)
        plt.plot(valid_df['timestamp'], valid_df['error_pct'], marker='o')
        plt.axhline(y=0, color='r', linestyle='-')
        plt.title('Procentuální chyba predikce v čase')
        plt.xlabel('Datum a čas')
        plt.ylabel('Chyba (%)')
        plt.grid(True)
        
        plt.subplot(2, 1, 2)
        plt.plot(valid_df['timestamp'], valid_df['direction_correct'], marker='o')
        plt.title('Správnost predikce směru v čase')
        plt.xlabel('Datum a čas')
        plt.ylabel('Správný směr (1 = ano, 0 = ne)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('prediction_errors.png')
        logger.info("Graf chyb predikcí uložen jako 'prediction_errors.png'")
        
        # Analýza korelace chyb s různými faktory
        if 'market_condition' in valid_df.columns:
            print("\nChyby podle tržních podmínek:")
            for condition in valid_df['market_condition'].unique():
                condition_df = valid_df[valid_df['market_condition'] == condition]
                print(f"  {condition}: {condition_df['abs_error_pct'].mean():.2f}% (počet: {len(condition_df)})")
        
        # Analýza extrémních chyb
        extreme_errors = valid_df[valid_df['abs_error_pct'] > 5]  # Chyby větší než 5%
        if not extreme_errors.empty:
            print(f"\nNalezeno {len(extreme_errors)} extrémních chyb (>5%):")
            for _, row in extreme_errors.iterrows():
                print(f"  Čas: {row['timestamp']}, Predikce: {row['predicted_price']:.6f}, Skutečnost: {row['actual_price']:.6f}, Chyba: {row['error_pct']:.2f}%")
    except Exception as e:
        logger.error(f"Chyba při analýze chyb predikcí: {e}")

def test_model_on_recent_data(model, price_df, indicator_df, feature_names):
    """Testuje model na nedávných datech."""
    try:
        if model is None or price_df is None or indicator_df is None:
            logger.warning("Chybí model nebo data pro testování")
            return
        
        # Spojení dat
        combined_df = price_df.join(indicator_df, how='inner')
        
        if combined_df.empty:
            logger.warning("Žádná společná data pro testování")
            return
        
        # Kontrola, zda máme všechny potřebné příznaky
        missing_features = [f for f in feature_names if f not in combined_df.columns]
        if missing_features:
            logger.error(f"Chybí příznaky pro model: {missing_features}")
            return
        
        # Příprava dat pro predikci
        X = combined_df[feature_names].values
        
        # Predikce
        predictions = model.predict(X)
        
        # Přidání predikcí do DataFrame
        combined_df['predicted_price'] = predictions
        
        # Výpočet skutečných budoucích cen (posun o 5 minut)
        combined_df['future_price'] = combined_df['close_price'].shift(-5)
        
        # Odstranění řádků bez budoucích cen
        valid_df = combined_df.dropna(subset=['future_price'])
        
        if valid_df.empty:
            logger.warning("Žádná data s budoucími cenami pro analýzu")
            return
        
        # Výpočet chyb
        valid_df['error'] = valid_df['predicted_price'] - valid_df['future_price']
        valid_df['error_pct'] = (valid_df['error'] / valid_df['future_price']) * 100
        valid_df['abs_error_pct'] = valid_df['error_pct'].abs()
        
        # Výpočet směru
        valid_df['predicted_direction'] = np.sign(valid_df['predicted_price'] - valid_df['close_price'])
        valid_df['actual_direction'] = np.sign(valid_df['future_price'] - valid_df['close_price'])
        valid_df['direction_correct'] = valid_df['predicted_direction'] == valid_df['actual_direction']
        
        # Statistiky
        mean_error = valid_df['error'].mean()
        mean_abs_error = valid_df['error'].abs().mean()
        mean_error_pct = valid_df['error_pct'].mean()
        mean_abs_error_pct = valid_df['abs_error_pct'].mean()
        direction_accuracy = valid_df['direction_correct'].mean() * 100
        
        print("\nVýsledky testování modelu na nedávných datech:")
        print(f"Počet testovacích vzorků: {len(valid_df)}")
        print(f"Průměrná chyba: {mean_error:.6f}")
        print(f"Průměrná absolutní chyba: {mean_abs_error:.6f}")
        print(f"Průměrná procentuální chyba: {mean_error_pct:.2f}%")
        print(f"Průměrná absolutní procentuální chyba: {mean_abs_error_pct:.2f}%")
        print(f"Přesnost predikce směru: {direction_accuracy:.2f}%")
        
        # Vykreslení grafu
        plt.figure(figsize=(12, 6))
        plt.plot(valid_df.index, valid_df['close_price'], label='Aktuální cena', color='blue')
        plt.plot(valid_df.index, valid_df['predicted_price'], label='Predikovaná cena', color='red', linestyle='--')
        plt.plot(valid_df.index, valid_df['future_price'], label='Budoucí cena', color='green')
        plt.title('Testování modelu na nedávných datech')
        plt.xlabel('Datum a čas')
        plt.ylabel('Cena')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig('model_test.png')
        logger.info("Graf testování modelu uložen jako 'model_test.png'")
        
        return valid_df
    except Exception as e:
        logger.error(f"Chyba při testování modelu: {e}")
        return None

def analyze_model_parameters(model):
    """Analyzuje parametry modelu."""
    try:
        print("\nParametry modelu:")
        
        if hasattr(model, 'get_params'):
            params = model.get_params()
            for key, value in params.items():
                print(f"  {key}: {value}")
        else:
            print("  Model nemá metodu get_params")
        
        # Analýza typu modelu
        model_type = type(model).__name__
        print(f"\nTyp modelu: {model_type}")
        
        # Specifické informace podle typu modelu
        if model_type == 'XGBRegressor':
            print(f"  Počet stromů: {model.n_estimators}")
            print(f"  Maximální hloubka: {model.max_depth}")
            print(f"  Learning rate: {model.learning_rate}")
            print(f"  Regularizace alpha: {model.reg_alpha}")
            print(f"  Regularizace lambda: {model.reg_lambda}")
    except Exception as e:
        logger.error(f"Chyba při analýze parametrů modelu: {e}")

def main():
    """Hlavní funkce."""
    print("=== Diagnostika predikčního modelu ===")
    
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return
    
    try:
        # Načtení modelu
        model_path = input("Zadejte cestu k souboru modelu (výchozí: alch_price_model.pkl): ").strip()
        if not model_path:
            model_path = "alch_price_model.pkl"
        
        model = load_model(model_path)
        if model is None:
            return
        
        # Analýza parametrů modelu
        analyze_model_parameters(model)
        
        # Načtení dat
        hours = int(input("Zadejte počet hodin historie pro analýzu (výchozí: 24): ") or "24")
        price_df, indicator_df = load_recent_data(conn, hours)
        
        # Načtení predikcí
        predictions_df = load_predictions(conn, hours)
        
        # Analýza chyb predikcí
        if predictions_df is not None:
            analyze_prediction_errors(predictions_df)
        
        # Testování modelu na nedávných datech
        feature_names = input("Zadejte názvy příznaků oddělené čárkou (výchozí: RSI,EMA9,EMA20,boll_high,boll_low,ATR): ").strip()
        if not feature_names:
            feature_names = "RSI,EMA9,EMA20,boll_high,boll_low,ATR"
        
        feature_list = [f.strip() for f in feature_names.split(',')]
        
        test_results = test_model_on_recent_data(model, price_df, indicator_df, feature_list)
        
        # Analýza důležitosti příznaků
        analyze_feature_importance(model, feature_list)
        
        print("\nDiagnostika dokončena. Grafy byly uloženy do aktuálního adresáře.")
    except Exception as e:
        logger.error(f"Chyba při diagnostice: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
