"""
Skript pro kontinuální běh predikčního systému.
Spouští predikci v pravidelných intervalech a zajišťuje nepřetržitý provoz.
"""
import os
import sys
import time
import logging
import subprocess
import traceback
from datetime import datetime, timedelta
import signal
import argparse

# Nastavení logování
log_filename = f"continuous_run_{datetime.now().strftime('%Y%m%d')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Globální proměnné
running = True
last_training_time = None
last_update_time = None
last_dashboard_time = None

def signal_handler(sig, frame):
    """Zpracování signálu pro ukončení skriptu."""
    global running
    logger.info("Přijat signál pro ukončení. Ukončuji běh...")
    running = False

def run_command(cmd, timeout=None):
    """
    Spustí příkaz a vrátí jeho výstup.

    Args:
        cmd: Seznam s příkazem a argumenty
        timeout: Časový limit pro běh příkazu (v sekundách)

    Returns:
        Tuple (návratový kód, stdout, stderr)
    """
    try:
        logger.info(f"Spouštím příkaz: {' '.join(cmd)}")
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        try:
            stdout, stderr = process.communicate(timeout=timeout)
            return process.returncode, stdout, stderr
        except subprocess.TimeoutExpired:
            logger.warning(f"Příkaz překročil časový limit {timeout}s. Ukončuji proces.")
            process.kill()
            stdout, stderr = process.communicate()
            return -1, stdout, stderr
    except Exception as e:
        logger.error(f"Chyba při spouštění příkazu: {e}")
        return -1, "", str(e)

def should_train_model():
    """
    Určí, zda by měl být model přetrénován.

    Returns:
        True pokud by měl být model přetrénován, jinak False
    """
    global last_training_time

    # Pokud jsme ještě netrénovali nebo uplynulo více než 6 hodin
    if last_training_time is None or (datetime.now() - last_training_time) > timedelta(hours=6):
        return True

    return False

def should_update_metrics():
    """
    Určí, zda by měly být aktualizovány metriky.

    Returns:
        True pokud by měly být aktualizovány metriky, jinak False
    """
    global last_update_time

    # Pokud jsme ještě neaktualizovali nebo uplynulo více než 15 minut
    if last_update_time is None or (datetime.now() - last_update_time) > timedelta(minutes=15):
        return True

    return False

def should_update_dashboard():
    """
    Určí, zda by měl být aktualizován dashboard.

    Returns:
        True pokud by měl být aktualizován dashboard, jinak False
    """
    global last_dashboard_time

    # Pokud jsme ještě neaktualizovali dashboard nebo uplynulo více než 30 minut
    if last_dashboard_time is None or (datetime.now() - last_dashboard_time) > timedelta(minutes=30):
        return True

    return False

def main():
    """Hlavní funkce pro kontinuální běh predikčního systému."""
    global running, last_training_time, last_update_time, last_dashboard_time

    # Zpracování argumentů příkazové řádky
    parser = argparse.ArgumentParser(description='Kontinuální běh predikčního systému')
    parser.add_argument('--interval', type=int, default=60,
                        help='Interval mezi predikcemi (v sekundách)')
    parser.add_argument('--ensemble', action='store_true',
                        help='Použít ensemble model místo standardního modelu')
    parser.add_argument('--use_sentiment', action='store_true',
                        help='Zahrnout sentiment do predikce')
    parser.add_argument('--update_sentiment', action='store_true',
                        help='Aktualizovat data o sentimentu')
    parser.add_argument('--no_dashboard', action='store_true',
                        help='Neaktualizovat dashboard')

    args = parser.parse_args()

    # Nastavení zpracování signálů
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info(f"Spouštím kontinuální běh predikčního systému s intervalem {args.interval} sekund")
    logger.info(f"Pro ukončení stiskněte Ctrl+C")

    # Kontrola, zda existují všechny potřebné soubory
    required_files = [
        'run_system.py',
        'integration.py',
        'combined_script.py'
    ]

    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        logger.error(f"Chybí následující soubory: {missing_files}")
        return

    # Počáteční trénování modelu
    if should_train_model():
        logger.info("Provádím počáteční trénování modelu...")

        cmd = [sys.executable, 'run_system.py', 'train']
        if args.ensemble:
            cmd.append('--ensemble')
        if args.use_sentiment:
            cmd.append('--use_sentiment')

        returncode, stdout, stderr = run_command(cmd, timeout=1800)  # 30 minut timeout

        if returncode == 0:
            logger.info("Počáteční trénování modelu úspěšně dokončeno")
            last_training_time = datetime.now()
        else:
            logger.error(f"Počáteční trénování modelu selhalo: {stderr}")

    # Hlavní smyčka
    cycle_count = 0

    while running:
        cycle_start_time = time.time()
        cycle_count += 1

        try:
            logger.info(f"Začínám cyklus #{cycle_count}")

            # 1. Spuštění predikce
            cmd = [sys.executable, 'fix_prediction_v2.py']
            # Poznámka: fix_prediction_v2.py nepoužívá argumenty ensemble, use_sentiment a update_sentiment

            returncode, stdout, stderr = run_command(cmd, timeout=45)  # 45 sekund timeout

            if returncode == 0:
                logger.info("Predikce úspěšně dokončena")
            else:
                logger.error(f"Predikce selhala: {stderr}")

            # 2. Aktualizace metrik, pokud je čas
            if should_update_metrics():
                logger.info("Aktualizuji metriky...")

                cmd = [sys.executable, 'run_system.py', 'update']
                returncode, stdout, stderr = run_command(cmd, timeout=60)  # 60 sekund timeout

                if returncode == 0:
                    logger.info("Aktualizace metrik úspěšně dokončena")
                    last_update_time = datetime.now()
                else:
                    logger.error(f"Aktualizace metrik selhala: {stderr}")

            # 3. Aktualizace grafů, pokud je čas a není zakázáno
            if not args.no_dashboard and should_update_dashboard():
                logger.info("Aktualizuji grafy...")

                # Aktualizace grafu predikce
                cmd = [sys.executable, 'simple_prediction_graph.py']
                returncode, stdout, stderr = run_command(cmd, timeout=60)  # 1 minuta timeout

                if returncode == 0:
                    logger.info("Aktualizace grafu predikce úspěšně dokončena")
                else:
                    logger.error(f"Aktualizace grafu predikce selhala: {stderr}")

                # Aktualizace grafu sentimentu
                cmd = [sys.executable, 'simple_sentiment_graph.py']
                returncode, stdout, stderr = run_command(cmd, timeout=60)  # 1 minuta timeout

                if returncode == 0:
                    logger.info("Aktualizace grafu sentimentu úspěšně dokončena")
                    last_dashboard_time = datetime.now()
                else:
                    logger.error(f"Aktualizace grafu sentimentu selhala: {stderr}")

            # 4. Trénování modelu, pokud je čas
            if should_train_model():
                logger.info("Přetrénovávám model...")

                cmd = [sys.executable, 'run_system.py', 'train']
                if args.ensemble:
                    cmd.append('--ensemble')
                if args.use_sentiment:
                    cmd.append('--use_sentiment')

                returncode, stdout, stderr = run_command(cmd, timeout=1800)  # 30 minut timeout

                if returncode == 0:
                    logger.info("Trénování modelu úspěšně dokončeno")
                    last_training_time = datetime.now()
                else:
                    logger.error(f"Trénování modelu selhalo: {stderr}")

            # Výpočet doby trvání cyklu a čekání do dalšího cyklu
            cycle_duration = time.time() - cycle_start_time
            logger.info(f"Cyklus #{cycle_count} dokončen za {cycle_duration:.2f} sekund")

            # Čekání do dalšího cyklu
            sleep_time = max(0, args.interval - cycle_duration)
            if sleep_time > 0:
                logger.info(f"Čekám {sleep_time:.2f} sekund do dalšího cyklu...")

                # Čekání s kontrolou ukončení
                sleep_start = time.time()
                while running and (time.time() - sleep_start) < sleep_time:
                    time.sleep(0.1)
            else:
                logger.warning(f"Cyklus trval déle než interval ({cycle_duration:.2f} > {args.interval})")

        except Exception as e:
            logger.error(f"Neočekávaná chyba v cyklu #{cycle_count}: {e}")
            logger.error(traceback.format_exc())

            # Krátká pauza před dalším cyklem v případě chyby
            time.sleep(5)

    logger.info("Kontinuální běh predikčního systému ukončen")

if __name__ == "__main__":
    main()
