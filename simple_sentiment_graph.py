"""
Jednoduchý skript pro vykreslení grafu sentimentu vs. cena.
"""
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pyodbc
from datetime import datetime, timedelta

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Připojovací řetězec k databázi
CONN_STR = "DRIVER={SQL Server};SERVER=GPPC\\SQLGPPC;DATABASE=byb;Trusted_Connection=yes;"

def connect_to_db():
    """Připojí se k databázi."""
    try:
        conn = pyodbc.connect(CONN_STR)
        return conn
    except pyodbc.Error as e:
        logger.error(f"Chyba při připojování k databázi: {e}")
        return None

def plot_simple_sentiment_graph(days=7):
    """
    Vykreslí jednoduchý graf sentimentu v čase.

    Args:
        days: Počet dní historie
    """
    # Připojení k databázi
    conn = connect_to_db()
    if not conn:
        logger.error("Nelze se připojit k databázi")
        return

    cursor = None
    try:
        cursor = conn.cursor()

        # Výpočet data
        start_date = datetime.now() - timedelta(days=days)

        # Načtení dat o sentimentu
        cursor.execute("""
        SELECT timestamp, combined_sentiment FROM dbo.EXTERNAL_SENTIMENT
        WHERE timestamp >= ?
        ORDER BY timestamp ASC
        """, (start_date,))

        sentiment_rows = cursor.fetchall()

        # Diagnostika dat
        print(f"Počet řádků: {len(sentiment_rows)}")
        if sentiment_rows:
            print(f"První řádek: {sentiment_rows[0]}")
            print(f"Typ prvního řádku: {type(sentiment_rows[0])}")
            print(f"Počet sloupců v prvním řádku: {len(sentiment_rows[0])}")

        # Vytvoření jednoduchého grafu
        plt.figure(figsize=(12, 6))

        if sentiment_rows:
            # Vytvoření seznamů pro osy x a y
            timestamps = []
            sentiments = []

            for row in sentiment_rows:
                timestamps.append(row[0])
                sentiments.append(row[1])

            print(f"Timestamps: {timestamps}")
            print(f"Sentiments: {sentiments}")

            # Vykreslení grafu sentimentu
            plt.plot(timestamps, sentiments, 'r-', marker='o')
            plt.title(f'Sentiment v čase (posledních {days} dní)')
            plt.xlabel('Datum a čas')
            plt.ylabel('Sentiment (-1 až 1)')
            plt.grid(True)
            plt.ylim(-1, 1)

            # Přidání horizontální čáry na nule
            plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)

            # Přidání popisků
            plt.text(0.02, 0.95, 'Pozitivní sentiment', transform=plt.gca().transAxes, color='green')
            plt.text(0.02, 0.05, 'Negativní sentiment', transform=plt.gca().transAxes, color='red')

            logger.info(f"Vykresleno {len(timestamps)} bodů sentimentu")
        else:
            plt.text(0.5, 0.5, 'Žádná data o sentimentu k dispozici',
                     horizontalalignment='center', verticalalignment='center',
                     transform=plt.gca().transAxes, fontsize=14)
            logger.warning("Žádná data o sentimentu k dispozici")

        plt.tight_layout()

        # Uložení grafu
        graph_path = os.path.join(os.getcwd(), 'sentiment_graph.png')
        plt.savefig(graph_path)
        logger.info(f"Graf sentimentu uložen jako: {graph_path}")
        print(f"Graf uložen do: {graph_path}")
    except Exception as e:
        logger.error(f"Chyba při vykreslování grafu sentimentu: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    plot_simple_sentiment_graph()
