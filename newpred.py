import ccxt
import pandas as pd
import numpy as np
import pyodbc
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
import xgboost as xgb
import time
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv # Pre načítanie premenných prostredia
from indicators import add_sma, add_rsi  # Add this import

# Načítanie premenných prostredia zo súboru.env
load_dotenv()

# --- Konfigurácia ---
SYMBOL = 'SOL/USDT'  # Formát pre ccxt (pre python-binance by to bolo 'SOLUSDT')
SYMBOL_DB = 'SOLUSDT' # Formát pre názvy tabuliek v DB
TIMEFRAME = '1m'       # Časový rámec sviečok (1 minúta)
PREDICTION_HORIZON = 5 # Predikčný horizont v poč<PERSON> čas<PERSON><PERSON><PERSON> krokov (5 minút)
DB_TABLE_NAME = f'{SYMBOL_DB}_{TIMEFRAME}_OHLCV' # Názov tabuľky v SQL DB

# Načítanie prihlasovacích údajov k DB z premenných prostredia
DB_DRIVER = os.getenv('DB_DRIVER', '{ODBC Driver 17 for SQL Server}') # Príklad ovládača
DB_SERVER = os.getenv('DB_SERVER', '192.168.0.100,1433')
DB_DATABASE = os.getenv('DB_DATABASE', 'byb')
DB_USERNAME = os.getenv('DB_USERNAME', 'dbuser')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'pochy1249')
SQL_CONN_STR = (
    f'DRIVER={DB_DRIVER};'
    f'SERVER={DB_SERVER};'
    f'DATABASE={DB_DATABASE};'
    f'UID={DB_USERNAME};'
    f'PWD={DB_PASSWORD};'
    'TrustServerCertificate=yes;'
    'Encrypt=no;'
)

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.0.100,1433;"  # Například "192.168.1.100\\SQLEXPRESS"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

BINANCE_API_LIMIT = 1000 # Maximálny počet sviečok na jednu požiadavku
RATE_LIMIT_DELAY = 0.2 # Pauza v sekundách medzi API volaniami na rešpektovanie rate limitov

N_SPLITS_CV = 5         # Počet splitov pre TimeSeriesSplit
XGB_PARAMS = {
    'n_estimators': 100,
    'learning_rate': 0.1,
    'objective': 'reg:squarederror', # Pre regresiu
    'random_state': 42,
    'n_jobs': -1 # Použiť všetky dostupné CPU jadrá
}

def connect_db(conn_str: str):
    return pyodbc.connect(conn_str)

def save_dataframe_to_sql(df: pd.DataFrame, table_name: str, conn: pyodbc.Connection, chunksize: int = 1000):
    if df.empty:
        print("DataFrame je prázdny, neukladám nič do DB.")
        return

    # Reset index to make timestamp a regular column
    df = df.reset_index()

    cursor = conn.cursor()
    try:
        cursor.fast_executemany = True
        cols = ','.join([f'[{col}]' for col in df.columns])
        vals_placeholder = ','.join(['?'] * len(df.columns))
        sql = f"INSERT INTO {table_name} ({cols}) VALUES ({vals_placeholder})"

        # Convert DataFrame to list of tuples, handling NaN values
        data_tuples = []
        for row in df.itertuples(index=False, name=None):
            # Replace NaN with None for SQL compatibility
            cleaned_row = tuple(None if pd.isna(x) else x for x in row)
            data_tuples.append(cleaned_row)

        num_inserted = 0
        for i in range(0, len(data_tuples), chunksize):
            chunk = data_tuples[i:i + chunksize]
            cursor.executemany(sql, chunk)
            num_inserted += len(chunk)

        conn.commit()
        print(f"Úspešne uložených {num_inserted} riadkov do tabuľky '{table_name}'.")

    except pyodbc.Error as ex:
        sqlstate = ex.args
        print(f"Chyba pri ukladaní dát do SQL Servera: SQLSTATE={sqlstate}, Chyba: {ex}")
        conn.rollback()
    except Exception as e:
        print(f"Všeobecná chyba pri ukladaní dát do DB: {e}")
        conn.rollback()
    finally:
        cursor.close()

def create_ohlcv_table_if_not_exists(table_name: str, conn: pyodbc.Connection):
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                           WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = '{table_name}')
            BEGIN
                CREATE TABLE {table_name} (
                    [timestamp] DATETIME2 PRIMARY KEY,
                    [open] FLOAT,
                    [high] FLOAT,
                    [low] FLOAT,
                    [close] FLOAT,
                    [volume] FLOAT
                );
            END
        """)
        conn.commit()
        print(f"Tabuľka '{table_name}' overená/vytvorená.")
    except pyodbc.Error as ex:
        sqlstate = ex.args
        print(f"Chyba pri vytváraní/overovaní tabuľky '{table_name}': SQLSTATE={sqlstate}, Chyba: {ex}")
        conn.rollback()
    except Exception as e:
        print(f"Všeobecná chyba pri vytváraní/overovaní tabuľky: {e}")
        conn.rollback()
    finally:
        cursor.close()

def load_data_from_sql(table_name: str, conn: pyodbc.Connection, start_date: str = None) -> pd.DataFrame | None:
    try:
        query = f"SELECT [timestamp], [open], [high], [low], [close], [volume] FROM {table_name}"
        if start_date:
            query += f" WHERE [timestamp] >= '{start_date}'"
        query += " ORDER BY [timestamp] ASC"

        df = pd.read_sql(query, conn)
        if not df.empty:
            df.set_index('timestamp', inplace=True)
            df.index = pd.to_datetime(df.index)
            df.index = df.index.tz_localize('UTC')

            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            print(f"Úspešne načítaných {len(df)} riadkov z tabuľky '{table_name}'.")
            return df
        else:
            print(f"Tabuľka '{table_name}' je prázdna.")
            return pd.DataFrame()
    except Exception as e:
        print(f"Chyba pri načítavaní dát z SQL tabuľky '{table_name}': {e}")
        return None

def download_historical_data(symbol: str, timeframe: str, start_date_str: str, exchange: ccxt.Exchange) -> list | None:
    all_ohlcv = []
    try:
        # Convert start_date_str to milliseconds timestamp
        if isinstance(start_date_str, str):
            since = exchange.parse8601(start_date_str)
        else:
            raise ValueError("start_date_str musí byť string")

        timeframe_duration_ms = exchange.parse_timeframe(timeframe) * 1000

        print(f"Sťahujem dáta pre {symbol} od {start_date_str} ({exchange.iso8601(since)}) s rámcom {timeframe}...")

        while True:
            print(f"  Sťahujem od timestamp: {exchange.iso8601(since)}")
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since, limit=BINANCE_API_LIMIT)

            if not ohlcv:
                break

            if all_ohlcv and ohlcv[0][0] <= all_ohlcv[-1][0]:
                # Skip duplicate data
                filtered_ohlcv = [x for x in ohlcv if x[0] > all_ohlcv[-1][0]]
                if not filtered_ohlcv:
                    break
                ohlcv = filtered_ohlcv

            first_ts = exchange.iso8601(ohlcv[0][0])
            last_ts = exchange.iso8601(ohlcv[-1][0])
            print(f"  Prijatých {len(ohlcv)} sviečok. Od {first_ts} do {last_ts}")

            all_ohlcv.extend(ohlcv)
            since = ohlcv[-1][0] + timeframe_duration_ms

            time.sleep(RATE_LIMIT_DELAY)

        print(f"Celkovo stiahnutých {len(all_ohlcv)} sviečok pre {symbol}.")
        return all_ohlcv if all_ohlcv else None

    except Exception as e:
        print(f"Neočekávaná chyba pri sťahovaní dát: {str(e)}")
        return None

def format_ohlcv_data(ohlcv_list: list) -> pd.DataFrame | None:
    if not ohlcv_list:
        return None

    df = pd.DataFrame(ohlcv_list, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
    df.set_index('timestamp', inplace=True)

    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    df.dropna(subset=['open', 'high', 'low', 'close', 'volume'], inplace=True)
    return df

def add_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    print("Pridávam technické indikátory (SMA, RSI)...")
    df = add_sma(df, length=5)
    df = add_sma(df, length=10)
    df = add_rsi(df, length=9)
    df = add_rsi(df, length=14)
    print(f"Nové stĺpce po pridaní indikátorov: {df.columns.tolist()}")
    return df

def add_lagged_features(df: pd.DataFrame, lags: list = [1, 2, 3, 4]) -> pd.DataFrame:
    print(f"Pridávam oneskorené príznaky pre 'close' a 'volume' s oneskoreniami: {lags}")
    for lag in lags:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'volume_lag_{lag}'] = df['volume'].shift(lag)

    print(f"Nové stĺpce po pridaní lagov: {df.columns.tolist()}")
    return df

def add_target_variable(df: pd.DataFrame, horizon: int) -> pd.DataFrame:
    print(f"Pridávam cieľovú premennú ('target') posunutím 'close' o {-horizon} krokov.")
    df['target'] = df['close'].shift(-horizon)
    return df

def prepare_data(df: pd.DataFrame, horizon: int, lags: list) -> tuple:
    df_processed = df.copy()
    df_processed = add_technical_indicators(df_processed)
    df_processed = add_lagged_features(df_processed, lags)
    df_processed = add_target_variable(df_processed, horizon)

    initial_rows = len(df_processed)
    df_processed.dropna(inplace=True)
    final_rows = len(df_processed)
    print(f"Odstránených {initial_rows - final_rows} riadkov kvôli NaN hodnotám.")

    # Save feature columns for consistent prediction
    feature_cols = [col for col in df_processed.columns if col != 'target']

    y = df_processed['target']
    X = df_processed[feature_cols]  # Use explicit feature columns

    print(f"Finálny tvar X: {X.shape}, y: {y.shape}")
    return X, y, feature_cols  # Return feature_cols for prediction

def perform_cross_validation(X: pd.DataFrame, y: pd.Series, n_splits: int) -> tuple:
    tscv = TimeSeriesSplit(n_splits=n_splits)
    all_fold_rmse = []
    models = []
    scalers = []

    print(f"\nSpúšťam {n_splits}-násobnú časovú krížovú validáciu...")

    for fold, (train_index, test_index) in enumerate(tscv.split(X)):
        print(f"--- Fold {fold + 1}/{n_splits} ---")
        X_train, X_test = X.iloc[train_index], X.iloc[test_index]
        y_train, y_test = y.iloc[train_index], y.iloc[test_index]
        print(f"  Train index: {train_index[0]} - {train_index[-1]}, size: {len(train_index)}")
        print(f"  Test index:  {test_index[0]} - {test_index[-1]}, size: {len(test_index)}")

        scaler = MinMaxScaler()
        scaler.fit(X_train)
        scalers.append(scaler)

        X_train_scaled = scaler.transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        model = xgb.XGBRegressor(**XGB_PARAMS)
        print("  Trénujem XGBoost model...")
        model.fit(X_train_scaled, y_train)
        models.append(model)

        y_pred = model.predict(X_test_scaled)
        # Calculate RMSE manually
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        all_fold_rmse.append(rmse)
        print(f"  RMSE pre Fold {fold + 1}: {rmse:.4f}")

    average_rmse = np.mean(all_fold_rmse)
    std_rmse = np.std(all_fold_rmse)
    print(f"\nPriemerné RMSE: {average_rmse:.4f} (+/- {std_rmse:.4f})")

    return models, scalers, all_fold_rmse

def predict_future(latest_data_raw: pd.DataFrame, model: xgb.XGBRegressor, scaler: MinMaxScaler, horizon: int, lags: list, feature_cols: list) -> float | None:
    print("\nGenerujem predikciu pre budúcnosť...")
    if latest_data_raw is None or len(latest_data_raw) == 0:
        print("Chyba: Nedostatok dát pre predikciu.")
        return None

    try:
        df_pred = latest_data_raw.copy()
        df_pred = add_technical_indicators(df_pred)
        df_pred = add_lagged_features(df_pred, lags)
        df_pred.dropna(inplace=True)

        if df_pred.empty:
            print("Chyba: Po feature engineeringu a dropna() nezostali žiadne dáta pre predikciu.")
            return None

        # Use the same feature columns as during training
        X_latest = df_pred[feature_cols].iloc[-1:]

        if X_latest.isnull().values.any():
            print("Chyba: Posledný riadok príznakov obsahuje NaN hodnoty po predspracovaní.")
            return None

        print(f"  Príznaky pre predikciu (pred škálovaním):\n{X_latest}")
        X_latest_scaled = scaler.transform(X_latest)
        print(f"  Príznaky pre predikciu (po škálovaní):\n{X_latest_scaled}")

        prediction = model.predict(X_latest_scaled)
        predicted_value = prediction[0]  # Get first prediction

        print(f"Predikovaná hodnota 'close' o {horizon} minút: {predicted_value:.4f}")
        return float(predicted_value)

    except Exception as e:
        print(f"Neočekávaná chyba pri generovaní predikcie: {e}")
        return None

if __name__ == "__main__":
    conn = connect_db(SQL_CONN_STR)
    if not conn:
        exit("Nepodarilo sa pripojiť k databáze.")

    create_ohlcv_table_if_not_exists(DB_TABLE_NAME, conn)

    start_download_date = '3 days ago UTC'

    try:
        exchange = ccxt.binance({
            'enableRateLimit': True,
        })
    except Exception as e:
        print(f"Chyba pri inicializácii ccxt: {e}")
        conn.close()
        exit()

    new_ohlcv_list = download_historical_data(SYMBOL, TIMEFRAME, start_download_date, exchange)

    if new_ohlcv_list:
        df_new = format_ohlcv_data(new_ohlcv_list)
        if df_new is not None and not df_new.empty:
            # Ensure timestamp is timezone-aware before saving
            if df_new.index.tz is None:
                df_new.index = df_new.index.tz_localize('UTC')
            save_dataframe_to_sql(df_new, DB_TABLE_NAME, conn)

    df_full = load_data_from_sql(DB_TABLE_NAME, conn)
    if df_full is None or df_full.empty:
        print("Žiadne dáta na spracovanie.")
        conn.close()
        exit()

    feature_lags = [1, 2, 3, 4]
    X, y, feature_cols = prepare_data(df_full.copy(), PREDICTION_HORIZON, lags=feature_lags)

    if not X.empty and not y.empty:
        cv_models, cv_scalers, cv_rmse = perform_cross_validation(X, y, N_SPLITS_CV)

    print("\nTrénujem finálny model...")
    train_size = int(len(X) * 0.95)
    X_train_final, y_train_final = X.iloc[:train_size], y.iloc[:train_size]

    final_scaler = MinMaxScaler()
    X_train_final_scaled = final_scaler.fit_transform(X_train_final)

    final_model = xgb.XGBRegressor(**XGB_PARAMS)
    final_model.fit(X_train_final_scaled, y_train_final)
    print("Finálny model natrénovaný.")

    max_lag = max(feature_lags) if feature_lags else 0
    required_history_length = max_lag + 50
    latest_data_raw = df_full.iloc[-required_history_length:]

    final_prediction = predict_future(latest_data_raw, final_model, final_scaler, PREDICTION_HORIZON, feature_lags, feature_cols)

    if final_prediction is not None:
        print(f"\n=== FINÁLNA PREDIKCIA ===")
        print(f"Symbol: {SYMBOL}")
        print(f"Časový rámec: {TIMEFRAME}")
        print(f"Predikčný horizont: {PREDICTION_HORIZON} minút")
        print(f"Posledná známa cena ({latest_data_raw.index[-1]}): {latest_data_raw['close'].iloc[-1]:.4f}")
        print(f"Predikovaná cena o {PREDICTION_HORIZON} minút: {final_prediction:.4f}")

    conn.close()
    print("\nSpojenie s databázou uzavreté.")

