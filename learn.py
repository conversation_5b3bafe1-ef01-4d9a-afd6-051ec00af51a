"""
Modul pro trénování modelu strojového učení k predikci cenových údajů.
Používá XGBoost pro regresi na základě technických indikátorů z SQL databáze.
"""
import logging
from datetime import datetime, timedelta
import numpy as np

import pyodbc
import pandas as pd
# pylint: disable=import-error
# type: ignore
# mypy: ignore-errors
import xgboost as xgb
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib
import matplotlib.pyplot as plt

def setup_logging():
    """Nastavení logování."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_data(conn_str):
    """Načtení dat z databáze."""
    try:
        with pyodbc.connect(conn_str) as conn:
            posledni_datum = datetime.now()
            tyden_zpet = posledni_datum - timedelta(days=14)

            query = f"""
            SELECT * FROM alch_indikatory 
            WHERE timestamp >= '{tyden_zpet.strftime('%Y-%m-%d')}'
            ORDER BY timestamp ASC
            """
            df = pd.read_sql(query, conn)

            logging.info(f"Načteno {len(df)} záznamů pro trénování modelu")
            logging.info(f"Časový rozsah: {df['timestamp'].min()} až {df['timestamp'].max()}")
            return df
    except pyodbc.Error as e:
        logging.error(f"Chyba při připojení k databázi: {e}")
        raise

def prepare_dataset(df):
    """Příprava datasetu pro trénování."""
    features = ["RSI", "EMA9", "EMA20", "boll_high", "boll_low", "ATR"]
    X = df[features]
    y = df["close_price"].shift(-5)
    X = X[:-5]
    y = y[:-5]
    return train_test_split(X, y, test_size=0.2, shuffle=False)

def train_model(X_train, y_train):
    """Trénování modelu s optimalizací parametrů."""
    param_grid = {
        'n_estimators': [100, 200, 500],
        'learning_rate': [0.01, 0.05, 0.1],
        'max_depth': [3, 5, 7],
        'min_child_weight': [1, 3, 5],
        'subsample': [0.7, 0.8, 0.9]
    }
    base_model = xgb.XGBRegressor(objective="reg:squarederror", random_state=42)
    grid_search = GridSearchCV(estimator=base_model, param_grid=param_grid, cv=3, scoring='neg_mean_squared_error', verbose=1, n_jobs=-1)
    logging.info("Hledám optimální parametry modelu...")
    grid_search.fit(X_train, y_train)
    logging.info(f"Nejlepší parametry: {grid_search.best_params_}")
    return grid_search.best_estimator_

def evaluate_model(model, X_test, y_test):
    """Ověření kvality modelu."""
    y_pred = model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    rmse = mse**0.5
    mae = mean_absolute_error(y_test, y_pred)
    logging.info(f"Vypočtené chyby modelu:")
    logging.info(f" - RMSE: {rmse}")
    logging.info(f" - MAE: {mae}")
    return rmse, mae

def save_model(model, filename):
    """Uložení modelu na disk."""
    joblib.dump(model, filename)
    logging.info(f"Model byl uložen do '{filename}'.")

def save_model_info(info, filename):
    """Uložení informací o modelu na disk."""
    with open(filename, 'w') as f:
        for key, value in info.items():
            f.write(f"{key}: {value}\n")
    logging.info(f"Informace o modelu uloženy do '{filename}'")

def plot_feature_importance(model, filename):
    """Zobrazení a uložení grafu důležitosti příznaků."""
    plt.figure(figsize=(10, 6))
    xgb.plot_importance(model)
    plt.savefig(filename)
    logging.info(f"Graf důležitosti příznaků uložen jako '{filename}'")

def plot_prediction_vs_actual(y_test, y_pred, filename):
    """Zobrazení a uložení grafu predikce vs. realita."""
    plt.figure(figsize=(12, 6))
    plt.plot(y_test.values, label='Skutečné ceny')
    plt.plot(y_pred, label='Predikované ceny')
    plt.legend()
    plt.title('Predikce vs. Skutečnost')
    plt.savefig(filename)
    logging.info(f"Graf predikce vs. realita uložen jako '{filename}'")

def main():
    setup_logging()
    conn_str = (
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=192.168.0.100,1433;"
        "DATABASE=byb;"
        "UID=dbuser;"
        "PWD=pochy1249;"
        "TrustServerCertificate=yes;"
        "Encrypt=no;"
    )
    df = load_data(conn_str)
    X_train, X_test, y_train, y_test = prepare_dataset(df)
    best_model = train_model(X_train, y_train)
    rmse, mae = evaluate_model(best_model, X_test, y_test)
    plot_feature_importance(best_model, 'feature_importance.png')
    plot_prediction_vs_actual(y_test, best_model.predict(X_test), 'prediction_vs_actual.png')
    save_model(best_model, "alch_price_model.pkl")
    info = {
        'date_trained': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data_range': f"{df['timestamp'].min()} až {df['timestamp'].max()}",
        'rmse': rmse,
        'mae': mae,
        'num_records': len(df)
    }
    save_model_info(info, 'model_info.txt')

if __name__ == "__main__":
    main()
