"""
Testovací skript pro ověření uklád<PERSON> grafů.
"""
import os
import matplotlib.pyplot as plt
import numpy as np
import logging

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_save_graph():
    """Testuje ukládání grafu do aktuálního adresáře."""
    try:
        # Vytvoření jednoduchého grafu
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        plt.figure(figsize=(8, 6))
        plt.plot(x, y)
        plt.title('Testovací graf')
        plt.xlabel('X')
        plt.ylabel('Y')
        plt.grid(True)
        
        # Získání aktuálního adresáře
        current_dir = os.getcwd()
        logger.info(f"Aktuální adresář: {current_dir}")
        
        # Cesta k souboru
        file_path = os.path.join(current_dir, 'test_graph.png')
        logger.info(f"Ukládám graf do: {file_path}")
        
        # Uložení grafu
        plt.savefig(file_path)
        logger.info(f"Graf úspěšně uložen do: {file_path}")
        
        # Kontrola, zda soubor existuje
        if os.path.exists(file_path):
            logger.info(f"Soubor existuje: {file_path}")
            file_size = os.path.getsize(file_path)
            logger.info(f"Velikost souboru: {file_size} bajtů")
        else:
            logger.error(f"Soubor neexistuje: {file_path}")
        
        # Zkusíme vytvořit adresář a uložit graf tam
        graphs_dir = os.path.join(current_dir, 'graphs')
        if not os.path.exists(graphs_dir):
            os.makedirs(graphs_dir)
            logger.info(f"Vytvořen adresář: {graphs_dir}")
        
        # Cesta k souboru v adresáři graphs
        file_path2 = os.path.join(graphs_dir, 'test_graph2.png')
        logger.info(f"Ukládám graf do: {file_path2}")
        
        # Uložení grafu
        plt.savefig(file_path2)
        logger.info(f"Graf úspěšně uložen do: {file_path2}")
        
        # Kontrola, zda soubor existuje
        if os.path.exists(file_path2):
            logger.info(f"Soubor existuje: {file_path2}")
            file_size = os.path.getsize(file_path2)
            logger.info(f"Velikost souboru: {file_size} bajtů")
        else:
            logger.error(f"Soubor neexistuje: {file_path2}")
        
        # Výpis všech souborů v aktuálním adresáři
        logger.info("Soubory v aktuálním adresáři:")
        for file in os.listdir(current_dir):
            logger.info(f"  {file}")
        
        # Výpis všech souborů v adresáři graphs
        if os.path.exists(graphs_dir):
            logger.info(f"Soubory v adresáři {graphs_dir}:")
            for file in os.listdir(graphs_dir):
                logger.info(f"  {file}")
        
        return True
    except Exception as e:
        logger.error(f"Chyba při ukládání grafu: {e}")
        return False

if __name__ == "__main__":
    print("=== Test ukládání grafů ===")
    success = test_save_graph()
    if success:
        print("Test úspěšně dokončen. Zkontrolujte výpis logů pro detaily.")
    else:
        print("Test selhal. Zkontrolujte výpis logů pro detaily.")
