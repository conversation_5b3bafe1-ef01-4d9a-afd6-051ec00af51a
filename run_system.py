"""
Hlavní skript pro spuštění predikčního systému.
Tento skript slouž<PERSON> jako jed<PERSON>du<PERSON> vstupní bod pro spuštění různých částí systému.
"""
import os
import sys
import logging
import argparse
import subprocess
from datetime import datetime

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"system_log_{datetime.now().strftime('%Y%m%d')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Hlavní funkce pro spuštění predikčního systému."""
    # Zpracování argumentů příkazové řádky
    parser = argparse.ArgumentParser(description='Predikční systém pro kryptoměny')
    parser.add_argument('action', choices=['predict', 'train', 'backtest', 'dashboard', 'update', 'all'],
                        help='Akce k <PERSON>ení')
    parser.add_argument('--days', type=int, default=7,
                        help='Počet dní historie pro backtest nebo dashboard')
    parser.add_argument('--ensemble', action='store_true',
                        help='Použít ensemble model místo standardního modelu')
    parser.add_argument('--use_sentiment', action='store_true',
                        help='Zahrnout sentiment do predikce')
    parser.add_argument('--update_sentiment', action='store_true',
                        help='Aktualizovat data o sentimentu')
    
    args = parser.parse_args()
    
    # Kontrola, zda existují všechny potřebné soubory
    required_files = [
        'combined_script.py',
        'trend_indicators.py',
        'adaptive_learning.py',
        'market_conditions.py',
        'sentiment_analysis.py',
        'integration.py'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        logger.error(f"Chybí následující soubory: {missing_files}")
        return
    
    # Spuštění podle zvolené akce
    if args.action == 'all':
        # Postupné spuštění všech akcí
        run_action('update', args)
        run_action('train', args)
        run_action('predict', args)
        run_action('backtest', args)
        run_action('dashboard', args)
    else:
        # Spuštění jedné akce
        run_action(args.action, args)

def run_action(action, args):
    """
    Spustí zvolenou akci.
    
    Args:
        action: Název akce
        args: Argumenty příkazové řádky
    """
    logger.info(f"Spouštím akci: {action}")
    
    # Sestavení příkazu
    cmd = [sys.executable, 'integration.py', '--mode', action]
    
    # Přidání dalších argumentů
    if action in ['backtest', 'dashboard']:
        cmd.extend(['--days', str(args.days)])
    
    if args.ensemble:
        cmd.append('--ensemble')
    
    if args.use_sentiment:
        cmd.append('--use_sentiment')
    
    if args.update_sentiment:
        cmd.append('--update_sentiment')
    
    # Spuštění příkazu
    try:
        logger.info(f"Spouštím příkaz: {' '.join(cmd)}")
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()
        
        # Výpis výstupu
        if stdout:
            for line in stdout.splitlines():
                logger.info(f"[{action}] {line}")
        
        # Výpis chyb
        if stderr:
            for line in stderr.splitlines():
                logger.error(f"[{action}] {line}")
        
        # Kontrola návratového kódu
        if process.returncode != 0:
            logger.error(f"Akce '{action}' selhala s návratovým kódem {process.returncode}")
        else:
            logger.info(f"Akce '{action}' úspěšně dokončena")
    except Exception as e:
        logger.error(f"Chyba při spouštění akce '{action}': {e}")

if __name__ == "__main__":
    main()
