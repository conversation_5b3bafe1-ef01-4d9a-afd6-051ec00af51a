"""
Modul pro ensemble modely, kter<PERSON> kombinu<PERSON><PERSON> růz<PERSON><PERSON> přístupy k predikci.
Implementuje různé strategie kombinování modelů pro zlepšení přesnosti predikce.
"""
import os
import logging
import pandas as pd
import numpy as np
import joblib
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Any, Union, Optional

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleModel:
    """
    Třída pro ensemble model, který kombinuje různé modely pro zlep<PERSON><PERSON>í predikce.
    """
    
    def __init__(self, models_config: List[Dict[str, Any]] = None, 
                 weights: List[float] = None,
                 ensemble_method: str = 'weighted_average'):
        """
        Inicializace ensemble modelu.
        
        Args:
            models_config: Seznam konfigurací modelů
                Každá konfigurace je slovník s klíči:
                - 'type': Typ modelu ('xgboost', 'random_forest', 'gbm', 'linear', 'ridge', 'lasso')
                - 'params': Parametry modelu
                - 'scaler': Typ scaleru ('standard', 'minmax', None)
            weights: Seznam vah pro jednotlivé modely (musí odpovídat délce models_config)
            ensemble_method: Metoda kombinování predikcí ('weighted_average', 'stacking', 'voting')
        """
        # Výchozí konfigurace modelů, pokud není zadána
        if models_config is None:
            self.models_config = [
                {
                    'type': 'xgboost',
                    'params': {
                        'n_estimators': 200,
                        'learning_rate': 0.05,
                        'max_depth': 5,
                        'subsample': 0.8,
                        'colsample_bytree': 0.8,
                        'tree_method': 'hist',
                        'device': 'cuda' if self._is_gpu_available() else 'cpu'
                    },
                    'scaler': 'minmax'
                },
                {
                    'type': 'random_forest',
                    'params': {
                        'n_estimators': 200,
                        'max_depth': 10,
                        'min_samples_split': 5,
                        'min_samples_leaf': 2,
                        'n_jobs': -1
                    },
                    'scaler': 'standard'
                },
                {
                    'type': 'gbm',
                    'params': {
                        'n_estimators': 150,
                        'learning_rate': 0.1,
                        'max_depth': 4,
                        'subsample': 0.8,
                        'random_state': 42
                    },
                    'scaler': 'standard'
                }
            ]
        else:
            self.models_config = models_config
        
        # Inicializace modelů a scalerů
        self.models = []
        self.scalers = []
        
        # Inicializace vah
        if weights is None:
            # Výchozí váhy - rovnoměrné
            self.weights = [1.0 / len(self.models_config)] * len(self.models_config)
        else:
            if len(weights) != len(self.models_config):
                raise ValueError("Počet vah musí odpovídat počtu modelů")
            # Normalizace vah
            sum_weights = sum(weights)
            self.weights = [w / sum_weights for w in weights]
        
        # Metoda kombinování predikcí
        self.ensemble_method = ensemble_method
        
        # Meta-model pro stacking
        self.meta_model = None
        
        logger.info(f"Inicializován ensemble model s {len(self.models_config)} modely a metodou '{ensemble_method}'")
    
    def _is_gpu_available(self) -> bool:
        """Kontroluje, zda je dostupná GPU."""
        try:
            # Pokus o import torch a kontrolu GPU
            import torch
            return torch.cuda.is_available()
        except ImportError:
            try:
                # Pokus o import cupy
                import cupy
                return True
            except ImportError:
                return False
    
    def _create_model(self, model_config: Dict[str, Any]) -> Tuple[Any, Any]:
        """
        Vytvoří model a scaler podle konfigurace.
        
        Args:
            model_config: Konfigurace modelu
            
        Returns:
            Tuple (model, scaler)
        """
        model_type = model_config['type'].lower()
        params = model_config.get('params', {})
        scaler_type = model_config.get('scaler')
        
        # Vytvoření scaleru
        scaler = None
        if scaler_type == 'standard':
            scaler = StandardScaler()
        elif scaler_type == 'minmax':
            scaler = MinMaxScaler()
        
        # Vytvoření modelu
        if model_type == 'xgboost':
            model = xgb.XGBRegressor(**params)
        elif model_type == 'random_forest':
            model = RandomForestRegressor(**params)
        elif model_type == 'gbm':
            model = GradientBoostingRegressor(**params)
        elif model_type == 'linear':
            model = LinearRegression(**params)
        elif model_type == 'ridge':
            model = Ridge(**params)
        elif model_type == 'lasso':
            model = Lasso(**params)
        else:
            raise ValueError(f"Neznámý typ modelu: {model_type}")
        
        return model, scaler
    
    def fit(self, X: pd.DataFrame, y: pd.Series) -> None:
        """
        Natrénuje ensemble model.
        
        Args:
            X: Trénovací data
            y: Cílové hodnoty
        """
        logger.info(f"Trénuji ensemble model na {len(X)} vzorcích...")
        
        # Resetujeme modely a scalery
        self.models = []
        self.scalers = []
        
        # Trénování jednotlivých modelů
        for i, config in enumerate(self.models_config):
            logger.info(f"Trénuji model {i+1}/{len(self.models_config)}: {config['type']}")
            
            model, scaler = self._create_model(config)
            
            # Škálování dat, pokud je scaler
            X_train = X.copy()
            if scaler is not None:
                X_train = scaler.fit_transform(X)
            
            # Trénování modelu
            model.fit(X_train, y)
            
            self.models.append(model)
            self.scalers.append(scaler)
        
        # Trénování meta-modelu pro stacking
        if self.ensemble_method == 'stacking':
            logger.info("Trénuji meta-model pro stacking...")
            
            # Získání predikcí jednotlivých modelů
            predictions = self._get_base_predictions(X)
            
            # Vytvoření meta-modelu
            self.meta_model = Ridge(alpha=1.0)
            
            # Trénování meta-modelu
            self.meta_model.fit(predictions, y)
        
        logger.info("Ensemble model úspěšně natrénován")
    
    def _get_base_predictions(self, X: pd.DataFrame) -> np.ndarray:
        """
        Získá predikce jednotlivých modelů.
        
        Args:
            X: Data pro predikci
            
        Returns:
            Matice predikcí (n_samples, n_models)
        """
        predictions = np.zeros((len(X), len(self.models)))
        
        for i, (model, scaler) in enumerate(zip(self.models, self.scalers)):
            # Škálování dat, pokud je scaler
            X_pred = X.copy()
            if scaler is not None:
                X_pred = scaler.transform(X)
            
            # Predikce
            predictions[:, i] = model.predict(X_pred)
        
        return predictions
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Provede predikci pomocí ensemble modelu.
        
        Args:
            X: Data pro predikci
            
        Returns:
            Predikce
        """
        if not self.models:
            raise ValueError("Model nebyl natrénován")
        
        # Získání predikcí jednotlivých modelů
        predictions = self._get_base_predictions(X)
        
        # Kombinování predikcí podle zvolené metody
        if self.ensemble_method == 'weighted_average':
            # Vážený průměr predikcí
            final_predictions = np.zeros(len(X))
            for i in range(len(self.models)):
                final_predictions += predictions[:, i] * self.weights[i]
        
        elif self.ensemble_method == 'stacking':
            # Použití meta-modelu pro kombinování predikcí
            if self.meta_model is None:
                raise ValueError("Meta-model nebyl natrénován")
            final_predictions = self.meta_model.predict(predictions)
        
        elif self.ensemble_method == 'voting':
            # Medián predikcí
            final_predictions = np.median(predictions, axis=1)
        
        else:
            raise ValueError(f"Neznámá metoda kombinování predikcí: {self.ensemble_method}")
        
        return final_predictions
    
    def evaluate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """
        Vyhodnotí výkon ensemble modelu.
        
        Args:
            X: Testovací data
            y: Skutečné hodnoty
            
        Returns:
            Slovník s metrikami výkonu
        """
        # Predikce
        y_pred = self.predict(X)
        
        # Výpočet metrik
        rmse = np.sqrt(mean_squared_error(y, y_pred))
        mae = mean_absolute_error(y, y_pred)
        
        # Výpočet procentuální chyby
        mape = np.mean(np.abs((y - y_pred) / y)) * 100
        
        # Výpočet úspěšnosti směru
        direction_actual = np.sign(y.diff().fillna(0))
        direction_pred = np.sign(pd.Series(y_pred).diff().fillna(0))
        direction_accuracy = np.mean(direction_actual == direction_pred) * 100
        
        metrics = {
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_accuracy
        }
        
        logger.info(f"Výkon ensemble modelu: RMSE={rmse:.4f}, MAE={mae:.4f}, MAPE={mape:.2f}%, Směr={direction_accuracy:.2f}%")
        
        return metrics
    
    def save(self, path: str) -> None:
        """
        Uloží ensemble model.
        
        Args:
            path: Cesta k souboru
        """
        model_data = {
            'models_config': self.models_config,
            'weights': self.weights,
            'ensemble_method': self.ensemble_method,
            'models': self.models,
            'scalers': self.scalers,
            'meta_model': self.meta_model
        }
        
        joblib.dump(model_data, path)
        logger.info(f"Ensemble model uložen do {path}")
    
    @classmethod
    def load(cls, path: str) -> 'EnsembleModel':
        """
        Načte ensemble model.
        
        Args:
            path: Cesta k souboru
            
        Returns:
            Načtený ensemble model
        """
        model_data = joblib.load(path)
        
        # Vytvoření instance
        ensemble = cls(
            models_config=model_data['models_config'],
            weights=model_data['weights'],
            ensemble_method=model_data['ensemble_method']
        )
        
        # Nastavení modelů a scalerů
        ensemble.models = model_data['models']
        ensemble.scalers = model_data['scalers']
        ensemble.meta_model = model_data['meta_model']
        
        logger.info(f"Ensemble model načten z {path}")
        return ensemble

class AdaptiveEnsembleModel(EnsembleModel):
    """
    Rozšíření EnsembleModel o adaptivní váhy modelů podle výkonu.
    """
    
    def __init__(self, models_config: List[Dict[str, Any]] = None, 
                 weights: List[float] = None,
                 ensemble_method: str = 'weighted_average',
                 adaptation_rate: float = 0.1):
        """
        Inicializace adaptivního ensemble modelu.
        
        Args:
            models_config: Seznam konfigurací modelů
            weights: Seznam vah pro jednotlivé modely
            ensemble_method: Metoda kombinování predikcí
            adaptation_rate: Rychlost adaptace vah (0-1)
        """
        super().__init__(models_config, weights, ensemble_method)
        
        self.adaptation_rate = adaptation_rate
        self.model_errors = [0.0] * len(self.models_config)
        
        logger.info(f"Inicializován adaptivní ensemble model s rychlostí adaptace {adaptation_rate}")
    
    def update_weights(self, X: pd.DataFrame, y: pd.Series) -> None:
        """
        Aktualizuje váhy modelů podle jejich výkonu.
        
        Args:
            X: Validační data
            y: Skutečné hodnoty
        """
        if not self.models:
            raise ValueError("Model nebyl natrénován")
        
        logger.info("Aktualizuji váhy modelů...")
        
        # Získání predikcí jednotlivých modelů
        predictions = self._get_base_predictions(X)
        
        # Výpočet chyb jednotlivých modelů
        for i in range(len(self.models)):
            error = mean_squared_error(y, predictions[:, i])
            
            # Aktualizace průměrné chyby modelu
            if self.model_errors[i] == 0.0:
                self.model_errors[i] = error
            else:
                self.model_errors[i] = (1 - self.adaptation_rate) * self.model_errors[i] + self.adaptation_rate * error
        
        # Výpočet nových vah (inverzně proporcionálních k chybám)
        if all(error > 0 for error in self.model_errors):
            inverse_errors = [1.0 / error for error in self.model_errors]
            sum_inverse_errors = sum(inverse_errors)
            
            if sum_inverse_errors > 0:
                self.weights = [error / sum_inverse_errors for error in inverse_errors]
        
        logger.info(f"Aktualizované váhy modelů: {self.weights}")
    
    def fit(self, X: pd.DataFrame, y: pd.Series, validation_split: float = 0.2) -> None:
        """
        Natrénuje adaptivní ensemble model.
        
        Args:
            X: Trénovací data
            y: Cílové hodnoty
            validation_split: Podíl dat pro validaci a aktualizaci vah
        """
        # Rozdělení dat na trénovací a validační
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # Trénování základních modelů
        super().fit(X_train, y_train)
        
        # Aktualizace vah podle výkonu na validačních datech
        if len(X_val) > 0:
            self.update_weights(X_val, y_val)

def create_ensemble_for_market_condition(market_condition: str) -> EnsembleModel:
    """
    Vytvoří ensemble model optimalizovaný pro dané tržní podmínky.
    
    Args:
        market_condition: Tržní podmínky
        
    Returns:
        Ensemble model
    """
    # Základní konfigurace modelů
    base_config = [
        {
            'type': 'xgboost',
            'params': {
                'n_estimators': 200,
                'learning_rate': 0.05,
                'max_depth': 5,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'tree_method': 'hist',
                'device': 'cuda' if EnsembleModel()._is_gpu_available() else 'cpu'
            },
            'scaler': 'minmax'
        },
        {
            'type': 'random_forest',
            'params': {
                'n_estimators': 200,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'n_jobs': -1
            },
            'scaler': 'standard'
        },
        {
            'type': 'gbm',
            'params': {
                'n_estimators': 150,
                'learning_rate': 0.1,
                'max_depth': 4,
                'subsample': 0.8,
                'random_state': 42
            },
            'scaler': 'standard'
        }
    ]
    
    # Úprava konfigurace podle tržních podmínek
    if 'volatile' in market_condition:
        # Pro volatilní trh zvýšíme regularizaci a snížíme learning rate
        base_config[0]['params']['learning_rate'] = 0.03
        base_config[0]['params']['reg_alpha'] = 0.5
        base_config[0]['params']['reg_lambda'] = 1.0
        
        base_config[2]['params']['learning_rate'] = 0.05
        base_config[2]['params']['subsample'] = 0.7
        
        # Přidáme lineární model, který může být stabilnější v volatilních podmínkách
        base_config.append({
            'type': 'ridge',
            'params': {
                'alpha': 1.0
            },
            'scaler': 'standard'
        })
        
        # Váhy modelů - větší důraz na stabilnější modely
        weights = [0.2, 0.3, 0.2, 0.3]
        
    elif 'stable' in market_condition:
        # Pro stabilní trh zvýšíme kapacitu modelů
        base_config[0]['params']['n_estimators'] = 300
        base_config[0]['params']['max_depth'] = 6
        
        base_config[1]['params']['n_estimators'] = 300
        base_config[1]['params']['max_depth'] = 12
        
        base_config[2]['params']['n_estimators'] = 200
        
        # Váhy modelů - větší důraz na komplexnější modely
        weights = [0.4, 0.3, 0.3]
        
    elif market_condition == 'breakout':
        # Pro průlomy potřebujeme rychlejší reakci
        base_config[0]['params']['learning_rate'] = 0.1
        base_config[0]['params']['max_depth'] = 6
        
        base_config[2]['params']['learning_rate'] = 0.15
        
        # Váhy modelů - větší důraz na reaktivnější modely
        weights = [0.4, 0.2, 0.4]
        
    elif market_condition == 'reversal':
        # Pro obraty trendu potřebujeme vyšší přesnost
        base_config[0]['params']['n_estimators'] = 300
        base_config[0]['params']['learning_rate'] = 0.03
        
        base_config[1]['params']['n_estimators'] = 300
        base_config[1]['params']['min_samples_split'] = 10
        
        # Přidáme Lasso pro lepší výběr příznaků
        base_config.append({
            'type': 'lasso',
            'params': {
                'alpha': 0.01
            },
            'scaler': 'standard'
        })
        
        # Váhy modelů - rovnoměrnější rozložení
        weights = [0.3, 0.3, 0.2, 0.2]
        
    else:
        # Výchozí váhy
        weights = [0.4, 0.3, 0.3]
    
    # Vytvoření ensemble modelu
    if 'volatile' in market_condition:
        # Pro volatilní trh použijeme adaptivní ensemble
        ensemble = AdaptiveEnsembleModel(
            models_config=base_config,
            weights=weights,
            ensemble_method='weighted_average',
            adaptation_rate=0.2
        )
    else:
        # Pro ostatní podmínky standardní ensemble
        ensemble = EnsembleModel(
            models_config=base_config,
            weights=weights,
            ensemble_method='weighted_average'
        )
    
    logger.info(f"Vytvořen ensemble model pro tržní podmínky '{market_condition}'")
    return ensemble

if __name__ == "__main__":
    # Test funkcionality
    import numpy as np
    
    # Vytvoření testovacích dat
    np.random.seed(42)
    X = pd.DataFrame(np.random.rand(1000, 5), columns=[f'feature_{i}' for i in range(5)])
    y = pd.Series(np.sin(X['feature_0'] * 5) + 0.1 * np.random.randn(1000))
    
    # Rozdělení na trénovací a testovací data
    train_size = int(0.8 * len(X))
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    # Test standardního ensemble modelu
    print("Test standardního ensemble modelu:")
    ensemble = EnsembleModel()
    ensemble.fit(X_train, y_train)
    metrics = ensemble.evaluate(X_test, y_test)
    
    # Test adaptivního ensemble modelu
    print("\nTest adaptivního ensemble modelu:")
    adaptive_ensemble = AdaptiveEnsembleModel(adaptation_rate=0.2)
    adaptive_ensemble.fit(X_train, y_train, validation_split=0.2)
    adaptive_metrics = adaptive_ensemble.evaluate(X_test, y_test)
    
    # Test ensemble modelu pro specifické tržní podmínky
    print("\nTest ensemble modelu pro volatilní trh:")
    volatile_ensemble = create_ensemble_for_market_condition('volatile_bullish')
    volatile_ensemble.fit(X_train, y_train)
    volatile_metrics = volatile_ensemble.evaluate(X_test, y_test)
    
    # Uložení a načtení modelu
    ensemble.save('ensemble_model_test.pkl')
    loaded_ensemble = EnsembleModel.load('ensemble_model_test.pkl')
    loaded_metrics = loaded_ensemble.evaluate(X_test, y_test)
    
    print("\nMetriky načteného modelu:")
    for key, value in loaded_metrics.items():
        print(f"  {key}: {value}")
