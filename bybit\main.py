# main.py
# <PERSON><PERSON><PERSON> loop, k<PERSON> bezi v konzoli a vse ridi

import time
from config import LOOP_INTERVAL, LOOKBACK, PREDICTION_HORIZON
from db import create_tables, insert_klines, fetch_klines, insert_predictions, update_real_prices
from bybit_data import get_klines
from lstm_predict import predict_future_lstm
from chaos_predict import predict_future_chaos
from chaos_predict import predict_future_knn
from vizualizace import plot_future_predictions

if __name__ == '__main__':
    create_tables()
    while True:
        try:
            # 1. Stahni nove svicky z Bybitu
            new_df = get_klines()
            # 2. Uloz do DB
            insert_klines(new_df)
            # 3. Nacti data z DB
            df_sql = fetch_klines(limit=60)
            # Kontrola poctu dat
            if len(df_sql) < (LOOKBACK + PREDICTION_HORIZON + 1):
                print("Malo dat v DB pro predikci, cekam dalsi interval...")
                time.sleep(LOOP_INTERVAL)
                continue
            # 4. Vypocitej predikci do budoucna
            future_lstm = predict_future_lstm(df_sql, lookback=LOOKBACK, future_steps=PREDICTION_HORIZON)
            future_knn = predict_future_knn(df_sql, future_steps=PREDICTION_HORIZON)
            future_chaos = predict_future_chaos(df_sql, future_steps=PREDICTION_HORIZON)
            # 5. Uloz predikce do DB
            timestamp_predikce = int(df_sql['timestamp'].iloc[-1])
            insert_predictions(timestamp_predikce, future_lstm, future_chaos)
            # 6. Aktualizuj skutecne ceny v predikcich
            update_real_prices()
            # 7. Vykresli graf – zde si nastav, kolik hodin/minut chces zobrazit
            plot_future_predictions(
                df_sql,
                future_knn,
                future_chaos,
                cooldown_seconds=LOOP_INTERVAL,
                poslednich_svic=60
            )
            print("Loop hotovy, cekam dalsi interval...\n")
        except Exception as e:
            print("Chyba v loopu:", e)
        time.sleep(LOOP_INTERVAL)

