#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Skript pro krátkodobou predikci ceny kryptoměny ALCHUSDT (5 minutový horizont).
Stahuje data z Binance, ukládá do SQL Serveru, trénuje XGBoost model a generuje predikci.

*** DŮLEŽITÉ UPOZORNĚNÍ ***
Predikce cen kryptoměn je vysoce spekulativní a riziková.
Tento skript je pouze pro vzdělávací účely.
Výsledky nemusí být přesné a model má svá omezení.
NEPOUŽÍVEJTE TENTO SKRIPT PRO REÁLNÉ OBCHODOVÁNÍ BEZ DŮKLADNÉ ANALÝZY RIZIK!
Toto není finanční poradenství.
"""

import os
import sys
import time
import pyodbc
import pandas as pd
import numpy as na
from dotenv import load_dotenv
from datetime import datetime, timedelta, timezone
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
import xgboost as xgb
import ccxt
import traceback
import ta

# ANSI color codes for output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

# --- 1. Nastavení a Importy ---
load_dotenv()

# Globální konstanty
SYMBOL = 'SOLUSDT'
SYMBOL_DB = 'SOLUSDT'
TIMEFRAME = '1m'
PREDICTION_HORIZON = 3
DB_TABLE_NAME = f'{SYMBOL_DB}_OHLCV_{TIMEFRAME}'
BINANCE_API_LIMIT = 1000
RATE_LIMIT_DELAY = 0.5
N_SPLITS_CV = 5
LAG_FEATURES_LIST = [1, 2, 3, 4, 5, 10, 15, 20]

XGB_PARAMS = {
    'objective': 'reg:squarederror',
    'n_estimators': 300,  # Více stromů
    'learning_rate': 0.01,  # Nižší learning rate
    'max_depth': 6,  # Hlubší stromy
    'subsample': 0.9,  # Více dat pro každý strom
    'colsample_bytree': 0.9,  # Více features pro každý strom
    'reg_alpha': 0.1,  # L1 regularizace
    'reg_lambda': 0.1,  # L2 regularizace
    'random_state': 42,
    'n_jobs': -1
}



# --- 2. Databázové Funkce ---

conn_str = (
    "DRIVER={SQL Server};"
    "SERVER=GPPC\\SQLGPPC;"
    "DATABASE=byb;"
    "Trusted_Connection=yes;"
)

def connect_db(conn_str):
    return pyodbc.connect(conn_str)

def create_ohlcv_table_if_not_exists(table_name: str, conn: pyodbc.Connection):
    """Vytvoří tabulku pro OHLCV data, pokud neexistuje."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute(f"""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                       WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = ?)
        BEGIN
            CREATE TABLE {table_name} (
                timestamp_utc DATETIME2 PRIMARY KEY,
                open_price FLOAT,
                high_price FLOAT,
                low_price FLOAT,
                close_price FLOAT,
                volume_amt FLOAT
            );
            PRINT 'Tabulka {table_name} byla úspěšně vytvořena.';
        END
        """, (table_name,))
        conn.commit()
        print(f"Ověřeno/vytvořeno: Tabulka '{table_name}'.")
    except pyodbc.Error as e:
        print(f"Chyba při vytváření/ověřování tabulky '{table_name}': {e}")
        conn.rollback()
    finally:
        if cursor:
            cursor.close()

def create_predictions_table(conn: pyodbc.Connection):
    """Vytvoří tabulku pro ukládání predikcí."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES
                       WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'PREDICTIONS')
        BEGIN
            CREATE TABLE PREDICTIONS (
                prediction_id INT IDENTITY(1,1) PRIMARY KEY,
                symbol VARCHAR(20),
                prediction_time DATETIME2,
                target_time DATETIME2,
                predicted_price FLOAT,
                actual_price FLOAT NULL,
                prediction_error FLOAT NULL,
                prediction_error_pct FLOAT NULL,
                horizon_minutes INT
            );
            PRINT 'Tabulka PREDICTIONS byla úspěšně vytvořena.';
        END
        """)
        conn.commit()
        print("Ověřeno/vytvořeno: Tabulka 'PREDICTIONS'.")
    except pyodbc.Error as e:
        print(f"Chyba při vytváření tabulky PREDICTIONS: {e}")
        conn.rollback()
    finally:
        if cursor:
            cursor.close()

def save_prediction(conn: pyodbc.Connection, symbol: str, prediction_time: datetime,
                   target_time: datetime, predicted_price: float, horizon_minutes: int):
    """Uloží predikci do databáze."""
    cursor = None
    try:
        cursor = conn.cursor()
        # Přidáme identifikátor modelu pro rozlišení
        model_identifier = "XGBOOST_BASIC"

        # Zkusíme vložit s model_type, pokud sloupec neexistuje, vložíme bez něj
        try:
            cursor.execute("""
            INSERT INTO PREDICTIONS (symbol, prediction_time, target_time, predicted_price, horizon_minutes, model_type)
            VALUES (?, ?, ?, ?, ?, ?)
            """, (symbol, prediction_time, target_time, predicted_price, horizon_minutes, model_identifier))
        except:
            # Fallback pokud sloupec model_type neexistuje
            cursor.execute("""
            INSERT INTO PREDICTIONS (symbol, prediction_time, target_time, predicted_price, horizon_minutes)
            VALUES (?, ?, ?, ?, ?)
            """, (symbol, prediction_time, target_time, predicted_price, horizon_minutes))
        conn.commit()
        print(f"Predikce uložena do databáze.")
    except pyodbc.Error as e:
        print(f"Chyba při ukládání predikce: {e}")
        conn.rollback()
    finally:
        if cursor:
            cursor.close()

def update_prediction_results(conn: pyodbc.Connection):
    """Aktualizuje skutečné hodnoty a chyby pro předchozí predikce."""
    cursor = None
    try:
        cursor = conn.cursor()

        # Nejprve zkontrolujeme kolik predikcí čeká na aktualizaci
        cursor.execute("""
        SELECT COUNT(*)
        FROM PREDICTIONS p
        WHERE p.actual_price IS NULL
        AND p.target_time <= GETUTCDATE()
        """)
        pending_count = cursor.fetchone()[0]
        print(f"Počet predikcí čekajících na aktualizaci: {pending_count}")

        # Aktualizujeme skutečné hodnoty
        cursor.execute("""
        UPDATE p
        SET
            actual_price = o.close_price,
            prediction_error = ABS(o.close_price - p.predicted_price),
            prediction_error_pct = ABS((o.close_price - p.predicted_price) / p.predicted_price * 100)
        FROM PREDICTIONS p
        INNER JOIN SOLUSDT_OHLCV_1m o
            ON DATEADD(SECOND, -30, p.target_time) <= o.timestamp_utc
            AND o.timestamp_utc <= DATEADD(SECOND, 30, p.target_time)
        WHERE p.actual_price IS NULL
            AND p.target_time <= GETUTCDATE()
        """)

        rows_affected = cursor.rowcount
        conn.commit()
        print(f"Aktualizováno {rows_affected} predikcí skutečnými hodnotami.")

        # Místo kontroly pouze zbývajících, zobrazíme statistiky za posledních 24h
        cursor.execute("""
        SELECT
            prediction_time,
            target_time,
            predicted_price,
            actual_price,
            ABS(ISNULL(actual_price - predicted_price, 0)) as error,
            ISNULL(ABS((actual_price - predicted_price) / predicted_price * 100), 0) as prediction_error_pct
        FROM PREDICTIONS
        WHERE prediction_time >= DATEADD(hour, -24, GETUTCDATE())
        ORDER BY prediction_time DESC
        """)
        historical = cursor.fetchall()

        if historical:
            print("\nVýsledky predikcí za posledních 24 hodin:")
            print(f"{'Čas predikce':^19} | {'Cílový čas':^19} | {'Predikce':^10} | {'Skutečná':^10} | {'Chyba':^8} | {'Chyba %':^8} | Status")
            print("-" * 95)

            for row in historical:
                # Převedeme stringy na datetime objekty pokud je potřeba
                pred_time_obj = row[0]
                target_time_obj = row[1]

                if isinstance(pred_time_obj, str):
                    pred_time_obj = pd.to_datetime(pred_time_obj)
                if isinstance(target_time_obj, str):
                    target_time_obj = pd.to_datetime(target_time_obj)

                pred_time = pred_time_obj.strftime('%Y-%m-%d %H:%M')
                target_time = target_time_obj.strftime('%Y-%m-%d %H:%M')
                pred_price = f"{row[2]:.4f}" if row[2] is not None else "N/A"
                actual = f"{row[3]:.4f}" if row[3] is not None else "čeká"
                error = f"{row[4]:.4f}" if row[4] is not None and row[3] is not None else "N/A"
                error_pct = f"{row[5]:.2f}%" if row[5] is not None and row[3] is not None else "N/A"

                # Barevné označení podle chyby
                if row[3] is None:
                    status = f"{Colors.BLUE}ČEKÁ{Colors.ENDC}"
                elif row[4] < 0.1:  # Výborná přesnost
                    status = f"{Colors.GREEN}PŘESNÁ{Colors.ENDC}"
                elif row[4] < 0.5:  # Dobrá přesnost
                    status = f"{Colors.YELLOW}DOBRÁ{Colors.ENDC}"
                else:  # Vysoká chyba
                    status = f"{Colors.RED}NEPŘESNÁ{Colors.ENDC}"

                print(f"{pred_time:^19} | {target_time:^19} | {pred_price:^10} | {actual:^10} | {error:^8} | {error_pct:^8} | {status}")

            # Souhrnné statistiky
            completed = [r for r in historical if r[3] is not None]
            if completed:
                avg_error = sum(r[4] for r in completed if r[4] is not None) / len(completed)
                avg_error_pct = sum(r[5] for r in completed if r[5] is not None) / len(completed)
                print(f"\nStatistiky:")
                print(f"Celkem predikcí: {len(historical)}")
                print(f"Dokončených: {len(completed)}")
                print(f"Průměrná chyba: {avg_error:.4f}")
                print(f"Průměrná procentuální chyba: {avg_error_pct:.2f}%")
        else:
            print("Žádné predikce za posledních 24 hodin.")

    except pyodbc.Error as e:
        print(f"Chyba při aktualizaci výsledků: {e}")
        conn.rollback()
    finally:
        if cursor:
            cursor.close()

def get_latest_timestamp_from_db(table_name: str, conn: pyodbc.Connection) -> datetime | None:
    """Získá poslední časovou známku z databáze (UTC)."""
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT MAX(timestamp_utc) FROM {table_name}")
        result = cursor.fetchone()
        if result and result[0]:
            latest_ts = result[0]
            # Pokud je výsledek string, převedeme ho na datetime
            if isinstance(latest_ts, str):
                latest_ts = pd.to_datetime(latest_ts)
            # Pokud je to pandas Timestamp, převedeme na datetime
            if hasattr(latest_ts, 'to_pydatetime'):
                latest_ts = latest_ts.to_pydatetime()
            # Zajistíme UTC timezone
            if latest_ts.tzinfo is None:
                latest_ts = latest_ts.replace(tzinfo=timezone.utc)
            else:
                latest_ts = latest_ts.astimezone(timezone.utc)
            print(f"Poslední timestamp v DB: {latest_ts}")
            return latest_ts
        else:
            print(f"V tabulce '{table_name}' nejsou žádná data.")
            return None
    except pyodbc.Error as e:
        print(f"Chyba při získávání posledního timestampu z '{table_name}': {e}")
        return None
    except Exception as e:
        print(f"Chyba při zpracování timestampu: {e}")
        return None
    finally:
        if cursor:
            cursor.close()

def save_dataframe_to_sql(df: pd.DataFrame, table_name: str, conn: pyodbc.Connection) -> bool:
    """Uloží DataFrame do SQL pomocí executemany (neošetřuje duplicity)."""
    if df.empty:
        print("DataFrame pro uložení je prázdný, neukládám nic.")
        return True
    cursor = None
    try:
        cursor = conn.cursor()
        data_tuples = [tuple(x) for x in df.reset_index().to_numpy()]
        sql = f"INSERT INTO {table_name} (timestamp_utc, open_price, high_price, low_price, close_price, volume_amt) VALUES (?,?,?,?,?,?)"
        try:
            cursor.fast_executemany = True
            print("Používám fast_executemany pro zápis do DB.")
        except AttributeError:
            print("fast_executemany není podporováno.")
        cursor.executemany(sql, data_tuples)
        conn.commit()
        print(f"Úspěšně uloženo {len(data_tuples)} záznamů do tabulky '{table_name}'.")
        return True
    except pyodbc.Error as e:
        print(f"Chyba při ukládání DataFrame do SQL tabulky '{table_name}': {e}")
        if conn: conn.rollback()
        return False
    finally:
        if cursor: cursor.close()

def load_data_from_sql(table_name: str, conn: pyodbc.Connection, start_date: datetime | None = None) -> pd.DataFrame | None:
    """Načte data z SQL do DataFrame (seřazené, index timestamp UTC)."""
    cursor = None
    try:
        cursor = conn.cursor()
        sql = f"SELECT timestamp_utc, open_price, high_price, low_price, close_price, volume_amt FROM {table_name}"
        params = []
        if start_date:
            if start_date.tzinfo is None: start_date = start_date.replace(tzinfo=timezone.utc)
            else: start_date = start_date.astimezone(timezone.utc)
            sql += " WHERE timestamp_utc >=?"
            params.append(start_date)
        sql += " ORDER BY timestamp_utc ASC"
        cursor.execute(sql, params)
        rows = cursor.fetchall()
        if not rows:
            print(f"Nenalezena žádná data v tabulce '{table_name}'" + (f" od {start_date}." if start_date else "."))
            return pd.DataFrame(columns=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'], index=pd.to_datetime([]).tz_localize('UTC'))
        df = pd.DataFrame.from_records(rows, columns=[desc[0] for desc in cursor.description])
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
        if df['timestamp_utc'].dt.tz is None: df['timestamp_utc'] = df['timestamp_utc'].dt.tz_localize('UTC')
        else: df['timestamp_utc'] = df['timestamp_utc'].dt.tz_convert('UTC')
        df.set_index('timestamp_utc', inplace=True)
        for col in ['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        print(f"Úspěšně načteno {len(df)} záznamů z tabulky '{table_name}'" + (f" od {start_date}." if start_date else "."))
        return df
    except pyodbc.Error as e:
        print(f"Chyba při načítání dat z SQL tabulky '{table_name}': {e}")
        return None
    except Exception as e:
        print(f"Neočekávaná chyba při zpracování dat z SQL: {e}")
        return None
    finally:
        if cursor: cursor.close()

# --- 3. Získávání a Formátování Dat ---

def download_historical_data(symbol: str, timeframe: str, start_date_str: str, exchange: ccxt.Exchange) -> list:
    """Stáhne historická OHLCV data z Binance pomocí ccxt."""
    all_ohlcv = []
    try:
        start_timestamp_ms = exchange.parse8601(start_date_str + 'Z')
        print(f"Stahování dat pro {symbol} od {start_date_str} UTC...")
        while True:
            print(f"  Stahuji od timestampu: {start_timestamp_ms} ({exchange.iso8601(start_timestamp_ms)})")
            try:
                ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=start_timestamp_ms, limit=BINANCE_API_LIMIT)
            except ccxt.RateLimitExceeded as e:
                print(f"Chyba RateLimitExceeded: {e}. Čekám a zkouším znovu...")
                time.sleep(exchange.rateLimit / 1000 * 2)
                continue
            except ccxt.NetworkError as e:
                print(f"Chyba sítě: {e}. Čekám a zkouším znovu...")
                time.sleep(5)
                continue
            except ccxt.ExchangeError as e:
                print(f"Chyba burzy: {e}. Ukončuji stahování.")
                break
            except Exception as e:
                print(f"Neočekávaná chyba při stahování: {e}. Ukončuji.")
                break

            if not ohlcv:
                print("  API nevrátilo žádná další data.")
                break

            first_candle = ohlcv[0] if ohlcv else None
            last_candle = ohlcv[-1] if ohlcv else None

            if first_candle and last_candle:
                print(f"  Staženo {len(ohlcv)} svíček. Od {exchange.iso8601(first_candle[0])} do {exchange.iso8601(last_candle[0])}")

            # Použití množiny pro odstranění duplicit na základě timestampu
            seen_timestamps = {candle[0] for candle in all_ohlcv}
            new_candles = [candle for candle in ohlcv if candle[0] not in seen_timestamps]
            all_ohlcv.extend(new_candles)

            if last_candle:
                timeframe_duration_ms = exchange.parse_timeframe(timeframe) * 1000
                next_start_timestamp_ms = last_candle[0] + timeframe_duration_ms
                if next_start_timestamp_ms <= last_candle[0]:
                    print("  Detekován konec dat.")
                    break
                start_timestamp_ms = next_start_timestamp_ms

            if len(ohlcv) < BINANCE_API_LIMIT:
                print("  API vrátilo méně než limit, předpokládám konec historie.")
                break

            time.sleep(RATE_LIMIT_DELAY)

    except ValueError as e:
        print(f"Chyba při parsování data '{start_date_str}': {e}")
    except Exception as e:
        print(f"Obecná chyba v download_historical_data: {e}")

    print(f"Celkem staženo {len(all_ohlcv)} záznamů pro {symbol}.")
    return all_ohlcv

def format_ohlcv_data(ohlcv_list: list) -> pd.DataFrame | None:
    """Převede seznam OHLCV dat na Pandas DataFrame (index timestamp UTC)."""
    if not ohlcv_list:
        print("Formátování dat: Vstupní seznam je prázdný.")
        return pd.DataFrame(columns=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'], index=pd.to_datetime([]).tz_localize('UTC'))
    try:
        df = pd.DataFrame(ohlcv_list, columns=['timestamp_utc', 'open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'])
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'], unit='ms', utc=True)
        df.set_index('timestamp_utc', inplace=True)
        for col in ['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df.dropna(subset=['open_price', 'high_price', 'low_price', 'close_price', 'volume_amt'], inplace=True)
        df.sort_index(inplace=True)
        print(f"Formátování dat: Úspěšně vytvořen DataFrame s {len(df)} záznamy.")
        return df
    except Exception as e:
        print(f"Chyba při formátování OHLCV dat: {e}")
        return None

# --- 4. Příprava Dat a Feature Engineering ---

def add_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Přidá technické indikátory pomocí knihovny ta."""
    try:
        print("Přidávám technické indikátory...")

        # Kratší SMA pro lepší pokrytí koncových dat
        df['sma_3'] = ta.trend.sma_indicator(df['close_price'], window=3)
        df['sma_5'] = ta.trend.sma_indicator(df['close_price'], window=5)

        # EMA pro lepší reaktivitu
        df['ema_3'] = ta.trend.ema_indicator(df['close_price'], window=3)
        df['ema_5'] = ta.trend.ema_indicator(df['close_price'], window=5)

        # Momentum indikátory s kratším obdobím
        df['rsi_6'] = ta.momentum.rsi(df['close_price'], window=6)
        df['rsi_8'] = ta.momentum.rsi(df['close_price'], window=8)

        # MACD with specified periods
        try:
            macd_line = ta.trend.macd(df['close_price'], window_slow=26, window_fast=12)
            macd_signal = ta.trend.macd_signal(df['close_price'], window_slow=26, window_fast=12, window_sign=9)
            macd_hist = ta.trend.macd_diff(df['close_price'], window_slow=26, window_fast=12, window_sign=9)
            df['macd'] = macd_line
            df['macd_signal'] = macd_signal
            df['macd_hist'] = macd_hist
        except Exception as e:
            print(f"VAROVÁNÍ: Chyba při výpočtu MACD: {e}")
            df['macd'] = 0
            df['macd_signal'] = 0
            df['macd_hist'] = 0

        # Volatilita
        df['atr_5'] = ta.volatility.average_true_range(df['high_price'], df['low_price'], df['close_price'], window=5)
        df['atr_14'] = ta.volatility.average_true_range(df['high_price'], df['low_price'], df['close_price'], window=14)

        # Bollinger Bands
        bb_high = ta.volatility.bollinger_hband(df['close_price'], window=20)
        bb_low = ta.volatility.bollinger_lband(df['close_price'], window=20)
        bb_mid = ta.volatility.bollinger_mavg(df['close_price'], window=20)
        df['bb_high'] = bb_high
        df['bb_low'] = bb_low
        df['bb_mid'] = bb_mid
        df['bb_width'] = (bb_high - bb_low) / bb_mid
        df['bb_position'] = (df['close_price'] - bb_low) / (bb_high - bb_low)

        # Price action
        df['close_pct_change'] = df['close_price'].pct_change().fillna(0)
        df['volume_pct_change'] = df['volume_amt'].pct_change().fillna(0)
        df['high_low_pct'] = ((df['high_price'] - df['low_price']) / df['close_price']).fillna(0)
        df['open_close_pct'] = ((df['close_price'] - df['open_price']) / df['open_price']).fillna(0)

        # Základní statistiky
        df['price_std_3'] = df['close_price'].rolling(window=3).std().fillna(0)
        df['price_std_10'] = df['close_price'].rolling(window=10).std().fillna(0)
        df['volume_std_3'] = df['volume_amt'].rolling(window=3).std().fillna(0)
        df['volume_std_10'] = df['volume_amt'].rolling(window=10).std().fillna(0)

        # Momentum indikátory
        df['roc_5'] = ta.momentum.roc(df['close_price'], window=5)
        df['roc_10'] = ta.momentum.roc(df['close_price'], window=10)

        # Stochastic
        df['stoch_k'] = ta.momentum.stoch(df['high_price'], df['low_price'], df['close_price'], window=14)
        df['stoch_d'] = ta.momentum.stoch_signal(df['high_price'], df['low_price'], df['close_price'], window=14)

        # Forward fill na NaN hodnoty pro zajištění kontinuity dat
        df.ffill(inplace=True)
        # Backward fill pro případné zbývající NaN na začátku
        df.bfill(inplace=True)
        # Použít 0 pro jakékoliv zbývající NaN hodnoty
        df.fillna(0, inplace=True)

        print("Technické indikátory přidány.")
    except Exception as e:
        print(f"Chyba při přidávání technických indikátorů: {e}")
        print(f"Dostupné sloupce: {df.columns.tolist()}")
        # Zajistíme, že všechny požadované sloupce existují, i když budou nulové
        required_columns = ['macd', 'macd_signal', 'macd_hist']
        for col in required_columns:
            if col not in df.columns:
                df[col] = 0
    return df

def add_lagged_features(df: pd.DataFrame, lags: list) -> pd.DataFrame:
    """Přidá zpožděné příznaky pro 'close_price' a 'volume_amt'."""
    try:
        print(f"Přidávám zpožděné příznaky pro lagy: {lags}...")
        for lag in lags:
            if lag > 0:
                df[f'close_lag_{lag}'] = df['close_price'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume_amt'].shift(lag)
        print("Zpožděné příznaky přidány.")
    except Exception as e:
        print(f"Chyba při přidávání zpožděných příznaků: {e}")
    return df

def add_target_variable(df: pd.DataFrame, horizon: int) -> pd.DataFrame:
    """Přidá cílovou proměnnou 'target' (budoucí cena 'close_price')."""
    try:
        print(f"Přidávám cílovou proměnnou (close_price posunuté o -{horizon} period)...")
        df['target'] = df['close_price'].shift(-horizon)
        print("Cílová proměnná přidána.")
    except Exception as e:
        print(f"Chyba při přidávání cílové proměnné: {e}")
    return df

def prepare_data(df: pd.DataFrame, horizon: int, lags: list) -> tuple:
    """Kompletní příprava dat: indikátory, lagy, cíl, odstranění NaN."""
    print("Zahajuji přípravu dat pro model...")
    if df.empty:
        print("Chyba: Vstupní DataFrame pro prepare_data je prázdný.")
        return None, None, None
    df_processed = df.copy()
    df_processed = add_technical_indicators(df_processed)
    df_processed = add_lagged_features(df_processed, lags)
    df_processed = add_target_variable(df_processed, horizon)
    initial_rows = len(df_processed)
    df_processed.dropna(inplace=True)
    final_rows = len(df_processed)
    print(f"Odstraněno {initial_rows - final_rows} řádků s NaN hodnotami.")
    if df_processed.empty:
        print("Chyba: Po odstranění NaN nezůstala žádná data.")
        return None, None, None
    try:
        y = df_processed['target']
        X = df_processed.drop(columns=['target'])
        feature_names = X.columns.tolist()
        print(f"Data připravena. Počet příznaků: {len(feature_names)}, Počet vzorků: {len(X)}")
        return X, y, feature_names
    except KeyError:
        print("Chyba: Sloupec 'target' nebyl nalezen.")
        return None, None, None
    except Exception as e:
        print(f"Neočekávaná chyba při oddělování X a y: {e}")
        return None, None, None

# --- 5. Trénování a Predikce ---

def train_final_model(X: pd.DataFrame, y: pd.Series, xgb_params: dict) -> tuple:
    """Natrénuje finální XGBoost model a MinMaxScaler."""
    print("Zahajuji trénování finálního modelu...")
    if X.empty or y.empty:
        print("Chyba: Prázdná vstupní data (X nebo y) pro trénování.")
        return None, None
    try:
        split_index = int(len(X) * 0.95)
        X_train_final, X_test_final = X.iloc[:split_index], X.iloc[split_index:]
        y_train_final, y_test_final = y.iloc[:split_index], y.iloc[split_index:]
        if X_train_final.empty or y_train_final.empty:
             print("Chyba: Po rozdělení nezůstala žádná trénovací data.")
             return None, None
        print(f"Rozdělení dat pro finální trénink: {len(X_train_final)} trénovacích, {len(X_test_final)} testovacích vzorků.")
        scaler = MinMaxScaler(feature_range=(0, 1))
        print("Trénuji MinMaxScaler...")
        scaler.fit(X_train_final)
        print("MinMaxScaler natrénován.")
        print("Škáluji trénovací data...")
        X_train_scaled = scaler.transform(X_train_final)
        print("Trénovací data oškálována.")
        print("Trénuji XGBoost model...")
        model = xgb.XGBRegressor(**xgb_params)
        model.fit(X_train_scaled, y_train_final)
        print("XGBoost model natrénován.")
        return model, scaler
    except Exception as e:
        print(f"Chyba během trénování finálního modelu: {e}")
        return None, None

def predict_future(latest_data_raw: pd.DataFrame,
                   model: xgb.XGBRegressor,
                   scaler: MinMaxScaler,
                   horizon: int,
                   lags: list,
                   feature_names: list) -> float | None:
    """Predikuje budoucí cenu na základě nejnovějších surových dat."""
    print("Provádím predikci budoucí ceny...")
    if latest_data_raw.empty:
        print("Chyba: Prázdný DataFrame 'latest_data_raw' pro predikci.")
        return None

    required_rows = max(lags + [6]) + 5
    if len(latest_data_raw) < required_rows:
        print(f"Chyba: Nedostatek dat pro predikci. Potřeba alespoň {required_rows} řádků, máme {len(latest_data_raw)}.")
        return None

    try:
        df_predict = latest_data_raw.copy()
        print(f"Počáteční počet řádků: {len(df_predict)}")

        # Add technical indicators
        df_predict = add_technical_indicators(df_predict)
        print(f"Počet řádků po přidání indikátorů: {len(df_predict)}")

        # Add lagged features
        df_predict = add_lagged_features(df_predict, lags)
        print(f"Počet řádků po přidání lagů: {len(df_predict)}")

        # Ensure all NaN values are filled
        df_predict.ffill(inplace=True)
        df_predict.bfill(inplace=True)
        df_predict.fillna(0, inplace=True)  # Final fallback for any remaining NaNs

        # Verify required features
        missing_features = [f for f in feature_names if f not in df_predict.columns]
        if missing_features:
            print(f"Chybějící požadované příznaky: {missing_features}")
            return None

        # Get the last row for prediction
        last_row = df_predict.iloc[-1:]
        X_latest = last_row[feature_names]

        # Final verification
        if X_latest.isna().any().any():
            print("VAROVÁNÍ: Poslední řádek obsahuje NaN hodnoty!")
            print("Sloupce s NaN:", X_latest.columns[X_latest.isna().any()].tolist())
            return None

        # Scale and predict
        X_latest_scaled = scaler.transform(X_latest)
        prediction = model.predict(X_latest_scaled)[0]
        predicted_value = float(prediction)

        print(f"Predikce úspěšně provedena. Predikovaná hodnota: {predicted_value:.4f}")
        return predicted_value

    except Exception as e:
        print(f"Chyba během predikce: {e}")
        traceback.print_exc()
        return None

# --- 6. Hlavní Skript ---

if __name__ == "__main__":
    print(f"{Colors.HEADER}{'='*50}{Colors.ENDC}")
    print(f"{Colors.BOLD}ZAHÁJENÍ SKRIPTU PRO PREDIKCI CENY ALCHUSDT{Colors.ENDC}")
    print(f"{Colors.HEADER}{'='*50}{Colors.ENDC}")

    while True:
        try:
            conn = connect_db(conn_str)
            if conn is None:
                print("Nepodařilo se připojit k databázi. Čekám na další pokus...")
                time.sleep(60)
                continue

            create_ohlcv_table_if_not_exists(DB_TABLE_NAME, conn)
            create_predictions_table(conn)
            latest_timestamp_db = get_latest_timestamp_from_db(DB_TABLE_NAME, conn)

            current_time = datetime.now(timezone.utc)
            if latest_timestamp_db:
                start_download_dt = latest_timestamp_db + timedelta(minutes=1)
                start_download_str = start_download_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                start_download_str = (current_time - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')

            try:
                exchange = ccxt.binance({'enableRateLimit': True, 'options': {'defaultType': 'future'}})
                new_ohlcv_list = download_historical_data(SYMBOL, TIMEFRAME, start_download_str, exchange)

                if new_ohlcv_list:
                    new_data_df = format_ohlcv_data(new_ohlcv_list)
                    if new_data_df is not None and not new_data_df.empty:
                        save_dataframe_to_sql(new_data_df, DB_TABLE_NAME, conn)

                # Načtení dat a trénování modelu
                all_data_from_db = load_data_from_sql(DB_TABLE_NAME, conn, start_date=None)
                if all_data_from_db is not None and not all_data_from_db.empty:
                    X, y, feature_names = prepare_data(all_data_from_db, PREDICTION_HORIZON, LAG_FEATURES_LIST)
                    model, scaler = train_final_model(X, y, XGB_PARAMS)

                    if model is not None and scaler is not None:
                        num_rows_for_prediction = max(LAG_FEATURES_LIST + [26]) + 10
                        latest_data_raw = all_data_from_db.iloc[-num_rows_for_prediction:]
                        final_prediction = predict_future(latest_data_raw, model, scaler,
                                                       PREDICTION_HORIZON, LAG_FEATURES_LIST, feature_names)

                        if final_prediction is not None:
                            prediction_time = datetime.now(timezone.utc)
                            target_time = prediction_time + timedelta(minutes=PREDICTION_HORIZON)
                            save_prediction(conn, SYMBOL, prediction_time, target_time,
                                         final_prediction, PREDICTION_HORIZON)

                            # Aktualizace výsledků předchozích predikcí
                            update_prediction_results(conn)

                            # Výpis statistik s upravenými podmínkami
                            cursor = conn.cursor()
                            cursor.execute("""
                            WITH PredictionStats AS (
                                SELECT
                                    predicted_price,
                                    actual_price,
                                    prediction_time,
                                    LAG(predicted_price) OVER (ORDER BY prediction_time) as prev_prediction
                                FROM PREDICTIONS
                                WHERE symbol = ?
                                AND prediction_time >= DATEADD(hour, -24, GETUTCDATE())
                            )
                            SELECT
                                AVG(ABS(predicted_price - actual_price)) as avg_error,
                                COUNT(*) as total_predictions,
                                COUNT(CASE WHEN actual_price IS NOT NULL THEN 1 END) as completed_predictions,
                                AVG(CASE WHEN predicted_price > prev_prediction THEN 1
                                     WHEN predicted_price < prev_prediction THEN -1
                                     ELSE 0 END) as trend_direction,
                                MIN(predicted_price) as min_prediction,
                                MAX(predicted_price) as max_prediction
                            FROM PredictionStats
                            """, (SYMBOL,))

                            stats = cursor.fetchone()
                            print(f"\n{Colors.HEADER}Statistiky predikcí za posledních 24 hodin:{Colors.ENDC}")
                            print(f"{Colors.BOLD}Celkový přehled:{Colors.ENDC}")
                            print(f"• Celkem predikcí: {stats[1] if stats else 0}")
                            print(f"• Dokončených predikcí: {stats[2] if stats else 0}")

                            if stats and stats[0] is not None:
                                print(f"• Průměrná absolutní chyba: {Colors.YELLOW}{stats[0]:.4f}{Colors.ENDC} USDT")
                            else:
                                print(f"• Průměrná chyba: {Colors.YELLOW}Čekání na dokončení predikcí...{Colors.ENDC}")

                            if stats and stats[3] is not None:
                                trend = "↑ ROSTOUCÍ" if stats[3] > 0 else "↓ KLESAJÍCÍ" if stats[3] < 0 else "→ STABILNÍ"
                                trend_color = (Colors.GREEN if stats[3] > 0
                                            else Colors.RED if stats[3] < 0
                                            else Colors.BLUE)
                                print(f"• Trend predikcí: {trend_color}{trend}{Colors.ENDC}")
                                print(f"• Rozsah predikcí: {stats[4]:.4f} - {stats[5]:.4f} USDT")

                            # Zobrazení posledních 5 predikcí
                            cursor.execute("""
                            SELECT TOP 5
                                prediction_time,
                                target_time,
                                predicted_price,
                                actual_price,
                                ABS(ISNULL(actual_price - predicted_price, 0)) as error,
                                ISNULL(ABS((actual_price - predicted_price) / predicted_price * 100), 0) as prediction_error_pct,
                                LAG(predicted_price) OVER (ORDER BY prediction_time) as prev_prediction
                            FROM PREDICTIONS
                            WHERE symbol = ?
                            ORDER BY prediction_time DESC
                            """, (SYMBOL,))

                            print(f"\n{Colors.BOLD}Posledních 5 predikcí:{Colors.ENDC}")
                            print(f"{Colors.BLUE}{'Čas predikce':^12} | {'Cílový čas':^12} | {'Predikce':^10} | {'Skutečná':^10} | {'Chyba':^8} | {'Chyba %':^8} | {'Trend':^6}{Colors.ENDC}")
                            print("-" * 78)
                            for row in cursor.fetchall():
                                # Převedeme stringy na datetime objekty pokud je potřeba
                                pred_time_obj = row[0]
                                target_time_obj = row[1]

                                if isinstance(pred_time_obj, str):
                                    pred_time_obj = pd.to_datetime(pred_time_obj)
                                if isinstance(target_time_obj, str):
                                    target_time_obj = pd.to_datetime(target_time_obj)

                                pred_time = pred_time_obj.strftime('%H:%M:%S')
                                target_time = target_time_obj.strftime('%H:%M:%S')
                                pred_price = f"{row[2]:.4f}" if row[2] is not None else "N/A"
                                actual = f"{row[3]:.4f}" if row[3] is not None else "čeká"
                                error = f"{row[4]:.4f}" if row[4] is not None and row[3] is not None else "N/A"
                                error_pct = f"{row[5]:.2f}%" if row[5] is not None and row[3] is not None else "N/A"

                                # Přidání šipky trendu
                                if row[6] is not None:
                                    trend_arrow = ("↑" if row[2] > row[6] else
                                                "↓" if row[2] < row[6] else "→")
                                    trend_color = (Colors.GREEN if row[2] > row[6] else
                                                Colors.RED if row[2] < row[6] else
                                                Colors.BLUE)
                                else:
                                    trend_arrow = "•"
                                    trend_color = Colors.BLUE

                                print(f"{pred_time:^12} | {target_time:^12} | {Colors.YELLOW}{pred_price:^10}{Colors.ENDC} | "
                                    f"{actual:^10} | {error:^8} | {error_pct:^8} | {trend_color}{trend_arrow:^6}{Colors.ENDC}")

            except Exception as e:
                print(f"Chyba v hlavní smyčce: {e}")
                traceback.print_exc()

            finally:
                if conn:
                    conn.close()

            # Čekání 30 sekund do další predikce
            time.sleep(30)

        except KeyboardInterrupt:
            print("\nUkončení skriptu na žádost uživatele.")
            break
        except Exception as e:
            print(f"Kritická chyba: {e}")
            traceback.print_exc()
            time.sleep(30)  # Počkat před dalším pokusem

    print("Skript ukončen.")
