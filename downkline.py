import time
import pyodbc
from datetime import datetime, timedelta
from pybit.unified_trading import HTTP
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
import pandas as pd

# API a databazove nastaveni
api_key = "WiC6jz757MgJvviWkC"
api_secret = "fqbslWfRQmVWkU69XbiVCMp7q79no0Zr4JFj"
symbol = "ALCHUSDT"
session = HTTP(testnet=False, api_key=api_key, api_secret=api_secret, recv_window=20000)

# Připojení k databázi
conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************,1433;"  # Například "*************\\SQLEXPRESS"
    "DATABASE=byb;"
    "UID=dbuser;"
    "PWD=pochy1249;"
    "TrustServerCertificate=yes;"
    "Encrypt=no;"
)

def connect_to_db():
    conn_str = (
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=*************,1433;"  # Například "*************\\SQLEXPRESS"
        "DATABASE=byb;"
        "UID=dbuser;"
        "PWD=pochy1249;"
        "TrustServerCertificate=yes;"
        "Encrypt=no;"
    )
    return pyodbc.connect(conn_str)


def ensure_kline_table(cursor):
    cursor.execute(
        """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'kline_data')
        BEGIN
            CREATE TABLE kline_data (
                timestamp DATETIME NOT NULL,
                symbol NVARCHAR(20) NOT NULL,
                open_price FLOAT,
                high_price FLOAT,
                low_price FLOAT,
                close_price FLOAT,
                volume FLOAT,
                turnover FLOAT,
                PRIMARY KEY (timestamp, symbol)
            )
        END
    """
    )
    cursor.commit()


def get_specific_symbols():
    # Vrací pouze požadované symboly místo načítání všech USDT perpetual
    symbols = ["ALCHUSDT", "BABYUSDT", "JELLYJELLYUSDT", "MUBARAKUSDT"]
    print(f"✓ Použití {len(symbols)} specifických symbolů: {', '.join(symbols)}")
    return symbols


def fetch_and_save_latest_kline(symbol):
    now = int(time.time() * 1000)
    one_minute_ago = now - 2 * 60 * 1000
    conn = connect_to_db()
    cursor = conn.cursor()

    try:
        response = session.get_kline(
            category="linear",
            symbol=symbol,
            interval="1",
            start=one_minute_ago,
            end=now,
            limit=1,
        )

        if response["retCode"] == 0 and response["result"]["list"]:
            k = response["result"]["list"][0]
            ts = datetime.fromtimestamp(int(k[0]) / 1000)
            cursor.execute(
                """
                IF NOT EXISTS (SELECT 1 FROM kline_data WHERE timestamp = ? AND symbol = ?)
                BEGIN
                    INSERT INTO kline_data (
                        timestamp, symbol, open_price, high_price, low_price,
                        close_price, volume, turnover
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                END
            """,
                (
                    ts,
                    symbol,
                    ts,
                    symbol,
                    float(k[1]),
                    float(k[2]),
                    float(k[3]),
                    float(k[4]),
                    float(k[5]),
                    float(k[6]),
                ),
            )
            conn.commit()
            print(f"[✓] {symbol} - {ts} ulozeno")
            with open("kline_debug.log", "a") as f:
                f.write(f"[{datetime.now()}] Ulozeno: {symbol} - {ts}\n")
        else:
            print(f"[-] {symbol} - zadna data")
    except Exception as e:
        print(f"✗ Chyba pri zpracovani {symbol}: {e}")
        with open("kline_debug.log", "a") as f:
            f.write(f"[{datetime.now()}] CHYBA {symbol}: {e}\n")
    finally:
        cursor.close()
        conn.close()


def fetch_historical_klines(symbol, days=14):
    """Stáhne historické svíčky pro výpočet indikátorů"""
    now = int(time.time() * 1000)
    start_time = now - (days * 24 * 60 * 60 * 1000)  # X dní zpět
    conn = connect_to_db()
    cursor = conn.cursor()

    try:
        print(f"Stahuji historické svíčky pro {symbol} (posledních {days} dní)...")

        # Stahujeme po částech, protože API má limit na počet svíček
        current_start = start_time
        while current_start < now:
            current_end = min(
                current_start + (1000 * 60 * 1000), now
            )  # Max 1000 minut nebo do současnosti

            response = session.get_kline(
                category="linear",
                symbol=symbol,
                interval="1",  # 1-minutové svíčky
                start=current_start,
                end=current_end,
                limit=1000,
            )

            if response["retCode"] == 0 and response["result"]["list"]:
                klines = response["result"]["list"]
                for k in klines:
                    ts = datetime.fromtimestamp(int(k[0]) / 1000)
                    cursor.execute(
                        """
                        IF NOT EXISTS (SELECT 1 FROM kline_data WHERE timestamp = ? AND symbol = ?)
                        BEGIN
                            INSERT INTO kline_data (
                                timestamp, symbol, open_price, high_price, low_price,
                                close_price, volume, turnover
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        END
                    """,
                        (
                            ts,
                            symbol,
                            ts,
                            symbol,
                            float(k[1]),
                            float(k[2]),
                            float(k[3]),
                            float(k[4]),
                            float(k[5]),
                            float(k[6]),
                        ),
                    )
                conn.commit()
                print(f"[✓] {symbol} - uloženo {len(klines)} historických svíček")
            else:
                print(f"[-] {symbol} - žádná historická data pro interval")

            # Posuneme se na další časový úsek
            current_start = current_end
            time.sleep(1)  # Pauza mezi požadavky, abychom nepřekročili rate limit

    except Exception as e:
        print(f"✗ Chyba při stahování historických dat pro {symbol}: {e}")
        with open("kline_debug.log", "a") as f:
            f.write(f"[{datetime.now()}] CHYBA historie {symbol}: {e}\n")
    finally:
        cursor.close()
        conn.close()


def stahni_minutove_svicky():
    """
    Funkce pro stažení minutových svíček za posledních 7 dní pomocí Bybit API
    """
    try:
        # Připojení k databázi
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Zajištění existence tabulky alch_price_history
        cursor.execute(
            """
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'alch_price_history')
            BEGIN
                CREATE TABLE alch_price_history (
                    timestamp DATETIME NOT NULL,
                    open_price FLOAT,
                    high_price FLOAT,
                    low_price FLOAT,
                    close_price FLOAT,
                    volume FLOAT,
                    PRIMARY KEY (timestamp)
                )
            END
            """
        )
        conn.commit()
        
        # Získání časového rozsahu - posledních 7 dní
        konec_dne = datetime.now()
        zacatek = konec_dne - timedelta(days=1)
        
        print(f"Stahuji minutové svíčky za posledních 7 dní")
        print(f"Časový rozsah: {zacatek} až {konec_dne}")
        
        # Formátování času pro API (Bybit používá milisekundy)
        zacatek_timestamp = int(zacatek.timestamp()) * 1000
        konec_timestamp = int(konec_dne.timestamp()) * 1000
        
        # Vymazání starých dat za daný časový rozsah
        delete_query = """
        DELETE FROM alch_price_history 
        WHERE timestamp >= ? AND timestamp <= ?
        """
        cursor.execute(delete_query, (zacatek, konec_dne))
        conn.commit()
        print("Vymazány staré svíčky za posledních 7 dní")
        
        # Stahujeme data po částech kvůli limitům API
        current_start = zacatek_timestamp
        celkem_vlozeno = 0
        
        while current_start < konec_timestamp:
            current_end = min(current_start + (1000 * 60 * 1000), konec_timestamp)  # Max 1000 minut
            
            # Použití Bybit API pomocí existující session
            response = session.get_kline(
                category="linear",
                symbol=symbol,  # Použití symbolu definovaného na začátku souboru
                interval="1",   # 1-minutové svíčky
                start=current_start,
                end=current_end,
                limit=1000,
            )
            
            if response["retCode"] == 0 and response["result"]["list"]:
                klines = response["result"]["list"]
                print(f"Staženo {len(klines)} minutových svíček pro interval {datetime.fromtimestamp(current_start/1000)} - {datetime.fromtimestamp(current_end/1000)}")
                
                # Příprava nových dat
                count = 0
                for k in klines:
                    timestamp = datetime.fromtimestamp(int(k[0]) / 1000)
                    
                    # Vložení do databáze
                    insert_query = """
                    INSERT INTO alch_price_history (timestamp, open_price, high_price, low_price, close_price, volume)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """
                    cursor.execute(
                        insert_query, 
                        (
                            timestamp,
                            float(k[1]),  # open
                            float(k[2]),  # high
                            float(k[3]),  # low
                            float(k[4]),  # close
                            float(k[5]),  # volume
                        )
                    )
                    count += 1
                    
                    # Commit po každých 100 záznamech
                    if count % 100 == 0:
                        conn.commit()
                        print(f"Vloženo {count} záznamů...")
                
                conn.commit()
                celkem_vlozeno += count
                print(f"Vloženo {count} svíček pro aktuální interval")
            else:
                print(f"Žádná data pro interval {datetime.fromtimestamp(current_start/1000)} - {datetime.fromtimestamp(current_end/1000)}")
            
            # Posun na další časový úsek
            current_start = current_end
            time.sleep(1)  # Pauza mezi požadavky
        
        print(f"Celkem vloženo {celkem_vlozeno} nových minutových svíček za posledních 7 dní")
    
    except Exception as e:
        print(f"CHYBA při stahování minutových svíček: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        print("Stahování minut dokončeno")


def main():
    print("Spoustim sber 1min svicek pro specifické symboly...")
    conn = connect_to_db()
    cursor = conn.cursor()
    ensure_kline_table(cursor)
    cursor.close()
    conn.close()

    symbols = get_specific_symbols()
    if not symbols:
        print("✗ Nenasly se zadne symboly, ukoncuji skript")
        return

    # Nejprve stáhneme historická data pro výpočet indikátorů
    print("Stahuji historická data pro výpočet indikátorů...")
    for symbol in symbols:
        fetch_historical_klines(symbol, days=14)  # 14 dní pro RSI a STOCH

    print("Historická data stažena, pokračuji s aktuálními daty...")

    start_time = time.time()
    duration_limit = 1200  # 2 minuty
    total_iterations = 0

    while time.time() - start_time < duration_limit:
        print(
            f"\n[{datetime.now().strftime('%H:%M:%S')}] Zacinam iteraci pro {len(symbols)} symbolu"
        )
        with ThreadPoolExecutor(
            max_workers=4
        ) as executor:  # Sníženo na 4 pro méně symbolů
            futures = [
                executor.submit(fetch_and_save_latest_kline, symbol)
                for symbol in symbols
            ]
            for future in as_completed(futures):
                pass
        total_iterations += 1
        print(
            f"[{datetime.now().strftime('%H:%M:%S')}] Iterace {total_iterations} dokoncena, cekam 2s\n"
        )
        time.sleep(2)

    print(f"\n⏱️ Hotovo. Pocet iteraci za 2 minuty: {total_iterations}")
    print(
        f"✓ Skript ulozil data pro ~{total_iterations * len(symbols)} pokusu o svicku"
    )

    print("Pro výpočet RSI a STOCH indikátorů spusťte calculate_indicators.asp")


if __name__ == "__main__":
    stahni_minutove_svicky()
